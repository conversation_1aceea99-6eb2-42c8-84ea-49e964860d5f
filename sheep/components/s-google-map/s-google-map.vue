<!-- 装修图文组件：谷歌地图 -->
<template>
  <view class="google-map-container" :style="containerStyle">
    <!-- 标题和工具栏同行 -->
    <view v-if="data.showTitle" class="map-header">
      <view class="map-title" :style="{ color: styles.titleColor }">
        {{ data.title }}
      </view>
<!--      <view class="fullscreen-btn" @tap="openFullscreen">-->
<!--        <text class="fullscreen-icon">📍</text>-->
<!--        <text class="fullscreen-text">全屏</text>-->
<!--      </view>-->
    </view>

    <!-- 如果没有标题但需要显示全屏按钮 -->
<!--    <view v-if="!data.showTitle" class="map-toolbar">-->
<!--      <view class="fullscreen-btn" @tap="openFullscreen">-->
<!--        <text class="fullscreen-icon">📍</text>-->
<!--        <text class="fullscreen-text">全屏</text>-->
<!--      </view>-->
<!--    </view>-->

    <view v-if="data.showLocationDesc" class="map-desc" :style="{ color: styles.descColor }">
      {{ data.locationDesc }}
    </view>

    <view class="map-container" :style="{ height: data.height + 'px' }" id="googleMapRef"></view>
    
    <!-- 站点信息弹窗 -->
    <su-popup ref="stationPopup" type="center" :show="showStationPopup" @close="closeStationInfo">
      <view class="station-info-popup" v-if="selectedStation">
        <view class="popup-header">
          <text class="popup-title">站点详情</text>
          <text class="popup-close" @tap="closeStationInfo">×</text>
        </view>
                 <view class="popup-content">
           <view class="station-name">{{ selectedStation.siteName }}</view>
           <view class="station-address">{{ selectedStation.address }}</view>
           <view class="station-status">
             <text class="status-label">状态：</text>
             <text :class="['status-value', getStatusClass(selectedStation.status)]">
               {{ getStatusText(selectedStation.status) }}
             </text>
           </view>
           <view class="station-distance" v-if="selectedStation.distance">
             <text class="distance-label">距离：</text>
             <text class="distance-value">{{ (selectedStation.distance / 1000).toFixed(1) }}km</text>
           </view>
           <view class="station-info" v-if="selectedStation.replaceCarCtn !== undefined">
             <text class="info-label">可换车辆：</text>
             <text class="info-value">{{ selectedStation.replaceCarCtn }}辆</text>
           </view>
           <view class="station-price" v-if="selectedStation.nowPrice">
             <text class="price-label">当前价格：</text>
             <text class="price-value">¥{{ selectedStation.nowPrice }}/度</text>
           </view>
         </view>
        <view class="popup-footer">
          <button class="popup-btn cancel-btn" @tap="closeStationInfo">关闭</button>
          <button class="popup-btn primary-btn" @tap="navigateToStation">导航</button>
        </view>
      </view>
    </su-popup>

    <!-- 全屏地图弹窗 -->
    <su-popup ref="fullscreenPopup" type="center" :show="showFullscreenPopup" @close="closeFullscreen" :mask-click="false">
      <view class="fullscreen-map-container">
        <view class="fullscreen-header">
          <text class="fullscreen-title">地图全览</text>
          <view class="close-btn" @tap="closeFullscreen">
            <text class="close-icon">×</text>
          </view>
        </view>
        <view class="fullscreen-map" id="fullscreenMapRef"></view>
      </view>
    </su-popup>
  </view>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import sheep from '@/sheep'
import SiteApi from '@/sheep/api/site/site'
// 标记聚合器变量（动态加载）
let MarkerClusterer = null
let SuperClusterAlgorithm = null
let markerClusterer = null

// InfoWindow相关变量
let currentInfoWindow = null
let fullscreenInfoWindow = null

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
  styles: {
    type: Object,
    default: () => ({}),
  },
})

const containerStyle = {
  backgroundColor: props.styles.bgColor,
  borderRadius: (props.styles.borderRadius || 0) + 'px',
}

let map = null
let marker = null
const stationPopup = ref(null)
const selectedStation = ref(null)
const showStationPopup = ref(false)
const stationMarkers = []

// 全屏相关状态
const fullscreenPopup = ref(null)
const showFullscreenPopup = ref(false)
let fullscreenMap = null
const fullscreenStationMarkers = []
let fullscreenMarkerClusterer = null

// 检测是否支持现代API
const useAdvancedMarkers = () => {
  return typeof window !== 'undefined' &&
         window.google &&
         window.google.maps &&
         window.google.maps.marker &&
         window.google.maps.marker.AdvancedMarkerElement
}

// 创建专业的换电站图标
const createStationIcon = (station) => {
  const iconElement = document.createElement('div')
  iconElement.className = 'station-marker'
  iconElement.style.cursor = 'pointer'
  iconElement.title = station.siteName

  const getIconColor = (status) => {
    switch (status) {
      case 1: return '#4CAF50' // 正常运营
      case 2: return '#FF9800' // 维护中
      case 3: return '#F44336' // 故障
      default: return '#9E9E9E' // 未知状态
    }
  }

  iconElement.innerHTML = `
    <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
      <circle cx="16" cy="16" r="14" fill="${getIconColor(station.status)}" stroke="#fff" stroke-width="2"/>
      <path d="M12 10h8v4h-2v6h-4v-6h-2z" fill="#fff"/>
      <circle cx="16" cy="22" r="2" fill="#fff"/>
    </svg>
  `
  return iconElement
}

// 创建聚合图标
const createClusterIcon = (count) => {
  const clusterElement = document.createElement('div')
  clusterElement.className = 'cluster-marker'
  clusterElement.innerHTML = `
    <div style="
      background: #1976D2;
      color: white;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 14px;
      border: 2px solid white;
      box-shadow: 0 2px 6px rgba(0,0,0,0.3);
      cursor: pointer;
    ">${count}</div>
  `
  return clusterElement
}

// 获取站点图标URL（从DiyEditor配置中读取）
const getIconUrl = (status) => {
  // 优先使用DiyEditor配置的图标
  if (props.data.stationIcons) {
    switch (status) {
      case 1: return props.data.stationIcons.online || props.data.stationIcons.normal || 'https://maps.google.com/mapfiles/ms/icons/green-dot.png'
      case 0: return props.data.stationIcons.offline || props.data.stationIcons.fault || 'https://maps.google.com/mapfiles/ms/icons/red-dot.png'
      case 2: return props.data.stationIcons.busy || props.data.stationIcons.maintenance || 'https://maps.google.com/mapfiles/ms/icons/yellow-dot.png'
      case 3: return props.data.stationIcons.fault || props.data.stationIcons.offline || 'https://maps.google.com/mapfiles/ms/icons/red-dot.png'
      default: return props.data.stationIcons.online || props.data.stationIcons.normal || 'https://maps.google.com/mapfiles/ms/icons/green-dot.png'
    }
  }

  // 兼容旧版本单一图标配置
  if (props.data.stationIcon) {
    return props.data.stationIcon
  }

  // 降级到默认图标
  switch (status) {
    case 1: return 'https://maps.google.com/mapfiles/ms/icons/green-dot.png' // 正常运营
    case 0: return 'https://maps.google.com/mapfiles/ms/icons/red-dot.png' // 离线
    case 2: return 'https://maps.google.com/mapfiles/ms/icons/yellow-dot.png' // 繁忙
    case 3: return 'https://maps.google.com/mapfiles/ms/icons/red-dot.png' // 故障
    default: return 'https://maps.google.com/mapfiles/ms/icons/green-dot.png'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 1: return '正常运营'
    case 2: return '维护中'
    case 3: return '故障'
    case 0: return '离线'
    default: return '未知状态'
  }
}

// 获取状态颜色
const getStatusColor = (status) => {
  switch (status) {
    case 1: return '#4CAF50' // 绿色
    case 2: return '#FF9800' // 橙色
    case 3: return '#F44336' // 红色
    case 0: return '#F44336' // 红色
    default: return '#9E9E9E' // 灰色
  }
}

// 创建原生InfoWindow内容
const createInfoWindowContent = (station) => {
  const statusText = getStatusText(station.status)
  const statusColor = getStatusColor(station.status)

  return `
    <div class="station-info-window">
      <div class="station-header">
        <h3 class="station-name">${station.siteName}</h3>
        <div class="station-status" style="color: ${statusColor};">
          <span class="status-dot" style="background-color: ${statusColor};"></span>
          ${statusText}
        </div>
      </div>

      <div class="station-details">
        <div class="detail-item">
          <span class="detail-icon">📍</span>
          <span class="detail-text">${station.address}</span>
        </div>

        ${station.distance ? `
        <div class="detail-item">
          <span class="detail-icon">📏</span>
          <span class="detail-text">距离 ${station.distance}m</span>
        </div>
        ` : ''}

        ${station.replaceCarCtn !== undefined ? `
        <div class="detail-item">
          <span class="detail-icon">🔋</span>
          <span class="detail-text">可用电池 ${station.replaceCarCtn}个</span>
        </div>
        ` : ''}

        ${station.nowPrice ? `
        <div class="detail-item">
          <span class="detail-icon">💰</span>
          <span class="detail-text">当前电价 ¥${station.nowPrice}/kWh</span>
        </div>
        ` : ''}
      </div>

      <div class="station-actions">
        <button class="action-btn primary" onclick="navigateToStation(${station.latitude}, ${station.longitude})">
          导航
        </button>
      </div>
    </div>
  `
}

// 动态加载聚合库
const loadMarkerClusterer = async () => {
  try {
    // 在H5环境中尝试动态导入
    if (typeof window !== 'undefined' && window.google && !MarkerClusterer) {
      // 这里可以通过CDN或其他方式加载聚合库
      // 由于uni-app环境限制，我们先使用简化版本
      console.log('MarkerClusterer loading skipped in uni-app environment')
    }
  } catch (error) {
    console.warn('MarkerClusterer not available:', error)
  }
}

// 添加InfoWindow样式到页面
const addInfoWindowStyles = () => {
  const styleId = 'google-maps-infowindow-styles'
  if (!document.getElementById(styleId)) {
    const styleElement = document.createElement('style')
    styleElement.id = styleId
    styleElement.innerHTML = `
      .station-info-window {
        font-family: 'Roboto', 'Arial', 'PingFang SC', 'Microsoft YaHei', sans-serif;
        font-size: 14px;
        line-height: 1.5;
        color: #333;
        max-width: 320px;
        padding: 0;
        margin: 0;
        background: #fff;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(0,0,0,0.12);
      }

      .station-header {
        padding: 20px 20px 16px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #fff;
        position: relative;
      }

      .station-header::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #4CAF50, #2196F3, #FF9800, #E91E63);
      }

      .station-name {
        margin: 0 0 8px 0;
        font-size: 18px;
        font-weight: 600;
        color: #fff;
        line-height: 1.3;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }

      .station-status {
        display: flex;
        align-items: center;
        font-size: 13px;
        font-weight: 500;
        color: rgba(255,255,255,0.9);
      }

      .status-dot {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 8px;
        display: inline-block;
        border: 2px solid rgba(255,255,255,0.8);
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
      }

      .station-details {
        padding: 16px 20px;
        background: #fff;
      }

      .detail-item {
        display: flex;
        align-items: center;
        margin: 12px 0;
        font-size: 14px;
        padding: 8px 12px;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #667eea;
        transition: all 0.2s ease;
      }

      .detail-item:hover {
        background: #e3f2fd;
        transform: translateX(4px);
      }

      .detail-icon {
        width: 20px;
        margin-right: 12px;
        text-align: center;
        font-size: 16px;
      }

      .detail-text {
        flex: 1;
        color: #424242;
        font-weight: 500;
      }

      .station-actions {
        padding: 8px 16px 12px;
        display: flex;
        gap: 8px;
        border-top: 1px solid #e0e0e0;
      }

      .action-btn {

      }

      .action-btn:hover {
        background: #f8f9fa;
        border-color: #1a73e8;
      }

      .action-btn.primary {
        background: #1a73e8;
        color: #fff;
        border-color: #1a73e8;
      }

      .action-btn.primary:hover {
        background: #1557b0;
      }

      .btn-icon {
        margin-right: 8px;
        font-size: 16px;
        transition: transform 0.3s ease;
      }

      .action-btn:hover .btn-icon {
        transform: scale(1.2) rotate(5deg);
      }

      .gm-style .gm-style-iw-c {
        padding: 0;
      }

      .gm-style .gm-style-iw-d {
        overflow: hidden !important;
      }

      /* InfoWindow默认样式 */
      .gm-style .gm-style-iw-c {
        padding: 0;
      }

      .gm-style .gm-style-iw-d {
        overflow: hidden !important;
      }
    `
    document.head.appendChild(styleElement)
  }
}

// 全局导航函数
if (typeof window !== 'undefined') {
  window.navigateToStation = (lat, lng) => {
    console.log('导航到站点:', lat, lng)

    // 检测是否在uni-app环境中
    if (typeof uni !== 'undefined' && uni.openLocation) {
      // 在uni-app中使用原生地图导航
      uni.openLocation({
        latitude: lat,
        longitude: lng,
        scale: 18,
        name: '换电站',
        address: '换电站位置',
        success: () => {
          console.log('打开原生地图成功')
        },
        fail: (err) => {
          console.error('打开原生地图失败:', err)
          // 降级到Google Maps
          openGoogleMapsNavigation(lat, lng)
        }
      })
    } else {
      // 在H5环境中直接使用Google Maps
      openGoogleMapsNavigation(lat, lng)
    }
  }

  // Google Maps导航函数
  function openGoogleMapsNavigation(lat, lng) {
    const url = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`
    console.log('打开Google Maps导航:', url)

    // 尝试打开Google Maps应用（移动端）
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)

    if (isMobile) {
      // 移动端尝试打开Google Maps应用
      const googleMapsApp = `comgooglemaps://?daddr=${lat},${lng}&directionsmode=driving`
      const appleMapApp = `http://maps.apple.com/?daddr=${lat},${lng}`

      // 创建一个隐藏的链接来尝试打开应用
      const link = document.createElement('a')
      link.href = googleMapsApp
      link.style.display = 'none'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // 如果应用没有打开，延迟后打开网页版
      setTimeout(() => {
        window.open(url, '_blank')
      }, 1000)
    } else {
      // 桌面端直接打开网页版
      window.open(url, '_blank')
    }
  }
}

// 加载谷歌地图API
const loadGoogleMapAPI = () => {
  return new Promise((resolve, reject) => {
    if (typeof window.google !== 'undefined' && window.google.maps) {
      resolve()
      return
    }
    const script = document.createElement('script')
    script.src = 'https://maps.googleapis.com/maps/api/js?key=AIzaSyArla_C7JZ6kpEFq9ojEK4YRFg6h8uhoQA&libraries=marker,geometry&callback=initGoogleMap'
    script.async = true
    script.defer = true
    window.initGoogleMap = () => resolve()
    script.onerror = () => reject(new Error('Google Maps API加载失败'))
    document.head.appendChild(script)
  })
}

// 获取站点列表
const getStationList = async () => {
  try {
    const { code, data } = await SiteApi.getSiteList({
      longitude: props.data.currentLocation?.lng || props.data.lng,
      latitude: props.data.currentLocation?.lat || props.data.lat,
      keyWord: ''
    })
    
    if (code === 0 && data && data.length > 0) {
      // 转换后端数据格式为前端期望的格式
      return data.map(station => ({
        siteNo: station.siteNo,
        siteName: station.siteName,
        address: station.siteAdress || station.address,
        latitude: station.latitude || station.lat,
        longitude: station.longitude || station.lng,
        status: station.siteStatus || station.status,
        distance: station.distance,
        // 其他字段
        city: station.city,
        enabled: station.enabled,
        replaceCarCtn: station.replaceCarCtn,
        lowPrice: station.lowPrice,
        normalPrice: station.normalPrice,
        peakPrice: station.peakPrice,
        nowPrice: station.nowPrice
      }))
    }
    
    // 如果后端数据为空，返回示例数据用于演示
    console.log('后端数据为空，使用示例数据进行演示')
    return getDemoStationData()
  } catch (error) {
    console.error('获取站点列表失败:', error)
    // 发生错误时也返回示例数据
    console.log('API调用失败，使用示例数据进行演示')
    return getDemoStationData()
  }
}

// 获取示例站点数据
const getDemoStationData = () => {
  return [
    {
      siteNo: 'DEMO001',
      siteName: '示例换电站1',
      address: '上海市浦东新区张江高科技园区',
      latitude: 31.23,
      longitude: 121.5,
      status: 1,
      distance: 1200,
      city: '上海',
      enabled: true,
      replaceCarCtn: 15,
      lowPrice: 0.8,
      normalPrice: 1.2,
      peakPrice: 1.8,
      nowPrice: 1.2
    },
    {
      siteNo: 'DEMO002',
      siteName: '示例换电站2',
      address: '上海市徐汇区漕河泾开发区',
      latitude: 31.18,
      longitude: 121.43,
      status: 2,
      distance: 2500,
      city: '上海',
      enabled: true,
      replaceCarCtn: 8,
      lowPrice: 0.9,
      normalPrice: 1.3,
      peakPrice: 1.9,
      nowPrice: 1.3
    },
    {
      siteNo: 'DEMO003',
      siteName: '示例换电站3',
      address: '上海市黄浦区外滩金融中心',
      latitude: 31.24,
      longitude: 121.49,
      status: 0,
      distance: 3800,
      city: '上海',
      enabled: false,
      replaceCarCtn: 0,
      lowPrice: 0.7,
      normalPrice: 1.1,
      peakPrice: 1.7,
      nowPrice: 1.1
    },
    {
      siteNo: 'DEMO004',
      siteName: '示例换电站4',
      address: '上海市静安区南京西路商圈',
      latitude: 31.22,
      longitude: 121.46,
      status: 1,
      distance: 1800,
      city: '上海',
      enabled: true,
      replaceCarCtn: 12,
      lowPrice: 0.85,
      normalPrice: 1.25,
      peakPrice: 1.85,
      nowPrice: 1.25
    },
    {
      siteNo: 'DEMO005',
      siteName: '示例换电站5',
      address: '上海市长宁区虹桥商务区',
      latitude: 31.20,
      longitude: 121.40,
      status: 1,
      distance: 3200,
      city: '上海',
      enabled: true,
      replaceCarCtn: 20,
      lowPrice: 0.75,
      normalPrice: 1.15,
      peakPrice: 1.75,
      nowPrice: 1.15
    }
  ]
}


// 创建标记（支持现代API和降级）
const createMarker = (station) => {
  if (useAdvancedMarkers()) {
    // 使用现代AdvancedMarkerElement API
    return new window.google.maps.marker.AdvancedMarkerElement({
      map: map,
      position: { lat: station.latitude, lng: station.longitude },
      title: station.siteName,
      content: createStationIcon(station)
    })
  } else {
    // 降级到传统Marker API
    return new window.google.maps.Marker({
      position: { lat: station.latitude, lng: station.longitude },
      map: map,
      title: station.siteName,
      icon: {
        url: getIconUrl(station.status),
        scaledSize: new window.google.maps.Size(
          props.data.stationIconSize?.width || 32,
          props.data.stationIconSize?.height || 32
        ),
        anchor: new window.google.maps.Point(
          props.data.stationIconAnchor?.x || 16,
          props.data.stationIconAnchor?.y || 32
        )
      }
    })
  }
}

// 渲染站点标记（优化版本）
const renderStationMarkers = (stations) => {
  // 清除现有标记和聚合器
  if (markerClusterer) {
    markerClusterer.clearMarkers && markerClusterer.clearMarkers()
    markerClusterer = null
  }
  stationMarkers.forEach(marker => {
    if (marker.map) {
      marker.map = null
    } else if (marker.setMap) {
      marker.setMap(null)
    }
  })
  stationMarkers.length = 0

  if (!stations.length) return

  // 创建新标记
  stations.forEach(station => {
    const marker = createMarker(station)

    // 添加点击事件 - 使用原生InfoWindow
    marker.addListener('click', () => {
      showStationInfoWindow(station, marker)
    })

    stationMarkers.push(marker)
  })

  // 简化版聚合：当标记数量较多时，可以考虑添加聚合逻辑
  // 由于uni-app环境限制，这里暂时跳过复杂的聚合实现

  // 自动调整视野
  if (props.data.autoCenter && stations.length > 0) {
    const bounds = new window.google.maps.LatLngBounds()
    stations.forEach(station => {
      bounds.extend({ lat: station.latitude, lng: station.longitude })
    })
    map.fitBounds(bounds)
  }
}



// 显示站点信息
const showStationInfo = (station) => {
  if (!props.data.showStationInfo) return
  
  selectedStation.value = station
  showStationPopup.value = true
}

// 关闭站点信息
const closeStationInfo = () => {
  showStationPopup.value = false
  selectedStation.value = null
}

// 获取状态样式类
const getStatusClass = (status) => {
  switch (status) {
    case 0: return 'offline'
    case 1: return 'online'
    case 2: return 'busy'
    default: return 'offline'
  }
}

// 导航到站点
const navigateToStation = () => {
  if (!selectedStation.value) return

  const { latitude, longitude } = selectedStation.value
  // 使用uni-app的导航功能
  uni.openLocation({
    latitude: latitude,
    longitude: longitude,
    name: selectedStation.value.siteName,
    address: selectedStation.value.address,
    success: () => {
      closeStationInfo()
    }
  })
}

// // 打开全屏模式
// const openFullscreen = () => {
//   showFullscreenPopup.value = true
//   nextTick(() => {
//     initFullscreenMap()
//   })
// }
//
// // 关闭全屏模式
// const closeFullscreen = () => {
//   showFullscreenPopup.value = false
//   destroyFullscreenMap()
// }
//
// // 初始化全屏地图
// const initFullscreenMap = () => {
//   const fullscreenMapRef = document.getElementById('fullscreenMapRef')
//   if (!fullscreenMapRef || !window.google?.maps) return
//
//   // 创建全屏地图实例
//   fullscreenMap = new window.google.maps.Map(fullscreenMapRef, {
//     center: map ? map.getCenter() : { lat: props.data.lat, lng: props.data.lng },
//     zoom: map ? map.getZoom() : props.data.zoom,
//     disableDefaultUI: false,
//     zoomControl: true,
//     fullscreenControl: true,
//     streetViewControl: true,
//     mapTypeControl: true,
//     mapId: 'DEMO_MAP_ID'
//   })
//
//   // 添加InfoWindow样式
//   addInfoWindowStyles()
//
//   // 移除全屏地图缩放事件监听器 - 使用固定大小图标
//
//   // 如果启用换电站显示，加载站点数据
//   if (props.data.showStations) {
//     loadFullscreenStationMarkers()
//   }
// }

// 加载全屏地图站点标记
const loadFullscreenStationMarkers = async () => {
  try {
    const stations = await getStationList()
    renderFullscreenStationMarkers(stations)
  } catch (error) {
    console.error('加载全屏地图站点数据失败:', error)
  }
}

// 创建全屏地图标记
const createFullscreenMarker = (station) => {
  if (useAdvancedMarkers()) {
    // 使用现代AdvancedMarkerElement API
    return new window.google.maps.marker.AdvancedMarkerElement({
      map: fullscreenMap,
      position: { lat: station.latitude, lng: station.longitude },
      title: station.siteName,
      content: createStationIcon(station)
    })
  } else {
    // 降级到传统Marker API
    return new window.google.maps.Marker({
      position: { lat: station.latitude, lng: station.longitude },
      map: fullscreenMap,
      title: station.siteName,
      icon: {
        url: getIconUrl(station.status),
        scaledSize: new window.google.maps.Size(
          props.data.stationIconSize?.width || 32,
          props.data.stationIconSize?.height || 32
        ),
        anchor: new window.google.maps.Point(
          props.data.stationIconAnchor?.x || 16,
          props.data.stationIconAnchor?.y || 32
        )
      }
    })
  }
}

// 渲染全屏地图站点标记（优化版本）
const renderFullscreenStationMarkers = (stations) => {
  // 清除现有标记和聚合器
  if (fullscreenMarkerClusterer) {
    fullscreenMarkerClusterer.clearMarkers && fullscreenMarkerClusterer.clearMarkers()
    fullscreenMarkerClusterer = null
  }
  fullscreenStationMarkers.forEach(marker => {
    if (marker.map) {
      marker.map = null
    } else if (marker.setMap) {
      marker.setMap(null)
    }
  })
  fullscreenStationMarkers.length = 0

  if (!stations.length) return

  // 创建新标记
  stations.forEach(station => {
    const marker = createFullscreenMarker(station)

    // 添加点击事件 - 使用原生InfoWindow
    marker.addListener('click', () => {
      showFullscreenStationInfoWindow(station, marker)
    })

    fullscreenStationMarkers.push(marker)
  })

  // 自动调整视野
  if (stations.length > 0) {
    const bounds = new window.google.maps.LatLngBounds()
    stations.forEach(station => {
      bounds.extend({ lat: station.latitude, lng: station.longitude })
    })
    fullscreenMap.fitBounds(bounds)
  }
}



// 销毁全屏地图
const destroyFullscreenMap = () => {
  if (fullscreenMarkerClusterer) {
    fullscreenMarkerClusterer.clearMarkers && fullscreenMarkerClusterer.clearMarkers()
    fullscreenMarkerClusterer = null
  }
  if (fullscreenMap) {
    fullscreenStationMarkers.forEach(marker => {
      if (marker.map) {
        marker.map = null
      } else if (marker.setMap) {
        marker.setMap(null)
      }
    })
    fullscreenStationMarkers.length = 0
    fullscreenMap = null
  }
}

// 显示站点信息窗口（原生InfoWindow）
const showStationInfoWindow = (station, marker) => {
  // 关闭之前的InfoWindow
  if (currentInfoWindow) {
    currentInfoWindow.close()
  }

  // 创建新的InfoWindow
  currentInfoWindow = new window.google.maps.InfoWindow({
    content: createInfoWindowContent(station),
    maxWidth: 320,
    pixelOffset: new window.google.maps.Size(0, -10)
  })

  // 打开InfoWindow
  currentInfoWindow.open(map, marker)
}

// 显示全屏地图站点信息窗口
const showFullscreenStationInfoWindow = (station, marker) => {
  // 关闭之前的InfoWindow
  if (fullscreenInfoWindow) {
    fullscreenInfoWindow.close()
  }

  // 创建新的InfoWindow
  fullscreenInfoWindow = new window.google.maps.InfoWindow({
    content: createInfoWindowContent(station),
    maxWidth: 320,
    pixelOffset: new window.google.maps.Size(0, -10)
  })

  // 打开InfoWindow
  fullscreenInfoWindow.open(fullscreenMap, marker)
}

const initMap = () => {
  const mapRef = document.getElementById('googleMapRef')
  if (!mapRef || !window.google?.maps) return
  map = new window.google.maps.Map(mapRef, {
    center: { lat: props.data.lat, lng: props.data.lng },
    zoom: props.data.zoom,
    disableDefaultUI: false,  // 启用默认UI控件
    zoomControl: true,        // 缩放控件
    mapTypeControl: true,     // 地图类型控件（卫星、地形等）
    streetViewControl: true,  // 街景控件 - 已启用
    fullscreenControl: true,  // 全屏控件 - 已启用
    rotateControl: true,      // 旋转控件
    scaleControl: true,       // 比例尺控件
    mapId: 'DEMO_MAP_ID'
  })

  // 添加InfoWindow样式
  addInfoWindowStyles()

  // 移除默认标记，只显示换电站图标
  // marker = new window.google.maps.Marker({
  //   position: { lat: props.data.lat, lng: props.data.lng },
  //   map: map,
  //   title: props.data.locationDesc,
  //   icon: {
  //     url: 'https://maps.google.com/mapfiles/ms/icons/red-dot.png',
  //     scaledSize: new window.google.maps.Size(32, 32),
  //     anchor: new window.google.maps.Point(16, 32)
  //   }
  // })
  
  // 移除缩放事件监听器 - 使用固定大小图标
  
  // 如果启用换电站显示，加载站点数据
  if (props.data.showStations) {
    loadStationMarkers()
  }
}

// 加载站点标记
const loadStationMarkers = async () => {
  try {
    const stations = await getStationList()
    renderStationMarkers(stations)
  } catch (error) {
    console.error('加载站点数据失败:', error)
  }
}

const updateMap = () => {
  if (!map) return
  const latLng = { lat: props.data.lat, lng: props.data.lng }
  map.setCenter(latLng)
  map.setZoom(props.data.zoom)
  // 移除默认标记的更新
  // if (marker) {
  //   marker.setPosition(latLng)
  // }
}

watch(
  () => [props.data.lat, props.data.lng, props.data.zoom],
  () => {
    updateMap()
  }
)

// 监听换电站配置变化
watch(
  () => props.data.showStations,
  (newVal) => {
    if (newVal && map) {
      loadStationMarkers()
    } else {
      // 清除站点标记
      stationMarkers.forEach(marker => marker.setMap(null))
      stationMarkers.length = 0
    }
  }
)



onMounted(async () => {
  try {
    await loadGoogleMapAPI()
    await loadMarkerClusterer()
    initMap()
  } catch (e) {
    console.error('加载谷歌地图失败:', e)
  }
})

onBeforeUnmount(() => {
  // 清理InfoWindow
  if (currentInfoWindow) {
    currentInfoWindow.close()
    currentInfoWindow = null
  }
  if (fullscreenInfoWindow) {
    fullscreenInfoWindow.close()
    fullscreenInfoWindow = null
  }

  // 清理资源
  if (markerClusterer) {
    markerClusterer.clearMarkers && markerClusterer.clearMarkers()
    markerClusterer = null
  }
  if (fullscreenMarkerClusterer) {
    fullscreenMarkerClusterer.clearMarkers && fullscreenMarkerClusterer.clearMarkers()
    fullscreenMarkerClusterer = null
  }

  // 清理标记
  stationMarkers.forEach(marker => {
    if (marker.map) {
      marker.map = null
    } else if (marker.setMap) {
      marker.setMap(null)
    }
  })
  stationMarkers.length = 0

  fullscreenStationMarkers.forEach(marker => {
    if (marker.map) {
      marker.map = null
    } else if (marker.setMap) {
      marker.setMap(null)
    }
  })
  fullscreenStationMarkers.length = 0
})
</script>

<style lang="scss" scoped>
.google-map-container {
  width: 100%;
  padding: 12px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 10px;
}
// 标题和按钮同行布局
.map-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  .map-title {
    font-size: 16px;
    font-weight: bold;
    margin: 0;
    flex: 1;
  }

  .fullscreen-btn {
    background: #f8f9fa;
    color: #495057;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 6px 10px;
    font-size: 12px;
    display: flex;
    align-items: center;
    flex-shrink: 0;
    margin-left: 12px;

    .fullscreen-icon {
      font-size: 14px;
      margin-right: 4px;
    }

    .fullscreen-text {
      font-size: 12px;
      font-weight: 500;
    }

    &:active {
      background: #e9ecef;
      opacity: 0.8;
    }
  }
}

// 独立的标题样式（当没有按钮时）
.map-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
}

.map-desc {
  font-size: 14px;
  margin-bottom: 12px;
}

// 独立的工具栏样式（当没有标题时）
.map-toolbar {
  margin-bottom: 8px;
  display: flex;
  justify-content: flex-end;

  .fullscreen-btn {
    background: #f8f9fa;
    color: #495057;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 6px 10px;
    font-size: 12px;
    display: flex;
    align-items: center;

    .fullscreen-icon {
      font-size: 14px;
      margin-right: 4px;
    }

    .fullscreen-text {
      font-size: 12px;
      font-weight: 500;
    }

    &:active {
      background: #e9ecef;
      opacity: 0.8;
    }
  }
}

.map-container {
  width: 100%;
  min-height: 200px;
  background-color: #f1f1f1;
}

// 全屏地图容器样式
.fullscreen-map-container {
  width: 95vw;
  height: 85vh;
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .fullscreen-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #409eff;
    color: white;
    flex-shrink: 0;

    .fullscreen-title {
      font-size: 18px;
      font-weight: bold;
    }

    .close-btn {
      padding: 5px;

      .close-icon {
        font-size: 24px;
        color: white;
      }
    }
  }

  .fullscreen-map {
    flex: 1;
    width: 100%;
    height: 0; /* 重要：配合flex使用 */
    background-color: #f1f1f1;
  }
}

// 站点信息弹窗样式
.station-info-popup {
  background-color: #fff;
  border-radius: 12px;
  width: 300px;
  max-width: 90vw;
  
  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #eee;
    
    .popup-title {
      font-size: 18px;
      font-weight: bold;
      color: #333;
    }
    
    .popup-close {
      font-size: 24px;
      color: #999;
      padding: 4px;
      cursor: pointer;
    }
  }
  
  .popup-content {
    padding: 20px;
    
    .station-name {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      margin-bottom: 8px;
    }
    
    .station-address {
      font-size: 14px;
      color: #666;
      margin-bottom: 12px;
      line-height: 1.4;
    }
    
    .station-status {
      margin-bottom: 8px;
      
      .status-label {
        font-size: 14px;
        color: #666;
      }
      
      .status-value {
        font-size: 14px;
        font-weight: bold;
        
                 &.online {
           color: #67c23a;
         }
         
         &.offline {
           color: #f56c6c;
         }
         
         &.busy {
           color: #e6a23c;
         }
      }
    }
    
         .station-distance {
       .distance-label {
         font-size: 14px;
         color: #666;
       }
       
       .distance-value {
         font-size: 14px;
         color: #409eff;
         font-weight: bold;
       }
     }
     
     .station-info {
       margin-bottom: 8px;
       
       .info-label {
         font-size: 14px;
         color: #666;
       }
       
       .info-value {
         font-size: 14px;
         color: #333;
         font-weight: bold;
       }
     }
     
     .station-price {
       .price-label {
         font-size: 14px;
         color: #666;
       }
       
       .price-value {
         font-size: 14px;
         color: #f56c6c;
         font-weight: bold;
       }
     }
  }
  
  .popup-footer {
    display: flex;
    padding: 16px 20px;
    border-top: 1px solid #eee;
    gap: 12px;
    
    .popup-btn {
      flex: 1;
      height: 40px;
      border-radius: 6px;
      border: none;
      font-size: 14px;
      cursor: pointer;
      
      &.cancel-btn {
        background-color: #f5f5f5;
        color: #666;
      }
      
      &.primary-btn {
        background-color: #409eff;
        color: #fff;
      }
    }
  }
}

/* 专业标记样式 */
:deep(.station-marker) {
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.1);
  }
}

/* 聚合标记样式 */
:deep(.cluster-marker) {
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.1);
  }
}

/* SVG图标样式优化 */
:deep(.station-marker svg) {
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
}
</style>
