export default {
  system: {
    menu:{
      menu_Name: '菜单名称',
      menu_Icon: '图标',
      menu_Sort: '排序',
      menu_Permission: '权限标识',
      menu_Component: '组件路径',
      component_Name: '组件名称',
      component_Status: '状态',

    }
  },
  vehicle: {
    buscar: {
      carNo: '车辆号',
      // 其他字段...
    }
  },
  common: {
    inputText: '请输入',
    selectText: '请选择',
    startTimeText: '开始时间',
    endTimeText: '结束时间',
    login: '登录',
    required: '该项为必填项',
    loginOut: '退出系统',
    document: '项目文档',
    profile: '个人中心',
    reminder: '温馨提示',
    loginOutMessage: '是否退出本系统？',
    back: '返回',
    ok: '确定',
    save: '保存',
    cancel: '取消',
    close: '关闭',
    reload: '重新加载',
    success: '成功',
    closeTab: '关闭标签页',
    closeTheLeftTab: '关闭左侧标签页',
    closeTheRightTab: '关闭右侧标签页',
    closeOther: '关闭其他标签页',
    closeAll: '关闭全部标签页',
    prevLabel: '上一步',
    nextLabel: '下一步',
    skipLabel: '跳过',
    doneLabel: '结束',
    menu: '菜单',
    menuDes: '以路由的结构渲染的菜单栏',
    collapse: '展开缩收',
    collapseDes: '展开和缩放菜单栏',
    tagsView: '标签页',
    tagsViewDes: '用于记录路由历史记录',
    tool: '工具',
    toolDes: '用于设置定制系统',
    query: '查询',
    reset: '重置',
    shrink: '收起',
    expand: '展开',
    confirmTitle: '系统提示',
    exportMessage: '是否确认导出数据项？',
    importMessage: '是否确认导入数据项？',
    createSuccess: '新增成功',
    updateSuccess: '修改成功',
    delMessage: '是否删除所选中数据？',
    delDataMessage: '是否删除数据？',
    delNoData: '请选择需要删除的数据',
    delSuccess: '删除成功',
    index: '序号',
    status: '状态',
    createTime: '创建时间',
    updateTime: '更新时间',
    copy: '复制',
    copySuccess: '复制成功',
    copyError: '复制失败'
  },
  lock: {
    lockScreen: '锁定屏幕',
    lock: '锁定',
    lockPassword: '锁屏密码',
    unlock: '点击解锁',
    backToLogin: '返回登录',
    entrySystem: '进入系统',
    placeholder: '请输入锁屏密码',
    message: '锁屏密码错误'
  },
  error: {
    noPermission: `抱歉，您无权访问此页面。`,
    pageError: '抱歉，您访问的页面不存在。',
    networkError: '抱歉，服务器报告错误。',
    returnToHome: '返回首页'
  },
  permission: {
    hasPermission: `请设置操作权限标签值`,
    hasRole: `请设置角色权限标签值`
  },
  setting: {
    projectSetting: '项目配置',
    theme: '主题',
    layout: '布局',
    systemTheme: '系统主题',
    menuTheme: '菜单主题',
    interfaceDisplay: '界面显示',
    breadcrumb: '面包屑',
    breadcrumbIcon: '面包屑图标',
    collapseMenu: '折叠菜单',
    hamburgerIcon: '折叠图标',
    screenfullIcon: '全屏图标',
    sizeIcon: '尺寸图标',
    localeIcon: '多语言图标',
    messageIcon: '消息图标',
    tagsView: '标签页',
    tagsViewImmerse: '标签页沉浸',
    logo: '标志',
    greyMode: '灰色模式',
    fixedHeader: '固定头部',
    headerTheme: '头部主题',
    cutMenu: '切割菜单',
    copy: '拷贝',
    clearAndReset: '清除缓存并且重置',
    copySuccess: '拷贝成功',
    copyFailed: '拷贝失败',
    footer: '页脚',
    uniqueOpened: '菜单手风琴',
    tagsViewIcon: '标签页图标',
    reExperienced: '请重新退出登录体验',
    fixedMenu: '固定菜单'
  },
  size: {
    default: '默认',
    large: '大',
    small: '小'
  },
  login: {
    welcome: '欢迎使用本系统',
    message: 'YDYX中后台管理系统',
    tenantname: '租户名称',
    username: '用户名',
    password: '密码',
    code: '验证码',
    login: '登录',
    relogin: '重新登录',
    otherLogin: '其他登录方式',
    register: '注册',
    checkPassword: '确认密码',
    remember: '记住我',
    hasUser: '已有账号？去登录',
    forgetPassword: '忘记密码?',
    tenantNamePlaceholder: '请输入租户名称',
    usernamePlaceholder: '请输入用户名',
    passwordPlaceholder: '请输入密码',
    codePlaceholder: '请输入验证码',
    mobileTitle: '手机登录',
    mobileNumber: '手机号码',
    mobileNumberPlaceholder: '请输入手机号码',
    backLogin: '返回',
    getSmsCode: '获取验证码',
    btnMobile: '手机登录',
    btnQRCode: '二维码登录',
    qrcode: '扫描二维码登录',
    btnRegister: '注册',
    SmsSendMsg: '验证码已发送',
    resetPassword: '重置密码',
    resetPasswordSuccess: '重置密码成功',
    invalidTenantName: '无效的租户名称'
  },
  captcha: {
    verification: '请完成安全验证',
    slide: '向右滑动完成验证',
    point: '请依次点击',
    success: '验证成功',
    fail: '验证失败'
  },
  router: {
    login: '登录',
    socialLogin: '社交登录',
    home: '首页',
    analysis: '分析页',
    workplace: '工作台',
    system_management: '系统管理',
    infrastructure: '基础设施',
    oa_example: 'OA 示例',
    user_management: '用户管理',
    role_management: '角色管理',
    menu_management: '菜单管理',
    department_management: '部门管理',
    position_management: '岗位管理',
    dictionary_management: '字典管理',
    configuration_management: '配置管理',
    notification_announcement: '通知公告',
    audit_log: '审计日志',
    token_management: '令牌管理',
    scheduled_task: '定时任务',
    mysql_monitoring: 'MySQL 监控',
    java_monitoring: 'Java 监控',
    redis_monitoring: 'Redis 监控',
    form_builder: '表单构建',
    code_generation: '代码生成',
    api_interface: 'API 接口',
    operation_log: '操作日志',
    login_log: '登录日志',
    user_query: '用户查询',
    user_create: '用户新增',
    user_update: '用户修改',
    user_delete: '用户删除',
    user_export: '用户导出',
    user_import: '用户导入',
    reset_password: '重置密码',
    role_query: '角色查询',
    role_create: '角色新增',
    role_update: '角色修改',
    role_delete: '角色删除',
    role_export: '角色导出',
    menu_query: '菜单查询',
    menu_create: '菜单新增',
    menu_update: '菜单修改',
    menu_delete: '菜单删除',
    department_query: '部门查询',
    department_create: '部门新增',
    department_update: '部门修改',
    department_delete: '部门删除',
    position_query: '岗位查询',
    position_create: '岗位新增',
    position_update: '岗位修改',
    position_delete: '岗位删除',
    position_export: '岗位导出',
    dictionary_query: '字典查询',
    dictionary_create: '字典新增',
    dictionary_update: '字典修改',
    dictionary_delete: '字典删除',
    dictionary_export: '字典导出',
    configuration_query: '配置查询',
    configuration_create: '配置新增',
    configuration_update: '配置修改',
    configuration_delete: '配置删除',
    configuration_export: '配置导出',
    announcement_query: '公告查询',
    announcement_create: '公告新增',
    announcement_update: '公告修改',
    announcement_delete: '公告删除',
    operation_query: '操作查询',
    log_export: '日志导出',
    login_query: '登录查询',
    token_list: '令牌列表',
    token_delete: '令牌删除',
    task_create: '任务新增',
    task_update: '任务修改',
    task_delete: '任务删除',
    status_update: '状态修改',
    task_export: '任务导出',
    generation_update: '生成修改',
    generation_delete: '生成删除',
    import_code: '导入代码',
    preview_code: '预览代码',
    generate_code: '生成代码',
    set_role_menu_permission: '设置角色菜单权限',
    set_role_data_permission: '设置角色数据权限',
    set_user_role: '设置用户角色',
    get_redis_monitoring_info: '获得 Redis 监控信息',
    get_redis_key_list: '获得 Redis Key 列表',
    code_generation_example: '代码生成案例',
    task_trigger: '任务触发',
    trace_link: '链路追踪',
    access_log: '访问日志',
    api_log: 'API 日志',
    error_log: '错误日志',
    log_processing: '日志处理',
    task_query: '任务查询',
    log_query: '日志查询',
    file_list: '文件列表',
    file_query: '文件查询',
    file_delete: '文件删除',
    sms_management: '短信管理',
    sms_channel: '短信渠道',
    sms_channel_query: '短信渠道查询',
    sms_channel_create: '短信渠道创建',
    sms_channel_update: '短信渠道更新',
    sms_channel_delete: '短信渠道删除',
    sms_template: '短信模板',
    sms_template_query: '短信模板查询',
    sms_template_create: '短信模板创建',
    sms_template_update: '短信模板更新',
    sms_template_delete: '短信模板删除',
    sms_template_export: '短信模板导出',
    send_test_sms: '发送测试短信',
    sms_log: '短信日志',
    sms_log_query: '短信日志查询',
    sms_log_export: '短信日志导出',
    payment_management: '支付管理',
    leave_query: '请假查询',
    leave_application_query: '请假申请查询',
    leave_application_create: '请假申请创建',
    application_information: '应用信息',
    payment_application_info_query: '支付应用信息查询',
    payment_application_info_create: '支付应用信息创建',
    payment_application_info_update: '支付应用信息更新',
    payment_application_info_delete: '支付应用信息删除',
    secret_key_parsing: '秘钥解析',
    payment_merchant_info_query: '支付商户信息查询',
    payment_merchant_info_create: '支付商户信息创建',
    payment_merchant_info_update: '支付商户信息更新',
    payment_merchant_info_delete: '支付商户信息删除',
    payment_merchant_info_export: '支付商户信息导出',
    tenant_list: '租户列表',
    tenant_query: '租户查询',
    tenant_create: '租户创建',
    tenant_update: '租户更新',
    tenant_delete: '租户删除',
    tenant_export: '租户导出',
    refund_order: '退款订单',
    refund_order_query: '退款订单查询',
    refund_order_create: '退款订单创建',
    refund_order_update: '退款订单更新',
    refund_order_delete: '退款订单删除',
    refund_order_export: '退款订单导出',
    payment_order: '支付订单',
    payment_order_query: '支付订单查询',
    payment_order_create: '支付订单创建',
    payment_order_update: '支付订单更新',
    payment_order_delete: '支付订单删除',
    payment_order_export: '支付订单导出',
    workflow_management: '工作流程',
    process_management: '流程管理',
    process_form: '流程表单',
    form_query: '表单查询',
    form_create: '表单创建',
    form_update: '表单更新',
    form_delete: '表单删除',
    form_export: '表单导出',
    process_model: '流程模型',
    model_query: '模型查询',
    model_create: '模型创建',
    model_update: '模型更新',
    model_delete: '模型删除',
    model_publish: '模型发布',
    approval_center: '审批中心',
    my_processes: '我的流程',
    process_instance_query: '流程实例的查询',
    pending_task: '待办任务',
    completed_task: '已办任务',
    user_group: '用户分组',
    user_group_query: '用户组查询',
    user_group_create: '用户组创建',
    user_group_update: '用户组更新',
    user_group_delete: '用户组删除',
    process_definition_query: '流程定义查询',
    process_task_assignment_rule_query: '流程任务分配规则查询',
    process_task_assignment_rule_create: '流程任务分配规则创建',
    process_task_assignment_rule_update: '流程任务分配规则更新',
    process_instance_create: '流程实例的创建',
    process_instance_cancel: '流程实例的取消',
    process_task_query: '流程任务的查询',
    process_task_update: '流程任务的更新',
    tenant_management: '租户管理',
    tenant_package: '租户套餐',
    tenant_package_query: '租户套餐查询',
    tenant_package_create: '租户套餐创建',
    tenant_package_update: '租户套餐更新',
    tenant_package_delete: '租户套餐删除',
    file_configuration: '文件配置',
    file_configuration_query: '文件配置查询',
    file_configuration_create: '文件配置创建',
    file_configuration_update: '文件配置更新',
    file_configuration_delete: '文件配置删除',
    file_configuration_export: '文件配置导出',
    file_management: '文件管理',
    author_dynamics: '作者动态',
    datasource_configuration: '数据源配置',
    datasource_configuration_query: '数据源配置查询',
    datasource_configuration_create: '数据源配置创建',
    datasource_configuration_update: '数据源配置更新',
    datasource_configuration_delete: '数据源配置删除',
    datasource_configuration_export: '数据源配置导出',
    oauth_2_0: 'OAuth 2.0',
    application_management: '应用管理',
    client_query: '客户端查询',
    client_create: '客户端创建',
    client_update: '客户端更新',
    client_delete: '客户端删除',
    report_management: '报表管理',
    report_designer: '报表设计器',
    product_center: '商品中心',
    product_category: '商品分类',
    category_query: '分类查询',
    category_create: '分类创建',
    category_update: '分类更新',
    category_delete: '分类删除',
    product_brand: '商品品牌',
    brand_query: '品牌查询',
    brand_create: '品牌创建',
    brand_update: '品牌更新',
    brand_delete: '品牌删除',
    product_list: '商品列表',
    product_query: '商品查询',
    product_create: '商品创建',
    product_update: '商品更新',
    product_delete: '商品删除',
    product_attribute: '商品属性',
    specification_query: '规格查询',
    specification_create: '规格创建',
    specification_update: '规格更新',
    specification_delete: '规格删除',
    banner: 'Banner',
    banner_query: 'Banner查询',
    banner_create: 'Banner创建',
    banner_update: 'Banner更新',
    banner_delete: 'Banner删除',
    marketing_center: '营销中心',
    coupon_list: '优惠劵列表',
    coupon_template_query: '优惠劵模板查询',
    coupon_template_create: '优惠劵模板创建',
    coupon_template_update: '优惠劵模板更新',
    coupon_template_delete: '优惠劵模板删除',
    claim_record: '领取记录',
    coupon_query: '优惠劵查询',
    coupon_delete: '优惠劵删除',
    full_reduction_promotion: '满减送',
    full_reduction_activity_query: '满减送活动查询',
    full_reduction_activity_create: '满减送活动创建',
    full_reduction_activity_update: '满减送活动更新',
    full_reduction_activity_delete: '满减送活动删除',
    full_reduction_activity_close: '满减送活动关闭',
    limited_time_discount: '限时折扣',
    limited_time_discount_activity_query: '限时折扣活动查询',
    limited_time_discount_activity_create: '限时折扣活动创建',
    limited_time_discount_activity_update: '限时折扣活动更新',
    limited_time_discount_activity_delete: '限时折扣活动删除',
    limited_time_discount_activity_close: '限时折扣活动关闭',
    flash_sale_product: '秒杀商品',
    flash_sale_activity_query: '秒杀活动查询',
    flash_sale_activity_create: '秒杀活动创建',
    flash_sale_activity_update: '秒杀活动更新',
    flash_sale_activity_delete: '秒杀活动删除',
    flash_sale_timeslot: '秒杀时段',
    flash_sale_timeslot_query: '秒杀时段查询',
    flash_sale_timeslot_create: '秒杀时段创建',
    flash_sale_timeslot_update: '秒杀时段更新',
    flash_sale_timeslot_delete: '秒杀时段删除',
    order_center: '订单中心',
    after_sales_refund: '售后退款',
    after_sales_query: '售后查询',
    flash_sale_activity_close: '秒杀活动关闭',
    order_list: '订单列表',
    region_management: '地区管理',
    official_account_management: '公众号管理',
    account_management: '账号管理',
    add_account: '新增账号',
    update_account: '修改账号',
    query_account: '查询账号',
    delete_account: '删除账号',
    generate_qrcode: '生成二维码',
    clear_api_quota: '清空 API 配额',
    data_statistics: '数据统计',
    tag_management: '标签管理',
    query_tag: '查询标签',
    add_tag: '新增标签',
    update_tag: '修改标签',
    delete_tag: '删除标签',
    sync_tag: '同步标签',
    fan_management: '粉丝管理',
    query_fan: '查询粉丝',
    update_fan: '修改粉丝',
    sync_fan: '同步粉丝',
    message_management: '消息管理',
    article_publish_record: '图文发表记录',
    query_publish_list: '查询发布列表',
    publish_draft: '发布草稿',
    delete_publish_record: '删除发布记录',
    article_draft_box: '图文草稿箱',
    create_draft: '新建草稿',
    update_draft: '修改草稿',
    query_draft: '查询草稿',
    delete_draft: '删除草稿',
    material_management: '素材管理',
    upload_temporary_material: '上传临时素材',
    upload_permanent_material: '上传永久素材',
    delete_material: '删除素材',
    upload_article_image: '上传图文图片',
    query_material: '查询素材',
    menu_management_official_account: '菜单管理', // Differentiated for official account context
    auto_reply: '自动回复',
    query_reply: '查询回复',
    add_reply: '新增回复',
    update_reply: '修改回复',
    delete_reply: '删除回复',
    query_menu: '查询菜单',
    save_menu: '保存菜单',
    delete_menu: '删除菜单',
    query_message: '查询消息',
    send_message: '发送消息',
    email_management: '邮箱管理',
    email_account: '邮箱账号',
    account_query: '账号查询',
    account_create: '账号创建',
    account_update: '账号更新',
    account_delete: '账号删除',
    email_template: '邮件模版',
    template_query: '模版查询',
    template_create: '模版创建',
    template_update: '模版更新',
    template_delete: '模版删除',
    email_record: '邮件记录',
    log_query_email: '日志查询', // Differentiated for email context
    send_test_email: '发送测试邮件',
    internal_message_management: '站内信管理',
    template_management_internal_message: '模板管理', // Differentiated for internal message context
    internal_message_template_query: '站内信模板查询',
    internal_message_template_create: '站内信模板创建',
    internal_message_template_update: '站内信模板更新',
    internal_message_template_delete: '站内信模板删除',
    send_test_internal_message: '发送测试站内信',
    message_record: '消息记录',
    internal_message_query: '站内信消息查询',
    large_screen_designer: '大屏设计器',
    create_project: '创建项目',
    update_project: '更新项目',
    query_project: '查询项目',
    query_data_sql: '使用 SQL 查询数据',
    query_data_http: '使用 HTTP 查询数据',
    boot_development_document: 'Boot 开发文档',
    cloud_development_document: 'Cloud 开发文档',
    access_example: '接入示例',
    product_export: '商品导出',
    delivery_management: '配送管理',
    express_delivery_shipment: '快递发货',
    store_self_pickup: '门店自提',
    express_company: '快递公司',
    express_company_query: '快递公司查询',
    express_company_create: '快递公司创建',
    express_company_update: '快递公司更新',
    express_company_delete: '快递公司删除',
    express_company_export: '快递公司导出',
    shipping_template: '运费模版',
    shipping_template_query: '快递运费模板查询',
    shipping_template_create: '快递运费模板创建',
    shipping_template_update: '快递运费模板更新',
    shipping_template_delete: '快递运费模板删除',
    shipping_template_export: '快递运费模板导出',
    store_management: '门店管理',
    self_pickup_store_query: '自提门店查询',
    self_pickup_store_create: '自提门店创建',
    self_pickup_store_update: '自提门店更新',
    self_pickup_store_delete: '自提门店删除',
    self_pickup_store_export: '自提门店导出',
    flash_sale_activity: '秒杀活动',
    member_center: '会员中心',
    member_configuration: '会员配置',
    member_configuration_query: '会员配置查询',
    member_configuration_save: '会员配置保存',
    check_in_configuration: '签到配置',
    points_check_in_rule_query: '积分签到规则查询',
    points_check_in_rule_create: '积分签到规则创建',
    points_check_in_rule_update: '积分签到规则更新',
    points_check_in_rule_delete: '积分签到规则删除',
    member_points: '会员积分',
    user_points_record_query: '用户积分记录查询',
    check_in_record: '签到记录',
    user_check_in_points_query: '用户签到积分查询',
    user_check_in_points_delete: '用户签到积分删除',
    member_check_in: '会员签到',
    callback_notification: '回调通知',
    payment_notification_query: '支付通知查询',
    group_buying_activity: '拼团活动',
    group_buying_product: '拼团商品',
    group_buying_activity_query: '拼团活动查询',
    group_buying_activity_create: '拼团活动创建',
    group_buying_activity_update: '拼团活动更新',
    group_buying_activity_delete: '拼团活动删除',
    group_buying_activity_close: '拼团活动关闭',
    bargain_activity: '砍价活动',
    bargain_product: '砍价商品',
    bargain_activity_query: '砍价活动查询',
    bargain_activity_create: '砍价活动创建',
    bargain_activity_update: '砍价活动更新',
    bargain_activity_delete: '砍价活动删除',
    bargain_activity_close: '砍价活动关闭',
    member_management: '会员管理',
    member_user_query: '会员用户查询',
    member_user_update: '会员用户更新',
    member_tag: '会员标签',
    member_tag_query: '会员标签查询',
    member_tag_create: '会员标签创建',
    member_tag_update: '会员标签更新',
    member_tag_delete: '会员标签删除',
    member_level: '会员等级',
    member_level_query: '会员等级查询',
    member_level_create: '会员等级创建',
    member_level_update: '会员等级更新',
    member_level_delete: '会员等级删除',
    member_group: '会员分组',
    user_group_query_member: '用户分组查询', // Differentiated for member context
    user_group_create_member: '用户分组创建', // Differentiated for member context
    user_group_update_member: '用户分组更新', // Differentiated for member context
    user_group_delete_member: '用户分组删除', // Differentiated for member context
    user_level_update: '用户等级修改',
    product_comment: '商品评论',
    comment_query: '评论查询',
    add_self_comment: '添加自评',
    merchant_reply: '商家回复',
    show_hide_comment: '显隐评论',
    coupon_send: '优惠劵发送',
    transaction_configuration: '交易配置',
    transaction_center_configuration_query: '交易中心配置查询',
    transaction_center_configuration_save: '交易中心配置保存',
    distribution_management: '分销管理',
    distribution_user: '分销用户',
    distribution_user_query: '分销用户查询',
    distribution_user_promoter_query: '分销用户推广人查询',
    distribution_user_promotion_order_query: '分销用户推广订单查询',
    distribution_user_update_promotion_qualification: '分销用户修改推广资格',
    update_promoter: '修改推广员',
    clear_promoter: '清除推广员',
    commission_record: '佣金记录',
    commission_record_query: '佣金记录查询',
    commission_withdrawal: '佣金提现',
    commission_withdrawal_query: '佣金提现查询',
    commission_withdrawal_approve: '佣金提现审核',
    statistics_center: '统计中心',
    transaction_statistics: '交易统计',
    transaction_statistics_query: '交易统计查询',
    transaction_statistics_export: '交易统计导出',
    mall_system: '商城系统',
    user_points_update: '用户积分修改',
    user_balance_update: '用户余额修改',
    coupon: '优惠劵',
    bargain_record: '砍价记录',
    bargain_record_query: '砍价记录查询',
    assistance_record_query: '助力记录查询',
    group_buying_record: '拼团记录',
    member_statistics: '会员统计',
    member_statistics_query: '会员统计查询',
    order_verification: '订单核销',
    article_category: '文章分类',
    category_query_article: '分类查询', // Differentiated for article context
    category_create_article: '分类创建', // Differentiated for article context
    category_update_article: '分类更新', // Differentiated for article context
    category_delete_article: '分类删除', // Differentiated for article context
    article_list: '文章列表',
    article_management_query: '文章管理查询',
    article_management_create: '文章管理创建',
    article_management_update: '文章管理更新',
    article_management_delete: '文章管理删除',
    content_management: '内容管理',
    mall_home_page: '商城首页',
    verify_order: '核销订单',
    promotional_activities: '优惠活动',
    customer_management: '客户管理',
    customer_query: '客户查询',
    customer_create: '客户创建',
    customer_update: '客户更新',
    customer_delete: '客户删除',
    customer_export: '客户导出',
    crm_system: 'CRM 系统',
    contract_management: '合同管理',
    contract_query: '合同查询',
    contract_create: '合同创建',
    contract_update: '合同更新',
    contract_delete: '合同删除',
    contract_export: '合同导出',
    lead_management: '线索管理',
    lead_query: '线索查询',
    lead_create: '线索创建',
    lead_update: '线索更新',
    lead_delete: '线索删除',
    lead_export: '线索导出',
    opportunity_management: '商机管理',
    opportunity_query: '商机查询',
    opportunity_create: '商机创建',
    opportunity_update: '商机更新',
    opportunity_delete: '商机删除',
    opportunity_export: '商机导出',
    contact_management: '联系人管理',
    contact_query: '联系人查询',
    contact_create: '联系人创建',
    contact_update: '联系人更新',
    contact_delete: '联系人删除',
    contact_export: '联系人导出',
    payment_collection_management: '回款管理',
    payment_collection_management_query: '回款管理查询',
    payment_collection_management_create: '回款管理创建',
    payment_collection_management_update: '回款管理更新',
    payment_collection_management_delete: '回款管理删除',
    payment_collection_management_export: '回款管理导出',
    payment_plan: '回款计划',
    payment_plan_query: '回款计划查询',
    payment_plan_create: '回款计划创建',
    payment_plan_update: '回款计划更新',
    payment_plan_delete: '回款计划删除',
    payment_plan_export: '回款计划导出',
    store_decoration: '商城装修',
    decoration_template: '装修模板',
    decoration_template_query: '装修模板查询',
    decoration_template_create: '装修模板创建',
    decoration_template_update: '装修模板更新',
    decoration_template_delete: '装修模板删除',
    decoration_template_use: '装修模板使用',
    decoration_page: '装修页面',
    decoration_page_query: '装修页面查询',
    decoration_page_create: '装修页面创建',
    decoration_page_update: '装修页面更新',
    decoration_page_delete: '装修页面删除',
    third_party_login: '三方登录',
    third_party_application: '三方应用',
    third_party_application_query: '三方应用查询',
    third_party_application_create: '三方应用创建',
    third_party_application_update: '三方应用更新',
    third_party_application_delete: '三方应用删除',
    third_party_user: '三方用户',
    main_sub_table_inline: '主子表（内嵌）',
    single_table_crud: '单表（增删改查）',
    example_contact_query: '示例联系人查询',
    example_contact_create: '示例联系人创建',
    example_contact_update: '示例联系人更新',
    example_contact_delete: '示例联系人删除',
    example_contact_export: '示例联系人导出',
    tree_table_crud: '树表（增删改查）',
    example_category_query: '示例分类查询',
    example_category_create: '示例分类创建',
    example_category_update: '示例分类更新',
    example_category_delete: '示例分类删除',
    example_category_export: '示例分类导出',
    main_sub_table_standard: '主子表（标准）',
    student_query: '学生查询',
    student_create: '学生创建',
    student_update: '学生更新',
    student_delete: '学生删除',
    student_export: '学生导出',
    main_sub_table_erp: '主子表（ERP）',
    customer_common_pool_config: '客户公海配置',
    customer_common_pool_config_save: '客户公海配置保存',
    customer_limit_config: '客户限制配置',
    customer_limit_config_query: '客户限制配置查询',
    customer_limit_config_create: '客户限制配置创建',
    customer_limit_config_update: '客户限制配置更新',
    customer_limit_config_delete: '客户限制配置删除',
    customer_limit_config_export: '客户限制配置导出',
    system_configuration: '系统配置',
    websocket: 'WebSocket',
    product_management: '产品管理',
    product_query_generic: '产品查询', // Differentiated for generic product context
    product_create_generic: '产品创建', // Differentiated
    product_update_generic: '产品更新', // Differentiated
    product_delete_generic: '产品删除', // Differentiated
    product_export_generic: '产品导出', // Differentiated
    product_category_config: '产品分类配置',
    product_category_query: '产品分类查询',
    product_category_create: '产品分类创建',
    product_category_update: '产品分类更新',
    product_category_delete: '产品分类删除',
    associate_opportunity: '关联商机',
    unfollow_opportunity: '取关商机',
    product_statistics: '商品统计',
    customer_common_pool: '客户公海',
    order_query: '订单查询',
    order_update: '订单更新',
    payment_refund_example: '支付&退款案例',
    transfer_example: '转账案例',
    wallet_management: '钱包管理',
    recharge_package: '充值套餐',
    wallet_recharge_package_query: '钱包充值套餐查询',
    wallet_recharge_package_create: '钱包充值套餐创建',
    wallet_recharge_package_update: '钱包充值套餐更新',
    wallet_recharge_package_delete: '钱包充值套餐删除',
    wallet_balance: '钱包余额',
    wallet_balance_query: '钱包余额查询',
    transfer_order: '转账订单',
    data_statistics_generic: '数据统计', // Differentiated for generic context
    leaderboard: '排行榜',
    customer_import: '客户导入',
    erp_system: 'ERP 系统',
    product_management_erp: '产品管理', // Differentiated for ERP context
    product_information: '产品信息',
    product_query_erp: '产品查询', // Differentiated for ERP context
    product_create_erp: '产品创建', // Differentiated for ERP context
    product_update_erp: '产品更新', // Differentiated for ERP context
    product_delete_erp: '产品删除', // Differentiated for ERP context
    product_export_erp: '产品导出', // Differentiated for ERP context
    product_category_erp: '产品分类', // Differentiated for ERP context
    category_query_erp: '分类查询', // Differentiated for ERP context
    category_create_erp: '分类创建', // Differentiated for ERP context
    category_update_erp: '分类更新', // Differentiated for ERP context
    category_delete_erp: '分类删除', // Differentiated for ERP context
    category_export_erp: '分类导出', // Differentiated for ERP context
    product_unit: '产品单位',
    unit_query: '单位查询',
    unit_create: '单位创建',
    unit_update: '单位更新',
    unit_delete: '单位删除',
    unit_export: '单位导出',
    inventory_management: '库存管理',
    warehouse_information: '仓库信息',
    warehouse_query: '仓库查询',
    warehouse_create: '仓库创建',
    warehouse_update: '仓库更新',
    warehouse_delete: '仓库删除',
    warehouse_export: '仓库导出',
    product_inventory: '产品库存',
    inventory_query: '库存查询',
    inventory_export: '库存导出',
    stock_in_out_details: '出入库明细',
    inventory_details_query: '库存明细查询',
    inventory_details_export: '库存明细导出',
    other_inbound: '其它入库',
    other_inbound_order_query: '其它入库单查询',
    other_inbound_order_create: '其它入库单创建',
    other_inbound_order_update: '其它入库单更新',
    other_inbound_order_delete: '其它入库单删除',
    other_inbound_order_export: '其它入库单导出',
    purchase_management: '采购管理',
    supplier_information: '供应商信息',
    supplier_query: '供应商查询',
    supplier_create: '供应商创建',
    supplier_update: '供应商更新',
    supplier_delete: '供应商删除',
    supplier_export: '供应商导出',
    other_inbound_order_approval: '其它入库单审批',
    other_outbound: '其它出库',
    other_outbound_order_query: '其它出库单查询',
    other_outbound_order_create: '其它出库单创建',
    other_outbound_order_update: '其它出库单更新',
    other_outbound_order_delete: '其它出库单删除',
    other_outbound_order_export: '其它出库单导出',
    other_outbound_order_approval: '其它出库单审批',
    sales_management: '销售管理',
    customer_information: '客户信息',
    customer_query_sales: '客户查询', // Differentiated for sales context
    customer_create_sales: '客户创建', // Differentiated for sales context
    customer_update_sales: '客户更新', // Differentiated for sales context
    customer_delete_sales: '客户删除', // Differentiated for sales context
    customer_export_sales: '客户导出', // Differentiated for sales context
    inventory_transfer: '库存调拨',
    inventory_transfer_order_query: '库存调度单查询',
    inventory_transfer_order_create: '库存调度单创建',
    inventory_transfer_order_update: '库存调度单更新',
    inventory_transfer_order_delete: '库存调度单删除',
    inventory_transfer_order_export: '库存调度单导出',
    inventory_transfer_order_approval: '库存调度单审批',
    inventory_count: '库存盘点',
    inventory_count_order_query: '库存盘点单查询',
    inventory_count_order_create: '库存盘点单创建',
    inventory_count_order_update: '库存盘点单更新',
    inventory_count_order_delete: '库存盘点单删除',
    inventory_count_order_export: '库存盘点单导出',
    inventory_count_order_approval: '库存盘点单审批',
    sales_order: '销售订单',
    sales_order_query: '销售订单查询',
    sales_order_create: '销售订单创建',
    sales_order_update: '销售订单更新',
    sales_order_delete: '销售订单删除',
    sales_order_export: '销售订单导出',
    sales_order_approval: '销售订单审批',
    financial_management: '财务管理',
    settlement_account: '结算账户',
    settlement_account_query: '结算账户查询',
    settlement_account_create: '结算账户创建',
    settlement_account_update: '结算账户更新',
    settlement_account_delete: '结算账户删除',
    settlement_account_export: '结算账户导出',
    sales_outbound: '销售出库',
    sales_outbound_query: '销售出库查询',
    sales_outbound_create: '销售出库创建',
    sales_outbound_update: '销售出库更新',
    sales_outbound_delete: '销售出库删除',
    sales_outbound_export: '销售出库导出',
    sales_outbound_approval: '销售出库审批',
    sales_return: '销售退货',
    sales_return_query: '销售退货查询',
    sales_return_create: '销售退货创建',
    sales_return_update: '销售退货更新',
    sales_return_delete: '销售退货删除',
    sales_return_export: '销售退货导出',
    sales_return_approval: '销售退货审批',
    purchase_order: '采购订单',
    purchase_order_query: '采购订单查询',
    purchase_order_create: '采购订单创建',
    purchase_order_update: '采购订单更新',
    purchase_order_delete: '采购订单删除',
    purchase_order_export: '采购订单导出',
    purchase_order_approval: '采购订单审批',
    purchase_inbound: '采购入库',
    purchase_inbound_query: '采购入库查询',
    purchase_inbound_create: '采购入库创建',
    purchase_inbound_update: '采购入库更新',
    purchase_inbound_delete: '采购入库删除',
    purchase_inbound_export: '采购入库导出',
    purchase_inbound_approval: '采购入库审批',
    purchase_return: '采购退货',
    purchase_return_query: '采购退货查询',
    purchase_return_create: '采购退货创建',
    purchase_return_update: '采购退货更新',
    purchase_return_delete: '采购退货删除',
    purchase_return_export: '采购退货导出',
    purchase_return_approval: '采购退货审批',
    payment_slip: '付款单',
    payment_slip_query: '付款单查询',
    payment_slip_create: '付款单创建',
    payment_slip_update: '付款单更新',
    payment_slip_delete: '付款单删除',
    payment_slip_export: '付款单导出',
    payment_slip_approval: '付款单审批',
    receipt_slip: '收款单',
    receipt_slip_query: '收款单查询',
    receipt_slip_create: '收款单创建',
    receipt_slip_update: '收款单更新',
    receipt_slip_delete: '收款单删除',
    receipt_slip_export: '收款单导出',
    receipt_slip_approval: '收款单审批',
    todo_items: '待办事项',
    erp_home_page: 'ERP 首页',
    opportunity_status_config: '商机状态配置',
    opportunity_status_query: '商机状态查询',
    opportunity_status_create: '商机状态创建',
    opportunity_status_update: '商机状态更新',
    opportunity_status_delete: '商机状态删除',
    contract_configuration: '合同配置',
    customer_common_pool_config_query: '客户公海配置查询',
    contract_configuration_update: '合同配置更新',
    contract_configuration_query: '合同配置查询',
    customer_analysis: '客户分析',
    cc_to_me: '抄送我的',
    process_category: '流程分类',
    category_query_process: '分类查询', // Differentiated for process context
    category_create_process: '分类创建', // Differentiated for process context
    category_update_process: '分类更新', // Differentiated for process context
    category_delete_process: '分类删除', // Differentiated for process context
    initiate_process: '发起流程',
    process_instance: '流程实例',
    process_instance_query_admin: '流程实例的查询（管理员）',
    process_instance_cancel_admin: '流程实例的取消（管理员）',
    process_task: '流程任务',
    process_task_query_admin: '流程任务的查询（管理员）',
    process_listener: '流程监听器',
    process_listener_query: '流程监听器查询',
    process_listener_create: '流程监听器创建',
    process_listener_update: '流程监听器更新',
    process_listener_delete: '流程监听器删除',
    process_expression: '流程表达式',
    process_expression_query: '流程表达式查询',
    process_expression_create: '流程表达式创建',
    process_expression_update: '流程表达式更新',
    process_expression_delete: '流程表达式删除',
    employee_performance: '员工业绩',
    customer_profile: '客户画像',
    sales_funnel: '销售漏斗',
    message_center: '消息中心',
    monitoring_center: '监控中心',
    claim_common_pool_customer: '领取公海客户',
    assign_common_pool_customer: '分配公海客户',
    product_statistics_query: '商品统计查询',
    product_statistics_export: '商品统计导出',
    payment_channel_query: '支付渠道查询',
    payment_channel_create: '支付渠道创建',
    payment_channel_update: '支付渠道更新',
    payment_channel_delete: '支付渠道删除',
    product_collection_query: '商品收藏查询',
    product_browsing_query: '商品浏览查询',
    after_sales_approve: '售后同意',
    after_sales_reject: '售后不同意',
    after_sales_confirm_return: '售后确认退货',
    after_sales_confirm_refund: '售后确认退款',
    delete_project: '删除项目',
    member_level_record_query: '会员等级记录查询',
    member_experience_record_query: '会员经验记录查询',
    ai_large_model: 'AI 大模型',
    ai_chat: 'AI 对话',
    console: '控制台',
    api_key: 'API 密钥',
    api_key_query: 'API 密钥查询',
    api_key_create: 'API 密钥创建',
    api_key_update: 'API 密钥更新',
    api_key_delete: 'API 密钥删除',
    model_configuration: '模型配置',
    chat_model_query: '聊天模型查询',
    chat_model_create: '聊天模型创建',
    chat_model_update: '聊天模型更新',
    chat_model_delete: '聊天模型删除',
    chat_role: '聊天角色',
    chat_role_query: '聊天角色查询',
    chat_role_create: '聊天角色创建',
    chat_role_update: '聊天角色更新',
    chat_role_delete: '聊天角色删除',
    chat_management: '聊天管理',
    session_query: '会话查询',
    session_delete: '会话删除',
    message_query_chat: '消息查询', // Differentiated for chat context
    message_delete_chat: '消息删除', // Differentiated for chat context
    ai_drawing: 'AI 绘画',
    drawing_management: '绘画管理',
    drawing_query: '绘画查询',
    drawing_delete: '绘画删除',
    drawing_update: '绘图更新',
    music_management: '音乐管理',
    music_query: '音乐查询',
    music_update: '音乐更新',
    music_delete: '音乐删除',
    ai_writing: 'AI 写作',
    writing_management: '写作管理',
    ai_writing_query: 'AI 写作查询',
    ai_writing_delete: 'AI 写作删除',
    ai_music: 'AI 音乐',
    customer_service_center: '客服中心',
    ai_mind_map: 'AI 思维导图',
    mind_map_management: '导图管理',
    mind_map_query: '思维导图查询',
    mind_map_delete: '思维导图删除',
    session_query_mindmap: '会话查询', // Differentiated for mindmap context
    session_update_mindmap: '会话更新', // Differentiated for mindmap context
    message_query_mindmap: '消息查询', // Differentiated for mindmap context
    session_delete_mindmap: '会话删除', // Differentiated for mindmap context
    message_send_mindmap: '消息发送', // Differentiated for mindmap context
    message_update_mindmap: '消息更新', // Differentiated for mindmap context
    points_mall: '积分商城',
    points_mall_activity_query: '积分商城活动查询',
    points_mall_activity_create: '积分商城活动创建',
    points_mall_activity_update: '积分商城活动更新',
    points_mall_activity_delete: '积分商城活动删除',
    points_mall_activity_export: '积分商城活动导出',
    create_promoter: '创建推广员',
    process_cleanup: '流程清理',
    points_mall_activity_close: '积分商城活动关闭',
    ai_knowledge_base: 'AI 知识库',
    ai_knowledge_base_query: 'AI 知识库查询',
    ai_knowledge_base_create: 'AI 知识库创建',
    ai_knowledge_base_update: 'AI 知识库更新',
    ai_knowledge_base_delete: 'AI 知识库删除',
    tool_management: '工具管理',
    tool_query: '工具查询',
    tool_create: '工具创建',
    tool_update: '工具更新',
    tool_delete: '工具删除',
    iot_internet_of_things: 'IoT 物联网',
    device_access: '设备接入',
    product_management_iot: '产品管理', // Differentiated for IoT context
    product_query_iot: '产品查询', // Differentiated for IoT context
    product_create_iot: '产品创建', // Differentiated for IoT context
    product_update_iot: '产品更新', // Differentiated for IoT context
    product_delete_iot: '产品删除', // Differentiated for IoT context
    product_export_iot: '产品导出', // Differentiated for IoT context
    device_management: '设备管理',
    device_query: '设备查询',
    device_create: '设备创建',
    device_update: '设备更新',
    device_delete: '设备删除',
    device_export: '设备导出',
    product_category_iot: '产品分类', // Differentiated for IoT context
    product_category_query_iot: '产品分类查询', // Differentiated for IoT context
    product_category_create_iot: '产品分类创建', // Differentiated for IoT context
    product_category_update_iot: '产品分类更新', // Differentiated for IoT context
    product_category_delete_iot: '产品分类删除', // Differentiated for IoT context
    plugin_management: '插件管理',
    plugin_query: '插件查询',
    plugin_create: '插件创建',
    plugin_update: '插件更新',
    plugin_delete: '插件删除',
    plugin_export: '插件导出',
    device_group: '设备分组',
    device_group_query: '设备分组查询',
    device_group_create: '设备分组创建',
    device_group_update: '设备分组更新',
    device_group_delete: '设备分组删除',
    device_import: '设备导入',
    product_thing_model: '产品物模型',
    product_thing_model_function_query: '产品物模型功能查询',
    product_thing_model_function_create: '产品物模型功能创建',
    product_thing_model_function_update: '产品物模型功能更新',
    product_thing_model_function_delete: '产品物模型功能删除',
    product_thing_model_function_export: '产品物模型功能导出',
    device_uplink: '设备上行',
    device_attribute_query: '设备属性查询',
    device_log_query: '设备日志查询',
    device_downlink: '设备下行',
    ops_management: '运维管理',
    rule_engine: '规则引擎',
    scene_linkage: '场景联动',
    iot_home_page: 'IoT首页',
    data_bridge: '数据桥梁',
    iot_data_bridge_query: 'IoT 数据桥梁查询',
    iot_data_bridge_create: 'IoT 数据桥梁创建',
    iot_data_bridge_update: 'IoT 数据桥梁更新',
    iot_data_bridge_delete: 'IoT 数据桥梁删除',
    iot_data_bridge_export: 'IoT 数据桥梁导出',
    ai_workflow: 'AI 工作流',
    ai_workflow_query: 'AI 工作流查询',
    ai_workflow_create: 'AI 工作流创建',
    ai_workflow_update: 'AI 工作流更新',
    ai_workflow_delete: 'AI 工作流删除',
    ai_workflow_test: 'AI 工作流测试',
    workbench: '工作台',
    vehicle_management: '车辆管理',
    site_management: '站点管理',
    order_management: '订单管理',
    vehicle_delete: '车辆删除',
    vehicle_add: '车辆新增',
    vehicle_export: '车辆导出',
    vehicle_update: '车辆更新',
    vehicle_unbind: '车辆解绑',
    edit: '编辑',
    enable_disable: '启用/禁用',
    show_hide: '显示/隐藏',
    details: '详情',
    import: '导入',

  },
  analysis: {
    newUser: '新增用户',
    unreadInformation: '未读消息',
    transactionAmount: '成交金额',
    totalShopping: '购物总量',
    monthlySales: '每月销售额',
    userAccessSource: '用户访问来源',
    january: '一月',
    february: '二月',
    march: '三月',
    april: '四月',
    may: '五月',
    june: '六月',
    july: '七月',
    august: '八月',
    september: '九月',
    october: '十月',
    november: '十一月',
    december: '十二月',
    estimate: '预计',
    actual: '实际',
    directAccess: '直接访问',
    mailMarketing: '邮件营销',
    allianceAdvertising: '联盟广告',
    videoAdvertising: '视频广告',
    searchEngines: '搜索引擎',
    weeklyUserActivity: '每周用户活跃量',
    activeQuantity: '活跃量',
    monday: '周一',
    tuesday: '周二',
    wednesday: '周三',
    thursday: '周四',
    friday: '周五',
    saturday: '周六',
    sunday: '周日'
  },
  workplace: {
    welcome: '你好',
    happyDay: '祝你开心每一天!',
    toady: '今日晴',
    notice: '通知公告',
    project: '项目数',
    access: '项目访问',
    toDo: '待办',
    introduction: '一个正经的简介',
    shortcutOperation: '快捷入口',
    operation: '操作',
    index: '指数',
    personal: '个人',
    team: '团队',
    quote: '引用',
    contribution: '贡献',
    hot: '热度',
    yield: '产量',
    dynamic: '动态',
    push: '推送',
    follow: '关注'
  },
  form: {
    input: '输入框',
    inputNumber: '数字输入框',
    default: '默认',
    icon: '图标',
    mixed: '复合型',
    textarea: '多行文本',
    slot: '插槽',
    position: '位置',
    autocomplete: '自动补全',
    select: '选择器',
    selectGroup: '选项分组',
    selectV2: '虚拟列表选择器',
    cascader: '级联选择器',
    switch: '开关',
    rate: '评分',
    colorPicker: '颜色选择器',
    transfer: '穿梭框',
    render: '渲染器',
    radio: '单选框',
    button: '按钮',
    checkbox: '多选框',
    slider: '滑块',
    datePicker: '日期选择器',
    shortcuts: '快捷选项',
    today: '今天',
    yesterday: '昨天',
    aWeekAgo: '一周前',
    week: '周',
    year: '年',
    month: '月',
    dates: '日期',
    daterange: '日期范围',
    monthrange: '月份范围',
    dateTimePicker: '日期时间选择器',
    dateTimerange: '日期时间范围',
    timePicker: '时间选择器',
    timeSelect: '时间选择',
    inputPassword: '密码输入框',
    passwordStrength: '密码强度',
    operate: '操作',
    change: '更改',
    restore: '还原',
    disabled: '禁用',
    disablement: '解除禁用',
    delete: '删除',
    add: '添加',
    setValue: '设置值',
    resetValue: '重置值',
    set: '设置',
    subitem: '子项',
    formValidation: '表单验证',
    verifyReset: '验证重置',
    remark: '备注'
  },
  watermark: {
    watermark: '水印'
  },
  table: {
    table: '表格',
    index: '序号',
    title: '标题',
    author: '作者',
    createTime: '创建时间',
    action: '操作',
    pagination: '分页',
    reserveIndex: '叠加序号',
    restoreIndex: '还原序号',
    showSelections: '显示多选',
    hiddenSelections: '隐藏多选',
    showExpandedRows: '显示展开行',
    hiddenExpandedRows: '隐藏展开行',
    header: '头部'
  },
  action: {
    create: '新增',
    add: '新增',
    del: '删除',
    delete: '删除',
    edit: '编辑',
    update: '编辑',
    preview: '预览',
    more: '更多',
    sync: '同步',
    save: '保存',
    detail: '详情',
    export: '导出',
    import: '导入',
    generate: '生成',
    logout: '强制退出',
    test: '测试',
    typeCreate: '字典类型新增',
    typeUpdate: '字典类型编辑',
    dataCreate: '字典数据新增',
    dataUpdate: '字典数据编辑'
  },
  dialog: {
    dialog: '弹窗',
    open: '打开',
    close: '关闭'
  },
  sys: {
    api: {
      operationFailed: '操作失败',
      errorTip: '错误提示',
      errorMessage: '操作失败,系统异常!',
      timeoutMessage: '登录超时,请重新登录!',
      apiTimeoutMessage: '接口请求超时,请刷新页面重试!',
      apiRequestFailed: '请求出错，请稍候重试',
      networkException: '网络异常',
      networkExceptionMsg: '网络异常，请检查您的网络连接是否正常!',
      errMsg401: '用户没有权限（令牌、用户名、密码错误）!',
      errMsg403: '用户得到授权，但是访问是被禁止的。!',
      errMsg404: '网络请求错误,未找到该资源!',
      errMsg405: '网络请求错误,请求方法未允许!',
      errMsg408: '网络请求超时!',
      errMsg500: '服务器错误,请联系管理员!',
      errMsg501: '网络未实现!',
      errMsg502: '网络错误!',
      errMsg503: '服务不可用，服务器暂时过载或维护!',
      errMsg504: '网络超时!',
      errMsg505: 'http版本不支持该请求!',
      errMsg901: '演示模式，无法进行写操作!'
    },
    app: {
      logoutTip: '温馨提醒',
      logoutMessage: '是否确认退出系统?',
      menuLoading: '菜单加载中...'
    },
    exception: {
      backLogin: '返回登录',
      backHome: '返回首页',
      subTitle403: '抱歉，您无权访问此页面。',
      subTitle404: '抱歉，您访问的页面不存在。',
      subTitle500: '抱歉，服务器报告错误。',
      noDataTitle: '当前页无数据',
      networkErrorTitle: '网络错误',
      networkErrorSubTitle: '抱歉，您的网络连接已断开，请检查您的网络！'
    },
    lock: {
      unlock: '点击解锁',
      alert: '锁屏密码错误',
      backToLogin: '返回登录',
      entry: '进入系统',
      placeholder: '请输入锁屏密码或者用户密码'
    },
    login: {
      backSignIn: '返回',
      signInFormTitle: '登录',
      ssoFormTitle: '三方授权',
      mobileSignInFormTitle: '手机登录',
      qrSignInFormTitle: '二维码登录',
      signUpFormTitle: '注册',
      forgetFormTitle: '重置密码',
      signInTitle: 'YDYX中后台管理系统',
      signInDesc: '输入您的个人详细信息开始使用！',
      policy: '我同意xxx隐私政策',
      scanSign: `扫码后点击"确认"，即可完成登录`,
      loginButton: '登录',
      registerButton: '注册',
      rememberMe: '记住我',
      forgetPassword: '忘记密码?',
      otherSignIn: '其他登录方式',
      // notify
      loginSuccessTitle: '登录成功',
      loginSuccessDesc: '欢迎回来',
      // placeholder
      accountPlaceholder: '请输入账号',
      passwordPlaceholder: '请输入密码',
      smsPlaceholder: '请输入验证码',
      mobilePlaceholder: '请输入手机号码',
      policyPlaceholder: '勾选后才能注册',
      diffPwd: '两次输入密码不一致',
      userName: '账号',
      password: '密码',
      confirmPassword: '确认密码',
      email: '邮箱',
      smsCode: '短信验证码',
      mobile: '手机号码'
    }
  },
  profile: {
    user: {
      title: '个人信息',
      username: '用户名称',
      nickname: '用户昵称',
      mobile: '手机号码',
      email: '用户邮箱',
      dept: '所属部门',
      posts: '所属岗位',
      roles: '所属角色',
      sex: '性别',
      man: '男',
      woman: '女',
      createTime: '创建日期'
    },
    info: {
      title: '基本信息',
      basicInfo: '基本设置',
      resetPwd: '密码设置',
      userSocial: '社交绑定'
    },
    rules: {
      nickname: '请输入用户昵称',
      mail: '请输入邮箱地址',
      truemail: '请输入正确的邮箱地址',
      phone: '请输入正确的手机号码',
      truephone: '请输入正确的手机号码'
    },
    password: {
      oldPassword: '旧密码',
      newPassword: '新密码',
      confirmPassword: '确认密码',
      oldPwdMsg: '请输入旧密码',
      newPwdMsg: '请输入新密码',
      cfPwdMsg: '请输入确认密码',
      pwdRules: '长度在 6 到 20 个字符',
      diffPwd: '两次输入密码不一致'
    }
  },
  cropper: {
    selectImage: '选择图片',
    uploadSuccess: '上传成功',
    modalTitle: '头像上传',
    okText: '确认并上传',
    btn_reset: '重置',
    btn_rotate_left: '逆时针旋转',
    btn_rotate_right: '顺时针旋转',
    btn_scale_x: '水平翻转',
    btn_scale_y: '垂直翻转',
    btn_zoom_in: '放大',
    btn_zoom_out: '缩小',
    preview: '预览'
  },
  'OAuth 2.0': 'OAuth 2.0' // 避免菜单名是 OAuth 2.0 时，一直 warn 报错
}
