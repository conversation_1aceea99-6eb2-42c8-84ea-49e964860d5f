<template>
  <doc-alert title="Line Pay 支付接入" url="/src/views/pay/order/LinePay接入文档.md" />
  
  <!-- 操作工具栏 -->
  <el-row :gutter="10" class="mb8">
    <el-col :span="1.5">
      <el-button type="primary" plain @click="openForm">
        <Icon icon="ep:plus" />发起 Line Pay 支付
      </el-button>
    </el-col>
  </el-row>

  <!-- Line Pay 支付演示 -->
  <ContentWrap>
    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <span>Line Pay 支付演示</span>
        </div>
      </template>
      
      <el-form :model="demoForm" label-width="120px">
        <el-form-item label="支付方式">
          <el-radio-group v-model="demoForm.paymentMethod">
            <el-radio value="line_pay_web">Line Pay 网页支付</el-radio>
            <el-radio value="line_pay_app">Line Pay App 支付</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="商品名称">
          <el-input v-model="demoForm.subject" placeholder="请输入商品名称" />
        </el-form-item>
        
        <el-form-item label="商品描述">
          <el-input v-model="demoForm.body" placeholder="请输入商品描述" />
        </el-form-item>
        
        <el-form-item label="支付金额">
          <el-input-number 
            v-model="demoForm.price" 
            :min="1" 
            :max="999999"
            :precision="2"
            placeholder="请输入支付金额"
          />
          <span class="ml-2">元</span>
        </el-form-item>
        
        <el-form-item label="货币类型">
          <el-select v-model="demoForm.currency" placeholder="请选择货币类型">
            <el-option label="台币 (TWD)" value="TWD" />
            <el-option label="日元 (JPY)" value="JPY" />
            <el-option label="韩元 (KRW)" value="KRW" />
            <el-option label="美元 (USD)" value="USD" />
            <el-option label="泰铢 (THB)" value="THB" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="createPayment" :loading="loading">
            创建支付订单
          </el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 支付结果展示 -->
    <el-card v-if="paymentResult" class="demo-card mt-4">
      <template #header>
        <div class="card-header">
          <span>支付结果</span>
        </div>
      </template>
      
      <el-descriptions :column="2" border>
        <el-descriptions-item label="订单号">
          {{ paymentResult.orderNo }}
        </el-descriptions-item>
        <el-descriptions-item label="支付状态">
          <el-tag :type="getStatusType(paymentResult.status)">
            {{ getStatusText(paymentResult.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="支付金额">
          {{ (paymentResult.price / 100).toFixed(2) }} {{ demoForm.currency }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ formatTime(paymentResult.createTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="支付链接" v-if="paymentResult.payUrl">
          <el-link :href="paymentResult.payUrl" target="_blank" type="primary">
            点击支付
          </el-link>
        </el-descriptions-item>
      </el-descriptions>
      
      <div class="mt-4" v-if="paymentResult.payUrl">
        <el-button type="primary" @click="openPayment">
          <Icon icon="ep:link" />
          打开支付页面
        </el-button>
        <el-button @click="checkPaymentStatus">
          <Icon icon="ep:refresh" />
          检查支付状态
        </el-button>
      </div>
    </el-card>
  </ContentWrap>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import * as PayDemoApi from '@/api/pay/demo'
import { formatDate } from '@/utils/formatTime'

defineOptions({ name: 'LinePayDemo' })

const loading = ref(false)
const paymentResult = ref(null)

const demoForm = reactive({
  paymentMethod: 'line_pay_web',
  subject: 'Line Pay 测试商品',
  body: '这是一个 Line Pay 支付测试订单',
  price: 100.00,
  currency: 'TWD'
})

/** 创建支付订单 */
const createPayment = async () => {
  if (!demoForm.subject || !demoForm.price) {
    ElMessage.warning('请填写完整的支付信息')
    return
  }
  
  loading.value = true
  try {
    const params = {
      spuId: 1, // 模拟商品ID
      skuId: 1, // 模拟SKU ID
      payPrice: Math.round(demoForm.price * 100), // 转换为分
      subject: demoForm.subject,
      body: demoForm.body,
      paymentMethod: demoForm.paymentMethod
    }
    
    const result = await PayDemoApi.createDemoOrder(params)
    paymentResult.value = result
    
    ElMessage.success('支付订单创建成功')
  } catch (error) {
    console.error('创建支付订单失败:', error)
    ElMessage.error('创建支付订单失败')
  } finally {
    loading.value = false
  }
}

/** 打开支付页面 */
const openPayment = () => {
  if (paymentResult.value?.payUrl) {
    window.open(paymentResult.value.payUrl, '_blank')
  }
}

/** 检查支付状态 */
const checkPaymentStatus = async () => {
  if (!paymentResult.value?.id) return
  
  try {
    const result = await PayDemoApi.getDemoOrder(paymentResult.value.id)
    paymentResult.value = result
    ElMessage.success('支付状态已更新')
  } catch (error) {
    ElMessage.error('检查支付状态失败')
  }
}

/** 重置表单 */
const resetForm = () => {
  demoForm.subject = 'Line Pay 测试商品'
  demoForm.body = '这是一个 Line Pay 支付测试订单'
  demoForm.price = 100.00
  demoForm.currency = 'TWD'
  demoForm.paymentMethod = 'line_pay_web'
  paymentResult.value = null
}

/** 获取状态类型 */
const getStatusType = (status: number) => {
  switch (status) {
    case 0: return 'warning' // 待支付
    case 10: return 'success' // 支付成功
    case 20: return 'info' // 已退款
    case 30: return 'danger' // 支付关闭
    default: return 'info'
  }
}

/** 获取状态文本 */
const getStatusText = (status: number) => {
  switch (status) {
    case 0: return '待支付'
    case 10: return '支付成功'
    case 20: return '已退款'
    case 30: return '支付关闭'
    default: return '未知状态'
  }
}

/** 格式化时间 */
const formatTime = (time: string) => {
  return formatDate(new Date(time))
}

/** 打开表单弹窗 */
const openForm = () => {
  resetForm()
}
</script>

<style scoped>
.demo-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mt-4 {
  margin-top: 16px;
}
</style>
