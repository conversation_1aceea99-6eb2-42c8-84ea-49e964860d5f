# Line Pay 支付接入文档

## 1. 概述

Line Pay 是 LINE 公司推出的移动支付服务，主要在亚洲地区（台湾、日本、韩国、泰国等）广泛使用。本文档介绍如何在芋道云系统中集成 Line Pay 支付功能。

## 2. 支持的支付方式

- **Line Pay 网页支付**: 适用于 PC 和移动端网页
- **Line Pay App 支付**: 适用于移动应用内支付

## 3. 前置条件

### 3.1 申请 Line Pay 商户账号

1. 访问 [Line Pay Console](https://pay.line.me/portal/tw/main)
2. 注册商户账号并完成认证
3. 创建支付应用，获取以下信息：
   - Channel ID
   - Channel Secret
   - Merchant ID

### 3.2 配置回调地址

在 Line Pay Console 中配置以下回调地址：
- 支付回调: `https://your-domain.com/admin-api/pay/notify/order/{channelId}`
- 退款回调: `https://your-domain.com/admin-api/pay/notify/refund/{channelId}`

## 4. 系统配置

### 4.1 创建支付应用

1. 登录芋道云管理后台
2. 进入 `支付管理` -> `应用信息`
3. 点击 `新增` 创建支付应用
4. 填写应用信息并保存

### 4.2 配置 Line Pay 支付渠道

1. 在应用列表中找到对应应用
2. 点击 Line Pay 配置列下的红色按钮
3. 填写 Line Pay 配置信息：

#### 沙箱环境配置示例
```json
{
  "env": "sandbox",
  "channelId": "your_sandbox_channel_id",
  "channelSecret": "your_sandbox_channel_secret", 
  "merchantId": "your_sandbox_merchant_id",
  "apiBaseUrl": "https://sandbox-api-pay.line.me",
  "confirmUrl": "https://your-domain.com/pay/success",
  "cancelUrl": "https://your-domain.com/pay/cancel",
  "currency": "TWD"
}
```

#### 生产环境配置示例
```json
{
  "env": "prod",
  "channelId": "your_prod_channel_id",
  "channelSecret": "your_prod_channel_secret",
  "merchantId": "your_prod_merchant_id", 
  "apiBaseUrl": "https://api-pay.line.me",
  "confirmUrl": "https://your-domain.com/pay/success",
  "cancelUrl": "https://your-domain.com/pay/cancel",
  "currency": "TWD"
}
```

### 4.3 配置参数说明

| 参数名 | 必填 | 说明 | 示例 |
|--------|------|------|------|
| env | 是 | 环境类型 | sandbox/prod |
| channelId | 是 | Line Pay 提供的 Channel ID | 1234567890 |
| channelSecret | 是 | Line Pay 提供的 Channel Secret | abcdef123456 |
| merchantId | 是 | 商户ID | merchant_123 |
| apiBaseUrl | 是 | API 基础地址 | 自动设置 |
| confirmUrl | 是 | 支付成功回调URL | https://your-domain.com/pay/success |
| cancelUrl | 是 | 支付取消回调URL | https://your-domain.com/pay/cancel |
| currency | 否 | 货币代码 | TWD（默认） |

## 5. 支持的货币

- TWD (台币)
- JPY (日元)  
- KRW (韩元)
- USD (美元)
- THB (泰铢)

## 6. 测试流程

### 6.1 沙箱测试

1. 使用沙箱环境配置
2. 创建测试订单
3. 选择 Line Pay 支付方式
4. 使用 Line 官方提供的测试账号完成支付

### 6.2 生产环境测试

1. 切换到生产环境配置
2. 使用真实的 Line 账号进行小额测试
3. 验证支付和退款流程

## 7. 常见问题

### 7.1 支付失败

**问题**: 支付时提示"商户未找到"
**解决**: 检查 merchantId 配置是否正确

**问题**: 支付时提示"签名验证失败"  
**解决**: 检查 channelSecret 配置是否正确

### 7.2 回调问题

**问题**: 支付成功但订单状态未更新
**解决**: 检查回调地址配置和网络连通性

### 7.3 货币问题

**问题**: 支付时提示"货币不支持"
**解决**: 确认使用的货币代码在支持列表中

## 8. 技术支持

如遇到技术问题，可通过以下方式获取支持：

1. 查看 Line Pay 官方文档
2. 联系 Line Pay 技术支持
3. 在芋道云社区提问

## 9. 相关链接

- [Line Pay 官方文档](https://pay.line.me/documents/online_v3_zh_TW.html)
- [Line Pay Console](https://pay.line.me/portal/tw/main)
- [芋道云官方文档](https://doc.iocoder.cn/)
