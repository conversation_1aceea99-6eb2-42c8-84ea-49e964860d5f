<template>
  <doc-alert title="Line Pay 支付" url="/src/views/pay/order/LinePay接入文档.md" />
  
  <!-- 搜索 -->
  <ContentWrap>
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="68px"
    >
      <el-form-item label="应用编号" prop="appId">
        <el-select
          v-model="queryParams.appId"
          class="!w-240px"
          clearable
          placeholder="请选择应用信息"
        >
          <el-option
            v-for="item in appList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="渠道编码" prop="channelCode">
        <el-select
          v-model="queryParams.channelCode"
          class="!w-240px"
          clearable
          placeholder="请选择渠道编码"
        >
          <el-option label="Line Pay 网页支付" value="line_pay_web" />
          <el-option label="Line Pay App 支付" value="line_pay_app" />
        </el-select>
      </el-form-item>
      <el-form-item label="商户订单号" prop="merchantOrderId">
        <el-input
          v-model="queryParams.merchantOrderId"
          class="!w-240px"
          clearable
          placeholder="请输入商户订单号"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="渠道订单号" prop="channelOrderNo">
        <el-input
          v-model="queryParams.channelOrderNo"
          class="!w-240px"
          clearable
          placeholder="请输入渠道订单号"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="支付状态" prop="status">
        <el-select
          v-model="queryParams.status"
          class="!w-240px"
          clearable
          placeholder="请选择支付状态"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.PAY_ORDER_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="退款状态" prop="refundStatus">
        <el-select
          v-model="queryParams.refundStatus"
          class="!w-240px"
          clearable
          placeholder="请选择退款状态"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.PAY_ORDER_REFUND_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" />
          重置
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['pay:order:export']"
        >
          <Icon icon="ep:download" class="mr-5px" />
          导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <el-table-column label="订单编号" align="center" prop="id" width="120" />
      <el-table-column label="应用名称" align="center" prop="appName" width="120" />
      <el-table-column label="渠道编码" align="center" prop="channelCode" width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.PAY_CHANNEL_CODE" :value="scope.row.channelCode" />
        </template>
      </el-table-column>
      <el-table-column
        label="商户订单号"
        align="center"
        prop="merchantOrderId"
        width="160"
        show-overflow-tooltip
      />
      <el-table-column
        label="渠道订单号"
        align="center"
        prop="channelOrderNo"
        width="160"
        show-overflow-tooltip
      />
      <el-table-column label="商品标题" align="center" prop="subject" width="120" />
      <el-table-column label="支付金额" align="center" prop="amount" width="100">
        <template #default="scope">
          ¥{{ (scope.row.amount / 100.0).toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column label="手续费" align="center" prop="channelFeeAmount" width="100">
        <template #default="scope">
          ¥{{ (scope.row.channelFeeAmount / 100.0).toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column label="支付状态" align="center" prop="status" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.PAY_ORDER_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="退款状态" align="center" prop="refundStatus" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.PAY_ORDER_REFUND_STATUS" :value="scope.row.refundStatus" />
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="180"
        :formatter="dateFormatter"
      />
      <el-table-column
        label="支付时间"
        align="center"
        prop="successTime"
        width="180"
        :formatter="dateFormatter"
      />
      <el-table-column label="操作" align="center" fixed="right" width="110">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openDetail(scope.row)"
            v-hasPermi="['pay:order:query']"
          >
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：详情 -->
  <LinePayOrderDetail ref="detailRef" />
</template>

<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import * as LinePayApi from '@/api/pay/linepay'
import * as AppApi from '@/api/pay/app'
import LinePayOrderDetail from './components/LinePayOrderDetail.vue'

defineOptions({ name: 'PayLinePayOrder' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  appId: undefined,
  channelCode: undefined,
  merchantOrderId: undefined,
  channelOrderNo: undefined,
  status: undefined,
  refundStatus: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const appList = ref([]) // 支付应用列表

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await LinePayApi.getLinePayOrderPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await LinePayApi.exportLinePayOrder(queryParams)
    download.excel(data, 'Line Pay 支付订单.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 详情操作 */
const detailRef = ref()
const openDetail = (data: LinePayApi.LinePayOrderVO) => {
  detailRef.value.open(data)
}

/** 初始化 **/
onMounted(async () => {
  await getList()
  // 加载应用列表
  appList.value = await AppApi.getAppSimpleList()
})
</script>
