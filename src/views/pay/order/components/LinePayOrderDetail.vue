<template>
  <Dialog v-model="dialogVisible" title="Line Pay 订单详情" width="800px">
    <el-descriptions :column="2" label-class-name="desc-label">
      <!-- 基本订单信息 -->
      <el-descriptions-item label="订单编号">
        <el-tag size="small">{{ detailData.id }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="商户订单号">
        <el-tag size="small">{{ detailData.merchantOrderId }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="应用编号">{{ detailData.appId }}</el-descriptions-item>
      <el-descriptions-item label="渠道编号">{{ detailData.channelId }}</el-descriptions-item>
      <el-descriptions-item label="渠道编码">
        <dict-tag :type="DICT_TYPE.PAY_CHANNEL_CODE" :value="detailData.channelCode" size="small" />
      </el-descriptions-item>
      <el-descriptions-item label="支付状态">
        <dict-tag :type="DICT_TYPE.PAY_ORDER_STATUS" :value="detailData.status" size="small" />
      </el-descriptions-item>
      
      <!-- 金额信息 -->
      <el-descriptions-item label="支付金额">
        <el-tag type="success" size="small">¥{{ (detailData.amount / 100.0).toFixed(2) }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="手续费率">
        {{ (detailData.channelFeeRate / 100.0).toFixed(2) }}%
      </el-descriptions-item>
      <el-descriptions-item label="手续费金额">
        <el-tag type="warning" size="small">
          ¥{{ (detailData.channelFeeAmount / 100.0).toFixed(2) }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="退款状态">
        <dict-tag :type="DICT_TYPE.PAY_ORDER_REFUND_STATUS" :value="detailData.refundStatus" size="small" />
      </el-descriptions-item>
      
      <!-- 退款信息 -->
      <el-descriptions-item label="退款次数">{{ detailData.refundTimes || 0 }}</el-descriptions-item>
      <el-descriptions-item label="退款金额">
        <el-tag type="info" size="small" v-if="detailData.refundAmount">
          ¥{{ (detailData.refundAmount / 100.0).toFixed(2) }}
        </el-tag>
        <span v-else>-</span>
      </el-descriptions-item>
      
      <!-- 时间信息 -->
      <el-descriptions-item label="创建时间">
        {{ formatDate(detailData.createTime) }}
      </el-descriptions-item>
      <el-descriptions-item label="支付时间">
        {{ formatDate(detailData.successTime) }}
      </el-descriptions-item>
      <el-descriptions-item label="过期时间">
        {{ formatDate(detailData.expireTime) }}
      </el-descriptions-item>
      <el-descriptions-item label="通知时间">
        {{ formatDate(detailData.notifyTime) }}
      </el-descriptions-item>
    </el-descriptions>
    
    <!-- 分割线 -->
    <el-divider />
    
    <!-- 商品和渠道信息 -->
    <el-descriptions :column="2" label-class-name="desc-label">
      <el-descriptions-item label="商品标题">{{ detailData.subject }}</el-descriptions-item>
      <el-descriptions-item label="商品描述">{{ detailData.body || '-' }}</el-descriptions-item>
      <el-descriptions-item label="用户IP">{{ detailData.userIp || '-' }}</el-descriptions-item>
      <el-descriptions-item label="渠道用户ID">{{ detailData.channelUserId || '-' }}</el-descriptions-item>
      <el-descriptions-item label="渠道订单号">
        <el-tag size="small" type="success" v-if="detailData.channelOrderNo">
          {{ detailData.channelOrderNo }}
        </el-tag>
        <span v-else>-</span>
      </el-descriptions-item>
      <el-descriptions-item label="成功扩展ID">{{ detailData.successExtensionId || '-' }}</el-descriptions-item>
    </el-descriptions>
    
    <!-- 分割线 -->
    <el-divider />
    
    <!-- URL信息 -->
    <el-descriptions :column="1" label-class-name="desc-label">
      <el-descriptions-item label="通知地址">
        <el-input v-model="detailData.notifyUrl" readonly type="textarea" :rows="2" />
      </el-descriptions-item>
      <el-descriptions-item label="返回地址">
        <el-input v-model="detailData.returnUrl" readonly type="textarea" :rows="2" />
      </el-descriptions-item>
    </el-descriptions>
  </Dialog>
</template>

<script lang="ts" setup>
import { DICT_TYPE } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import * as LinePayApi from '@/api/pay/linepay'

defineOptions({ name: 'LinePayOrderDetail' })

const dialogVisible = ref(false) // 弹窗的是否展示
const detailLoading = ref(false) // 表单的加载中
const detailData = ref<LinePayApi.LinePayOrderVO>({
  id: 0,
  appId: 0,
  channelId: 0,
  channelCode: '',
  merchantOrderId: '',
  subject: '',
  body: '',
  notifyUrl: '',
  returnUrl: '',
  amount: 0,
  channelFeeRate: 0,
  channelFeeAmount: 0,
  status: 0,
  userIp: '',
  expireTime: new Date(),
  successTime: new Date(),
  notifyTime: new Date(),
  successExtensionId: 0,
  refundStatus: 0,
  refundTimes: 0,
  refundAmount: 0,
  channelUserId: '',
  channelOrderNo: '',
  createTime: new Date()
})

/** 打开弹窗 */
const open = async (data: LinePayApi.LinePayOrderVO) => {
  dialogVisible.value = true
  // 设置数据
  detailLoading.value = true
  try {
    // 如果传入的是完整数据，直接使用；否则通过API获取详情
    if (data && data.id) {
      if (data.subject && data.merchantOrderId) {
        // 数据完整，直接使用
        detailData.value = data
      } else {
        // 数据不完整，通过API获取详情
        detailData.value = await LinePayApi.getLinePayOrder(data.id)
      }
    }
  } finally {
    detailLoading.value = false
  }
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>

<style scoped>
.desc-label {
  font-weight: bold;
}
</style>
