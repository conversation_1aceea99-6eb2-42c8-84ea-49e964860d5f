<template>
  <div>
    <Dialog v-model="dialogVisible" :title="dialogTitle" width="830px" @closed="close">
      <el-form
        ref="formRef"
        v-loading="formLoading"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="渠道费率" label-width="180px" prop="feeRate">
          <el-input v-model="formData.feeRate" clearable placeholder="请输入渠道费率">
            <template #append>%</template>
          </el-input>
        </el-form-item>
        
        <el-form-item label="渠道状态" label-width="180px" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio
              v-for="dict in getDictOptions(DICT_TYPE.COMMON_STATUS)"
              :key="parseInt(dict.value)"
              :value="parseInt(dict.value)"
            >
              {{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="环境类型" label-width="180px" prop="config.env">
          <el-radio-group v-model="formData.config.env">
            <el-radio value="sandbox">沙箱环境</el-radio>
            <el-radio value="prod">生产环境</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="Channel ID" label-width="180px" prop="config.channelId">
          <el-input 
            v-model="formData.config.channelId" 
            clearable 
            placeholder="请输入 Line Pay Channel ID" 
          />
        </el-form-item>

        <el-form-item label="Channel Secret" label-width="180px" prop="config.channelSecret">
          <el-input 
            v-model="formData.config.channelSecret" 
            clearable 
            placeholder="请输入 Line Pay Channel Secret"
            type="password"
            show-password
          />
        </el-form-item>

        <el-form-item label="商户ID" label-width="180px" prop="config.merchantId">
          <el-input 
            v-model="formData.config.merchantId" 
            clearable 
            placeholder="请输入商户ID" 
          />
        </el-form-item>

        <el-form-item label="API Base URL" label-width="180px" prop="config.apiBaseUrl">
          <el-input 
            v-model="formData.config.apiBaseUrl" 
            clearable 
            placeholder="API 基础地址"
            :disabled="true"
          />
        </el-form-item>

        <el-form-item label="支付成功回调URL" label-width="180px" prop="config.confirmUrl">
          <el-input 
            v-model="formData.config.confirmUrl" 
            clearable 
            placeholder="请输入支付成功回调URL" 
          />
        </el-form-item>

        <el-form-item label="支付取消回调URL" label-width="180px" prop="config.cancelUrl">
          <el-input 
            v-model="formData.config.cancelUrl" 
            clearable 
            placeholder="请输入支付取消回调URL" 
          />
        </el-form-item>

        <el-form-item label="货币代码" label-width="180px" prop="config.currency">
          <el-select v-model="formData.config.currency" placeholder="请选择货币代码">
            <el-option label="台币 (TWD)" value="TWD" />
            <el-option label="日元 (JPY)" value="JPY" />
            <el-option label="韩元 (KRW)" value="KRW" />
            <el-option label="美元 (USD)" value="USD" />
            <el-option label="泰铢 (THB)" value="THB" />
          </el-select>
        </el-form-item>

        <el-form-item label="备注" label-width="180px" prop="remark">
          <el-input v-model="formData.remark" :style="{ width: '100%' }" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </template>
    </Dialog>
  </div>
</template>

<script lang="ts" setup>
import { CommonStatusEnum } from '@/utils/constants'
import { DICT_TYPE, getDictOptions } from '@/utils/dict'
import * as ChannelApi from '@/api/pay/channel'

defineOptions({ name: 'LinePayChannelForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref({
  id: undefined,
  code: '',
  config: {
    env: 'sandbox',
    channelId: '',
    channelSecret: '',
    merchantId: '',
    apiBaseUrl: '',
    confirmUrl: '',
    cancelUrl: '',
    currency: 'TWD'
  },
  feeRate: undefined,
  remark: '',
  status: CommonStatusEnum.ENABLE,
  appId: undefined
})
const formRules = reactive({
  feeRate: [{ required: true, message: '渠道费率不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '渠道状态不能为空', trigger: 'change' }],
  'config.env': [{ required: true, message: '环境类型不能为空', trigger: 'change' }],
  'config.channelId': [{ required: true, message: 'Channel ID不能为空', trigger: 'blur' }],
  'config.channelSecret': [{ required: true, message: 'Channel Secret不能为空', trigger: 'blur' }],
  'config.merchantId': [{ required: true, message: '商户ID不能为空', trigger: 'blur' }],
  'config.confirmUrl': [{ required: true, message: '支付成功回调URL不能为空', trigger: 'blur' }],
  'config.cancelUrl': [{ required: true, message: '支付取消回调URL不能为空', trigger: 'blur' }],
  'config.currency': [{ required: true, message: '货币代码不能为空', trigger: 'change' }]
})

const formRef = ref() // 表单 Ref

// 监听环境类型变化，自动设置API Base URL
watch(() => formData.value.config.env, (newEnv) => {
  if (newEnv === 'sandbox') {
    formData.value.config.apiBaseUrl = 'https://sandbox-api-pay.line.me'
  } else if (newEnv === 'prod') {
    formData.value.config.apiBaseUrl = 'https://api-pay.line.me'
  }
}, { immediate: true })

/** 打开弹窗 */
const open = async (appId, code) => {
  dialogVisible.value = true
  formLoading.value = true
  resetForm(appId, code)
  // 加载数据
  try {
    const data = await ChannelApi.getChannel(appId, code)
    if (data && data.id) {
      formData.value = data
      formData.value.config = JSON.parse(data.config)
    }
    dialogTitle.value = !formData.value.id ? '创建 Line Pay 支付渠道' : '编辑 Line Pay 支付渠道'
  } finally {
    formLoading.value = false
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = { ...formData.value } as unknown as ChannelApi.ChannelVO
    data.config = JSON.stringify(formData.value.config)
    if (!data.id) {
      await ChannelApi.createChannel(data)
      message.success(t('common.createSuccess'))
    } else {
      await ChannelApi.updateChannel(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = (appId, code) => {
  formData.value = {
    id: undefined,
    code: code,
    config: {
      env: 'sandbox',
      channelId: '',
      channelSecret: '',
      merchantId: '',
      apiBaseUrl: 'https://sandbox-api-pay.line.me',
      confirmUrl: '',
      cancelUrl: '',
      currency: 'TWD'
    },
    feeRate: undefined,
    remark: '',
    status: CommonStatusEnum.ENABLE,
    appId: appId
  }
}

/** 关闭弹窗 */
const close = () => {
  formRef.value?.resetFields()
}
</script>
