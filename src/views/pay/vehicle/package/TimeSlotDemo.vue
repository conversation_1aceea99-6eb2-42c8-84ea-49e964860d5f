<template>
  <div class="time-slot-demo">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>时间段选择器演示 - 优化版本</span>
          <el-button type="primary" @click="openDialog">
            <el-icon><Plus /></el-icon>
            打开弹窗测试
          </el-button>
        </div>
      </template>
      
      <div class="demo-section">
        <h3>独立组件演示</h3>
        <TimeSlotSelector
          v-model="selectedTimeSlots"
          @change="handleTimeSlotsChange"
        />
      </div>

      <div class="demo-section">
        <h3>选择结果</h3>
        <el-card class="result-card">
          <div class="result-item">
            <strong>选中的时间段数量：</strong>{{ selectedTimeSlots.length }}
          </div>
          <div class="result-item">
            <strong>JSON格式：</strong>
            <pre>{{ jsonResult }}</pre>
          </div>
          <div class="result-item">
            <strong>时间段列表：</strong>
            <ul v-if="selectedTimeSlots.length > 0">
              <li v-for="(slot, index) in selectedTimeSlots" :key="index">
                {{ slot.startTime }} ~ {{ slot.endTime }}
              </li>
            </ul>
            <span v-else class="text-gray-400">暂无选择</span>
          </div>
        </el-card>
      </div>

      <div class="demo-section">
        <h3>操作按钮</h3>
        <el-button-group>
          <el-button @click="loadTestData">加载测试数据</el-button>
          <el-button @click="clearAll">清空选择</el-button>
          <el-button @click="validateTimeSlots">验证时间段</el-button>
        </el-button-group>
      </div>

      <div v-if="validationResult" class="demo-section">
        <h3>验证结果</h3>
        <el-alert 
          :title="validationResult.title"
          :type="validationResult.type"
          :description="validationResult.message"
          show-icon
        />
      </div>
    </el-card>

    <!-- 弹窗测试 -->
    <VehiclePackageForm
      ref="formRef"
      @success="handleSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import TimeSlotSelector from '@/components/TimeSlotSelector/index.vue'
import VehiclePackageForm from './VehiclePackageForm.vue'
import type { TimeSlot } from '@/api/pay/vehicle/package'

/** 时间段选择器演示 */
defineOptions({ name: 'TimeSlotDemo' })

// 响应式数据
const selectedTimeSlots = ref<TimeSlot[]>([])
const validationResult = ref<{
  title: string
  type: 'success' | 'warning' | 'error' | 'info'
  message: string
} | null>(null)

const formRef = ref()

// 计算属性
const jsonResult = computed(() => {
  if (selectedTimeSlots.value.length === 0) {
    return ''
  }
  return JSON.stringify({ timeSlots: selectedTimeSlots.value }, null, 2)
})

/** 处理时间段变化 */
const handleTimeSlotsChange = (slots: TimeSlot[]) => {
  console.log('时间段变化:', slots)
  validationResult.value = null // 清除之前的验证结果
}

/** 加载测试数据 */
const loadTestData = () => {
  selectedTimeSlots.value = [
    { startTime: '08:00', endTime: '12:00' },
    { startTime: '14:00', endTime: '18:00' },
    { startTime: '20:00', endTime: '22:00' }
  ]
}

/** 清空选择 */
const clearAll = () => {
  selectedTimeSlots.value = []
  validationResult.value = null
}

/** 验证时间段 */
const validateTimeSlots = () => {
  if (selectedTimeSlots.value.length === 0) {
    validationResult.value = {
      title: '验证结果',
      type: 'info',
      message: '没有选择任何时间段'
    }
    return
  }

  // 简单的前端验证
  let hasError = false
  let errorMessage = ''

  for (let i = 0; i < selectedTimeSlots.value.length; i++) {
    const slot = selectedTimeSlots.value[i]
    
    // 验证时间格式
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/
    if (!timeRegex.test(slot.startTime) || !timeRegex.test(slot.endTime)) {
      hasError = true
      errorMessage = `第${i + 1}个时间段格式错误`
      break
    }

    // 验证开始时间小于结束时间
    const startTime = new Date(`2000-01-01 ${slot.startTime}`)
    const endTime = new Date(`2000-01-01 ${slot.endTime}`)
    
    if (startTime >= endTime) {
      hasError = true
      errorMessage = `第${i + 1}个时间段：开始时间必须小于结束时间`
      break
    }
  }

  // 验证时间段重叠
  if (!hasError) {
    const sortedSlots = [...selectedTimeSlots.value].sort((a, b) => 
      a.startTime.localeCompare(b.startTime)
    )

    for (let i = 0; i < sortedSlots.length - 1; i++) {
      const current = sortedSlots[i]
      const next = sortedSlots[i + 1]
      
      const currentEnd = new Date(`2000-01-01 ${current.endTime}`)
      const nextStart = new Date(`2000-01-01 ${next.startTime}`)
      
      if (currentEnd > nextStart) {
        hasError = true
        errorMessage = `时间段重叠：${current.startTime}-${current.endTime} 与 ${next.startTime}-${next.endTime}`
        break
      }
    }
  }

  if (hasError) {
    validationResult.value = {
      title: '验证失败',
      type: 'error',
      message: errorMessage
    }
  } else {
    validationResult.value = {
      title: '验证成功',
      type: 'success',
      message: `所有${selectedTimeSlots.value.length}个时间段格式正确，无重叠`
    }
  }
}

/** 打开弹窗 */
const openDialog = () => {
  formRef.value.open('create')
}

/** 处理成功 */
const handleSuccess = () => {
  console.log('表单提交成功')
}
</script>

<style scoped>
.time-slot-demo {
  padding: 20px;
}

.demo-section {
  margin-bottom: 30px;
}

.demo-section h3 {
  margin-bottom: 15px;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.result-card {
  background-color: #f8f9fa;
}

.result-item {
  margin-bottom: 15px;
}

.result-item:last-child {
  margin-bottom: 0;
}

.result-item strong {
  color: #606266;
  margin-right: 8px;
}

.result-item pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  margin-top: 5px;
  overflow-x: auto;
}

.result-item ul {
  margin-top: 5px;
  padding-left: 20px;
}

.result-item li {
  margin-bottom: 5px;
  color: #409eff;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
