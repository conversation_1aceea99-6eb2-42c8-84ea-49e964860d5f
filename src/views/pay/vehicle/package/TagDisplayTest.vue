<template>
  <div class="tag-display-test">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>el-tag显示问题调试</span>
          <el-button @click="openConsole">打开控制台查看日志</el-button>
        </div>
      </template>
      
      <div class="test-section">
        <h3>问题分析</h3>
        <el-alert 
          title="可能的问题原因" 
          type="warning" 
          :closable="false"
          description="1. Close图标导入缺失
2. transition-group双层嵌套问题
3. 默认示例数据与v-model冲突
4. CSS样式问题
5. 数据绑定循环问题"
        />
      </div>

      <div class="test-section">
        <h3>修复后的TimeSlotSelector组件</h3>
        <TimeSlotSelector 
          v-model="selectedTimeSlots"
          @change="handleTimeSlotsChange"
        />
      </div>

      <div class="test-section">
        <h3>调试信息</h3>
        <el-descriptions :column="1" border>
          <el-descriptions-item label="timeSlots数组长度">
            {{ selectedTimeSlots.length }}
          </el-descriptions-item>
          <el-descriptions-item label="timeSlots内容">
            <pre>{{ JSON.stringify(selectedTimeSlots, null, 2) }}</pre>
          </el-descriptions-item>
          <el-descriptions-item label="是否为空数组">
            {{ selectedTimeSlots.length === 0 ? '是' : '否' }}
          </el-descriptions-item>
          <el-descriptions-item label="数组类型检查">
            {{ Array.isArray(selectedTimeSlots) ? '正确的数组' : '不是数组' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="test-section">
        <h3>手动测试</h3>
        <el-button-group>
          <el-button @click="addTestData">添加测试数据</el-button>
          <el-button @click="clearData">清空数据</el-button>
          <el-button @click="addSingleSlot">添加单个时段</el-button>
          <el-button @click="logCurrentState">打印当前状态</el-button>
        </el-button-group>
      </div>

      <div class="test-section">
        <h3>直接el-tag测试</h3>
        <p>下面是直接使用el-tag的测试，确认Element Plus是否正常工作：</p>
        <div class="direct-tags">
          <el-tag 
            v-for="(slot, index) in selectedTimeSlots"
            :key="index"
            closable
            type="primary"
            size="large"
            @close="removeDirectTag(index)"
            class="mr-2 mb-2"
          >
            <el-icon><Clock /></el-icon>
            {{ slot.startTime }} ~ {{ slot.endTime }}
          </el-tag>
          <span v-if="selectedTimeSlots.length === 0" class="text-gray-400">
            暂无数据 - 如果这里能显示el-tag，说明Element Plus正常
          </span>
        </div>
      </div>

      <div class="test-section">
        <h3>修复说明</h3>
        <el-steps :active="4" finish-status="success">
          <el-step title="添加Close图标导入" description="修复时间轴删除功能的图标问题"/>
          <el-step title="简化标签结构" description="移除双层容器，直接使用transition-group"/>
          <el-step title="禁用默认示例" description="避免默认数据干扰v-model绑定"/>
          <el-step title="优化数据绑定" description="防止无限循环，添加调试日志"/>
        </el-steps>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Clock } from '@element-plus/icons-vue'
import TimeSlotSelector from '@/components/TimeSlotSelector/index.vue'

/** 调试页面 */
defineOptions({ name: 'TagDisplayTest' })

interface TimeSlot {
  startTime: string
  endTime: string
}

// 响应式数据
const selectedTimeSlots = ref<TimeSlot[]>([])

/** 处理时间段变化 */
const handleTimeSlotsChange = (slots: TimeSlot[]) => {
  console.log('父组件收到时间段变化:', slots)
}

/** 添加测试数据 */
const addTestData = () => {
  selectedTimeSlots.value = [
    { startTime: '08:00', endTime: '12:00' },
    { startTime: '14:00', endTime: '18:00' },
    { startTime: '20:00', endTime: '22:00' }
  ]
  console.log('添加测试数据:', selectedTimeSlots.value)
}

/** 清空数据 */
const clearData = () => {
  selectedTimeSlots.value = []
  console.log('清空数据')
}

/** 添加单个时段 */
const addSingleSlot = () => {
  selectedTimeSlots.value = [
    { startTime: '10:00', endTime: '16:00' }
  ]
  console.log('添加单个时段:', selectedTimeSlots.value)
}

/** 打印当前状态 */
const logCurrentState = () => {
  console.log('=== 当前状态调试信息 ===')
  console.log('selectedTimeSlots:', selectedTimeSlots.value)
  console.log('数组长度:', selectedTimeSlots.value.length)
  console.log('是否为数组:', Array.isArray(selectedTimeSlots.value))
  console.log('JSON字符串:', JSON.stringify(selectedTimeSlots.value))
}

/** 移除直接标签 */
const removeDirectTag = (index: number) => {
  selectedTimeSlots.value.splice(index, 1)
  console.log('移除直接标签，索引:', index)
}

/** 打开控制台提示 */
const openConsole = () => {
  alert('请按F12打开浏览器开发者工具，查看Console标签页的日志输出')
}
</script>

<style scoped>
.tag-display-test {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-section {
  margin-bottom: 30px;
}

.test-section:last-child {
  margin-bottom: 0;
}

.test-section h3 {
  margin-bottom: 15px;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
  margin: 0;
  max-height: 200px;
  overflow-y: auto;
}

.direct-tags {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  min-height: 50px;
}

.mr-2 {
  margin-right: 8px;
}

.mb-2 {
  margin-bottom: 8px;
}

.text-gray-400 {
  color: #909399;
}
</style>
