<template>
  <Dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    :width="1200"
    :top="'5vh'"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="vehicle-package-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
      class="vehicle-package-form"
    >
      <!-- 基础信息区域 -->
      <div class="form-section">
        <div class="section-title">
          <el-icon><InfoFilled /></el-icon>
          <span>基础信息</span>
        </div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="套餐名称" prop="name">
              <el-input v-model="formData.name" placeholder="请输入套餐名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="套餐周期" prop="period">
              <el-input-number v-model="formData.period" placeholder="请输入套餐周期" :min="1" style="width: 100%" />
              <span class="ml-2 text-gray-500">天</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="结算类型" prop="settlementType">
              <el-radio-group v-model="formData.settlementType">
                <el-radio :label="1">按次</el-radio>
                <el-radio :label="2">按金额</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="formData.status">
                <el-radio
                  v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
                  :key="dict.value"
                  :label="dict.value"
                >
                  {{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 价格配置区域 -->
      <div class="form-section">
        <div class="section-title">
          <el-icon><Money /></el-icon>
          <span>价格配置</span>
        </div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="套餐额度" prop="quota">
              <el-input-number v-model="formData.quota" placeholder="请输入套餐额度" :min="1" style="width: 100%" />
              <span class="ml-2 text-gray-500">{{ formData.settlementType === 1 ? '次' : '元' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="套餐价格" prop="price">
              <el-input-number v-model="formData.price" placeholder="请输入套餐价格" :min="1" :precision="2" style="width: 100%" />
              <span class="ml-2 text-gray-500">元</span>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 使用限制区域 -->
      <div class="form-section">
        <div class="section-title">
          <el-icon><Setting /></el-icon>
          <span>使用限制</span>
        </div>
        <el-form-item label="可用站点" prop="availableStations">
          <el-select
            v-model="selectedStationIds"
            multiple
            filterable
            placeholder="请选择可用站点"
            style="width: 100%"
            @change="handleStationChange"
          >
            <el-option
              v-for="station in stationOptions"
              :key="station.siteId"
              :label="station.siteName"
              :value="station.siteId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="可用时段" prop="availableTimes" class="time-slot-form-item">
          <TimeSlotSelector
            v-model="timeSlots"
            @change="handleTimeSlotsChange"
          />
        </el-form-item>
      </div>

      <!-- 其他信息区域 -->
      <div class="form-section">
        <div class="section-title">
          <el-icon><Document /></el-icon>
          <span>其他信息</span>
        </div>
        <el-form-item label="套餐说明" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            placeholder="请输入套餐说明"
            :rows="3"
          />
        </el-form-item>
      </div>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import * as VehiclePackageApi from '@/api/pay/vehicle/package'
import { VehiclePackageVO } from '@/api/pay/vehicle/package'
import { BusSiteApi, BusSiteVO } from '@/api/site/bussite'
import TimeSlotSelector from '@/components/TimeSlotSelector/index.vue'
import { InfoFilled, Money, Setting, Document } from '@element-plus/icons-vue'

/** 车辆套餐 表单 */
defineOptions({ name: 'VehiclePackageForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  name: '',
  period: undefined,
  settlementType: 1,
  quota: undefined,
  availableStations: '',
  availableTimes: '',
  price: undefined,
  description: '',
  status: 0
})
const formRules = reactive({
  name: [{ required: true, message: '套餐名称不能为空', trigger: 'blur' }],
  period: [{ required: true, message: '套餐周期不能为空', trigger: 'blur' }],
  settlementType: [{ required: true, message: '结算类型不能为空', trigger: 'change' }],
  quota: [{ required: true, message: '套餐额度不能为空', trigger: 'blur' }],
  price: [{ required: true, message: '套餐价格不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref
const selectedStationIds = ref<number[]>([]) // 选中的站点ID数组
const stationOptions = ref<BusSiteVO[]>([]) // 站点选项数据

// 时间段相关
interface TimeSlot {
  startTime: string
  endTime: string
}

const timeSlots = ref<TimeSlot[]>([]) // 选中的时间段

/** 处理站点选择变化 */
const handleStationChange = (stationIds: number[]) => {
  selectedStationIds.value = stationIds
  // 将站点ID数组转换为逗号分隔的字符串存储到表单数据中
  formData.value.availableStations = stationIds.join(',')
}

/** 处理时间段变化 */
const handleTimeSlotsChange = (slots: TimeSlot[]) => {
  timeSlots.value = slots
  // 将时间段转换为JSON格式存储
  if (slots.length > 0) {
    formData.value.availableTimes = JSON.stringify({ timeSlots: slots })
  } else {
    formData.value.availableTimes = ''
  }
}

/** 将逗号分隔的站点ID字符串转换为数组 */
const parseStationIds = (stationStr: string): number[] => {
  if (!stationStr || stationStr.trim() === '') {
    return []
  }
  return stationStr.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id))
}

/** 解析时间段JSON字符串 */
const parseTimeSlots = (timeSlotsStr: string): TimeSlot[] => {
  if (!timeSlotsStr || timeSlotsStr.trim() === '') {
    return []
  }

  try {
    const parsed = JSON.parse(timeSlotsStr)
    return parsed.timeSlots || []
  } catch (error) {
    console.error('解析时间段JSON失败:', error)
    return []
  }
}

/** 加载站点数据 */
const loadStationOptions = async () => {
  try {
    const response = await BusSiteApi.getSimpleBusSiteList()
    // 处理响应数据，确保是数组格式
    if (Array.isArray(response)) {
      stationOptions.value = response
    } else if (response && Array.isArray(response.data)) {
      stationOptions.value = response.data
    } else {
      stationOptions.value = []
    }
  } catch (error) {
    console.error('加载站点数据失败:', error)
    stationOptions.value = []
  }
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()

  // 加载站点数据
  await loadStationOptions()

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await VehiclePackageApi.getVehiclePackage(id)
      // 价格从分转换为元
      if (formData.value.price) {
        formData.value.price = formData.value.price / 100
      }
      // 解析已选择的站点ID
      selectedStationIds.value = parseStationIds(formData.value.availableStations || '')
      // 解析已选择的时间段
      timeSlots.value = parseTimeSlots(formData.value.availableTimes || '')
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = { ...formData.value } as unknown as VehiclePackageVO
    // 价格从元转换为分
    if (data.price) {
      data.price = Math.round(data.price * 100)
    }
    if (formType.value === 'create') {
      await VehiclePackageApi.createVehiclePackage(data)
      message.success(t('common.createSuccess'))
    } else {
      await VehiclePackageApi.updateVehiclePackage(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: '',
    period: undefined,
    settlementType: 1,
    quota: undefined,
    availableStations: '',
    availableTimes: '',
    price: undefined,
    description: '',
    status: 0
  }
  selectedStationIds.value = []
  timeSlots.value = []
  formRef.value?.resetFields()
}
</script>

<style scoped>
/* 弹窗样式优化 */
:deep(.vehicle-package-dialog) {
  .el-dialog {
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  }

  .el-dialog__header {
    background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
    color: white;
    border-radius: 12px 12px 0 0;
    padding: 20px 24px;
  }

  .el-dialog__title {
    font-size: 18px;
    font-weight: 600;
  }

  .el-dialog__headerbtn .el-dialog__close {
    color: white;
    font-size: 18px;
  }

  .el-dialog__body {
    padding: 16px 20px;
    max-height: 75vh;
    overflow-y: auto;
  }

  .el-dialog__footer {
    padding: 16px 24px;
    border-top: 1px solid #e4e7ed;
    background: #fafbfc;
    border-radius: 0 0 12px 12px;
  }
}

/* 表单样式优化 */
.vehicle-package-form {
  .el-form-item {
    margin-bottom: 16px;
  }

  .el-form-item__label {
    font-weight: 600;
    color: #303133;
    line-height: 1.5;
  }

  .el-form-item__content {
    line-height: 1.5;
  }

  /* 表单分组样式 */
  .form-section {
    margin-bottom: 20px;
    padding: 16px;
    background: #fafbfc;
    border-radius: 8px;
    border: 1px solid #e4e7ed;
  }

  .form-section:last-child {
    margin-bottom: 0;
  }

  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    font-size: 14px;
    font-weight: 600;
    color: #303133;
    padding-bottom: 8px;
    border-bottom: 1px solid #e4e7ed;
  }

  .section-title .el-icon {
    margin-right: 8px;
    color: #409eff;
    font-size: 16px;
  }

  /* 时间段表单项特殊样式 */
  .time-slot-form-item {
    margin-bottom: 0;
  }

  .time-slot-form-item .el-form-item__content {
    line-height: normal;
  }

  /* 输入框样式 */
  .el-input,
  .el-select,
  .el-textarea {
    .el-input__wrapper,
    .el-textarea__inner {
      border-radius: 6px;
      transition: all 0.3s ease;
    }

    .el-input__wrapper:hover,
    .el-textarea__inner:hover {
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
    }
  }

  /* 数字输入框样式 */
  .el-input-number {
    width: 100%;

    .el-input__wrapper {
      border-radius: 6px;
    }
  }

  /* 选择器样式 */
  .el-select {
    width: 100%;
  }

  /* 单选框组样式 */
  .el-radio-group {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .el-radio {
      margin-right: 0;

      .el-radio__label {
        font-weight: 500;
      }
    }
  }
}

/* 时间段选择器容器样式 */
.time-slot-form-item .el-form-item__content {
  line-height: normal;
}

/* 紧凑布局优化 */
.vehicle-package-form :deep(.time-slot-selector) {
  margin: 0;
  border: none;
  background: transparent;
  padding: 0;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  :deep(.vehicle-package-dialog) {
    .el-dialog {
      width: 95% !important;
      margin: 0 auto;
    }
  }
}

@media (max-width: 768px) {
  :deep(.vehicle-package-dialog) {
    .el-dialog {
      width: 98% !important;
      margin: 0 auto;
    }

    .el-dialog__body {
      padding: 12px;
      max-height: 65vh;
    }
  }

  .vehicle-package-form {
    .el-form-item {
      margin-bottom: 14px;
    }

    .el-form-item__label {
      line-height: 1.2;
      margin-bottom: 6px;
    }

    .form-section {
      padding: 12px;
      margin-bottom: 16px;
    }

    .section-title {
      margin-bottom: 12px;
      font-size: 13px;
    }

    /* 移动端强制单列布局 */
    .el-col {
      width: 100% !important;
      flex: 0 0 100% !important;
      max-width: 100% !important;
    }
  }
}

/* 加载状态优化 */
:deep(.el-loading-mask) {
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.9);
}

/* 滚动条样式 */
:deep(.el-dialog__body)::-webkit-scrollbar {
  width: 6px;
}

:deep(.el-dialog__body)::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

:deep(.el-dialog__body)::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

:deep(.el-dialog__body)::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
