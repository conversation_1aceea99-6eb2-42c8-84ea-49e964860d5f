<template>
  <div class="time-slot-stats-test">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>TimeSlotSelector 总时长和覆盖率功能测试</span>
          <el-button @click="clearSlots">清空测试</el-button>
        </div>
      </template>

      <div class="test-section">
        <h3>增强后的TimeSlotSelector组件</h3>
        <p class="description">
          <strong>新增功能：</strong>
          <br/>1. 总时长计算：显示所选时间段的总时长（小时/分钟）
          <br/>2. 覆盖率计算：显示时间段覆盖24小时的百分比
          <br/>3. 智能格式化：自动格式化时长显示（如：8小时30分钟）
          <br/>4. 实时更新：选择或删除时间段时实时更新统计信息
        </p>

        <TimeSlotSelector
          v-model="selectedTimeSlots"
          @change="handleTimeSlotsChange"
        />
      </div>

      <div class="test-controls">
        <h4>快速测试按钮</h4>
        <div class="button-group">
          <el-button @click="addSingleSlot">
            添加单个时段 (9:00-17:00)
          </el-button>
          <el-button @click="addMultipleSlots">
            添加多个时段
          </el-button>
          <el-button @click="addHalfDaySlot">
            添加半天时段 (12小时)
          </el-button>
          <el-button @click="addFullDaySlot">
            添加全天时段 (24小时)
          </el-button>
          <el-button @click="addShortSlots">
            添加短时段 (30分钟)
          </el-button>
        </div>
      </div>

      <div class="debug-info">
        <h4>调试信息</h4>
        <pre>{{ JSON.stringify(selectedTimeSlots, null, 2) }}</pre>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import TimeSlotSelector from '@/components/TimeSlotSelector/index.vue'

interface TimeSlot {
  startTime: string
  endTime: string
}

const selectedTimeSlots = ref<TimeSlot[]>([])

const handleTimeSlotsChange = (slots: TimeSlot[]) => {
  console.log('时间段变化:', slots)
}

const clearSlots = () => {
  selectedTimeSlots.value = []
}

const addSingleSlot = () => {
  selectedTimeSlots.value = [
    { startTime: '09:00', endTime: '17:00' }
  ]
}

const addMultipleSlots = () => {
  selectedTimeSlots.value = [
    { startTime: '08:00', endTime: '12:00' },
    { startTime: '14:00', endTime: '18:00' },
    { startTime: '20:00', endTime: '22:00' }
  ]
}

const addHalfDaySlot = () => {
  selectedTimeSlots.value = [
    { startTime: '06:00', endTime: '18:00' }
  ]
}

const addFullDaySlot = () => {
  selectedTimeSlots.value = [
    { startTime: '00:00', endTime: '24:00' }
  ]
}

const addShortSlots = () => {
  selectedTimeSlots.value = [
    { startTime: '09:00', endTime: '09:30' },
    { startTime: '10:00', endTime: '10:30' },
    { startTime: '11:00', endTime: '11:30' }
  ]
}
</script>

<style scoped>
.time-slot-stats-test {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-section {
  margin-bottom: 24px;
}

.description {
  background: #f0f9ff;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid #409eff;
  margin-bottom: 16px;
  line-height: 1.6;
}

.test-controls {
  margin-bottom: 24px;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 12px;
}

.debug-info {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.debug-info pre {
  margin: 0;
  font-size: 12px;
  color: #606266;
  max-height: 200px;
  overflow-y: auto;
}

h3 {
  color: #303133;
  margin-bottom: 12px;
}

h4 {
  color: #606266;
  margin-bottom: 8px;
}
</style>
