<template>
  <div class="time-slot-test">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>TimeSlotSelector 修复后效果测试</span>
          <el-button @click="clearSlots">清空测试</el-button>
        </div>
      </template>

      <div class="test-section">
        <h3>修复后的TimeSlotSelector组件</h3>
        <p class="description">
          <strong>修复内容：</strong>
          <br/>1. 修复时间轴高度：保持时间标签区域30px，减少交互区域到54px
          <br/>2. 修复时间段标签显示问题：优化数据绑定和transition-group
          <br/>3. 确保removeSlot删除功能正常工作
          <br/>4. 改善默认示例数据的设置逻辑
        </p>

        <TimeSlotSelector
          v-model="selectedTimeSlots"
          @change="handleTimeSlotsChange"
        />
      </div>

      <div class="test-section">
        <h3>当前状态</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="选中时间段数量">
            {{ selectedTimeSlots.length }}
          </el-descriptions-item>
          <el-descriptions-item label="是否为空状态">
            {{ selectedTimeSlots.length === 0 ? '是' : '否' }}
          </el-descriptions-item>
          <el-descriptions-item :span="2" label="时间段详情">
            <div v-if="selectedTimeSlots.length > 0">
              <el-tag
                v-for="(slot, index) in selectedTimeSlots"
                :key="index"
                class="mr-2 mb-2"
              >
                {{ slot.startTime }} ~ {{ slot.endTime }}
              </el-tag>
            </div>
            <span v-else class="text-gray-400">无时间段</span>
          </el-descriptions-item>
          <el-descriptions-item :span="2" label="JSON数据">
            <pre>{{ jsonData }}</pre>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="test-section">
        <h3>测试操作</h3>
        <el-button-group>
          <el-button @click="addSingleSlot">添加单个时段</el-button>
          <el-button @click="addMultipleSlots">添加多个时段</el-button>
          <el-button type="danger" @click="clearSlots">清空所有</el-button>
          <el-button @click="testRemove">测试删除功能</el-button>
        </el-button-group>
      </div>

      <div class="test-section">
        <h3>功能验证</h3>
        <el-alert
          :closable="false"
          description="1. 时间轴高度是否合适（标签区域清晰可见）
2. 选中时间段是否正确显示为el-tag标签
3. 点击标签的删除按钮是否能正常删除
4. 空状态是否显示简洁的提示文字
5. 拖拽选择是否正常工作"
          title="验证项目"
          type="info"
        />
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import {computed, ref} from 'vue'
import TimeSlotSelector from '@/components/TimeSlotSelector/index.vue'

/** 测试页面 */
defineOptions({name: 'TimeSlotTest'})

interface TimeSlot {
  startTime: string
  endTime: string
}

// 响应式数据
const selectedTimeSlots = ref<TimeSlot[]>([])

// 计算属性
const jsonData = computed(() => {
  if (selectedTimeSlots.value.length === 0) {
    return '无数据'
  }
  return JSON.stringify({timeSlots: selectedTimeSlots.value}, null, 2)
})

/** 处理时间段变化 */
const handleTimeSlotsChange = (slots: TimeSlot[]) => {
  console.log('时间段变化:', slots)
}

/** 添加单个时段 */
const addSingleSlot = () => {
  selectedTimeSlots.value = [
    {startTime: '09:00', endTime: '17:00'}
  ]
}

/** 添加多个时段 */
const addMultipleSlots = () => {
  selectedTimeSlots.value = [
    {startTime: '08:00', endTime: '12:00'},
    {startTime: '14:00', endTime: '18:00'},
    {startTime: '20:00', endTime: '22:00'}
  ]
}

/** 清空时段 */
const clearSlots = () => {
  selectedTimeSlots.value = []
}

/** 测试删除功能 */
const testRemove = () => {
  selectedTimeSlots.value = [
    {startTime: '10:00', endTime: '12:00'},
    {startTime: '14:00', endTime: '16:00'}
  ]
  setTimeout(() => {
    alert('请点击时间段标签右侧的删除按钮测试删除功能')
  }, 100)
}
</script>

<style scoped>
.time-slot-test {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-section {
  margin-bottom: 30px;
}

.test-section:last-child {
  margin-bottom: 0;
}

.test-section h3 {
  margin-bottom: 15px;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.description {
  margin-bottom: 20px;
  padding: 12px;
  background: #f0f9ff;
  border-left: 4px solid #409eff;
  border-radius: 4px;
  color: #606266;
  line-height: 1.6;
}

.description strong {
  color: #303133;
}

pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
  margin: 0;
}

.mr-2 {
  margin-right: 8px;
}

.mb-2 {
  margin-bottom: 8px;
}

.text-gray-400 {
  color: #909399;
}
</style>
