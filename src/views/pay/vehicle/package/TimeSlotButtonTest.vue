<template>
  <div class="test-container">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>时间段选择器按钮样式测试</span>
        </div>
      </template>
      
      <div class="test-content">
        <h3>按钮样式一致性测试</h3>
        <p>验证快捷预设按钮（峰时段、平时段、谷时段、全天）与清空按钮的大小风格是否一致。</p>
        
        <div class="test-section">
          <h4>TimeSlotSelector组件测试</h4>
          <TimeSlotSelector
            v-model="timeSlots"
            @change="handleTimeSlotsChange"
          />
        </div>

        <div class="test-section">
          <h4>按钮对比测试</h4>
          <div class="button-comparison">
            <div class="button-group">
              <h5>预设按钮样式</h5>
              <el-button size="default" type="default">
                <el-icon><Clock /></el-icon>
                峰时段
              </el-button>
              <el-button size="default" type="primary">
                <el-icon><Clock /></el-icon>
                平时段（选中）
              </el-button>
            </div>
            
            <div class="button-group">
              <h5>清空按钮样式</h5>
              <el-button size="default">
                <el-icon><Delete /></el-icon>
                清空
              </el-button>
            </div>
          </div>
        </div>

        <div class="test-section">
          <h4>当前选中的时间段</h4>
          <div class="selected-info">
            <p v-if="timeSlots.length === 0">暂无选中时间段</p>
            <div v-else>
              <p>已选择 {{ timeSlots.length }} 个时间段：</p>
              <ul>
                <li v-for="(slot, index) in timeSlots" :key="index">
                  {{ slot.startTime }} ~ {{ slot.endTime }}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import TimeSlotSelector from '@/components/TimeSlotSelector/index.vue'
import { Clock, Delete } from '@element-plus/icons-vue'

defineOptions({ name: 'TimeSlotButtonTest' })

interface TimeSlot {
  startTime: string
  endTime: string
}

const timeSlots = ref<TimeSlot[]>([])

const handleTimeSlotsChange = (slots: TimeSlot[]) => {
  console.log('时间段变化:', slots)
}
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-content {
  line-height: 1.6;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.test-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.test-section h5 {
  margin: 0 0 12px 0;
  color: #606266;
  font-size: 14px;
  font-weight: 500;
}

.button-comparison {
  display: flex;
  gap: 40px;
  flex-wrap: wrap;
}

.button-group {
  flex: 1;
  min-width: 200px;
}

.button-group .el-button {
  margin-right: 8px;
  margin-bottom: 8px;
}

.selected-info {
  background: white;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.selected-info ul {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.selected-info li {
  margin-bottom: 4px;
  color: #606266;
}

@media (max-width: 768px) {
  .test-container {
    padding: 12px;
  }
  
  .button-comparison {
    flex-direction: column;
    gap: 20px;
  }
  
  .test-section {
    padding: 16px;
  }
}
</style>
