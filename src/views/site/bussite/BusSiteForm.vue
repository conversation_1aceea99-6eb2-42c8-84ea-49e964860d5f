<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="站点图" prop="siteImg">
        <UploadImg v-model="formData.siteImg" />
      </el-form-item>
      <el-form-item label="站点号" prop="siteNo">
        <el-input 
          v-model="formData.siteNo" 
          placeholder="请输入站点号" 
          :disabled="formType === 'update'"
        />
      </el-form-item>
      <el-form-item label="站点名称" prop="siteName">
        <el-input 
          v-model="formData.siteName" 
          placeholder="请输入站点名称" 
          :disabled="formType === 'update'"
        />
      </el-form-item>
      <el-form-item label="地址" prop="siteAdress">
        <el-input 
          v-model="formData.siteAdress" 
          placeholder="请输入地址" 
          :disabled="formType === 'update'"
        />
      </el-form-item>
<!--      <el-form-item label="省" prop="province">-->
<!--        <el-select v-model="formData.province" placeholder="请选择省">-->
<!--          <el-option label="请选择字典生成" value="" />-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="市" prop="city">-->
<!--        <el-select v-model="formData.city" placeholder="请选择市">-->
<!--          <el-option label="请选择字典生成" value="" />-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="区" prop="district">-->
<!--        <el-select v-model="formData.district" placeholder="请选择区">-->
<!--          <el-option label="请选择字典生成" value="" />-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <el-form-item label="经度" prop="longitude">
        <el-input 
          v-model="formData.longitude" 
          placeholder="请输入经度" 
          :disabled="formType === 'update'"
        />
      </el-form-item>
      <el-form-item label="维度" prop="latitude">
        <el-input 
          v-model="formData.latitude" 
          placeholder="请输入维度" 
          :disabled="formType === 'update'"
        />
      </el-form-item>
      <el-form-item label="营业开始时间" prop="startTime">
        <el-time-picker
          v-model="formData.startTime"
          format="HH:mm:ss"
          value-format="HH:mm:ss"
          placeholder="选择营业开始时间"
        />
      </el-form-item>
      <el-form-item label="营业结束时间" prop="endTime">
        <el-time-picker
          v-model="formData.endTime"
          format="HH:mm:ss"
          value-format="HH:mm:ss"
          placeholder="选择营业结束时间"
        />
      </el-form-item>
      <el-form-item label="换电时长" prop="changeTime">
        <el-input v-model="formData.changeTime" type="textarea" placeholder="请输入换电时长" />
      </el-form-item>
      <!-- <el-form-item label="换电状态" prop="siteStatus">
        <el-radio-group v-model="formData.siteStatus">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.SITE_STATUS)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item> -->
      <el-form-item label="换电实景" prop="siteImgs">
        <UploadImg v-model="formData.siteImgs" :limit="5" multiple />
      </el-form-item>
      <!-- <el-form-item label="城市code" prop="cityCode">
        <el-input v-model="formData.cityCode" placeholder="请输入城市code" />
      </el-form-item> -->
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { BusSiteApi, BusSiteVO } from '@/api/site/bussite'

/** 站点 表单 */
defineOptions({ name: 'BusSiteForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  siteId: undefined,
  siteImg: undefined,
  siteNo: undefined,
  siteName: undefined,
  siteAdress: undefined,
  province: undefined,
  city: undefined,
  district: undefined,
  longitude: undefined,
  latitude: undefined,
  startTime: undefined,
  endTime: undefined,
  changeTime: undefined,
  siteStatus: undefined,
  enabled: undefined,
  siteImgs: undefined,
  cityCode: undefined
})
const formRules = reactive({
  // siteImg: [{ required: true, message: '站点图不能为空', trigger: 'blur' }],
  // siteNo: [{ required: true, message: '站点号不能为空', trigger: 'blur' }],
  // siteName: [{ required: true, message: '站点名称不能为空', trigger: 'blur' }],
  // siteAdress: [{ required: true, message: '地址不能为空', trigger: 'blur' }],
  // province: [{ required: true, message: '省不能为空', trigger: 'change' }],
  // city: [{ required: true, message: '市不能为空', trigger: 'change' }],
  // district: [{ required: true, message: '区不能为空', trigger: 'change' }],
  // longitude: [{ required: true, message: '经度不能为空', trigger: 'blur' }],
  // latitude: [{ required: true, message: '维度不能为空', trigger: 'blur' }],
  startTime: [{ required: true, message: '营业开始时间不能为空', trigger: 'change' }],
  endTime: [{ required: true, message: '营业结束时间不能为空', trigger: 'change' }],
  changeTime: [{ required: true, message: '换电时长不能为空', trigger: 'blur' }],
  // siteStatus: [{ required: true, message: '换电状态不能为空', trigger: 'blur' }],
  // enabled: [{ required: true, message: '是否启用不能为空', trigger: 'change' }],
  // siteImgs: [{ required: true, message: '换电实景不能为空', trigger: 'blur' }],
  // cityCode: [{ required: true, message: '城市code不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const data = await BusSiteApi.getBusSite(id)
      // 处理时间数组
      if (Array.isArray(data.startTime)) {
        data.startTime = formatTimeArray(data.startTime)
      }
      if (Array.isArray(data.endTime)) {
        data.endTime = formatTimeArray(data.endTime)
      }
      formData.value = data
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = { ...formData.value } as unknown as BusSiteVO
    // 处理时间字符串
    data.startTime = parseTimeString(data.startTime)
    data.endTime = parseTimeString(data.endTime)
    
    if (formType.value === 'create') {
      await BusSiteApi.createBusSite(data)
      message.success(t('common.createSuccess'))
    } else {
      await BusSiteApi.updateBusSite(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    siteId: undefined,
    siteImg: undefined,
    siteNo: undefined,
    siteName: undefined,
    siteAdress: undefined,
    province: undefined,
    city: undefined,
    district: undefined,
    longitude: undefined,
    latitude: undefined,
    startTime: undefined,
    endTime: undefined,
    changeTime: undefined,
    siteStatus: undefined,
    enabled: undefined,
    siteImgs: undefined,
    cityCode: undefined
  }
  formRef.value?.resetFields()
}

const formatTimeArray = (timeArray: number[]) => {
  if (!timeArray || timeArray.length < 2) return undefined
  const [hours, minutes, seconds = 0] = timeArray
  return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`
}

const parseTimeString = (timeString: string) => {
  if (!timeString) return undefined
  const [hours, minutes, seconds] = timeString.split(':').map(Number)
  return [hours, minutes, seconds]
}
</script>
