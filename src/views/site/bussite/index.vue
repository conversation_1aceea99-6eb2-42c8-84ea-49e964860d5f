<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="站点号" prop="siteNo">
        <el-input
          v-model="queryParams.siteNo"
          placeholder="请输入站点号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="站点名称" prop="siteName">
        <el-input
          v-model="queryParams.siteName"
          placeholder="请输入站点名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
<!--      <el-form-item label="省" prop="province">-->
<!--        <el-select-->
<!--          v-model="queryParams.province"-->
<!--          placeholder="请选择省"-->
<!--          clearable-->
<!--          class="!w-240px"-->
<!--        >-->
<!--          <el-option label="请选择字典生成" value="" />-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="市" prop="city">-->
<!--        <el-select-->
<!--          v-model="queryParams.city"-->
<!--          placeholder="请选择市"-->
<!--          clearable-->
<!--          class="!w-240px"-->
<!--        >-->
<!--          <el-option label="请选择字典生成" value="" />-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="区" prop="district">-->
<!--        <el-select-->
<!--          v-model="queryParams.district"-->
<!--          placeholder="请选择区"-->
<!--          clearable-->
<!--          class="!w-240px"-->
<!--        >-->
<!--          <el-option label="请选择字典生成" value="" />-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <el-form-item label="换电状态" prop="siteStatus">
        <el-select
          v-model="queryParams.siteStatus"
          placeholder="请选择换电状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.SITE_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否启用" prop="enabled">
        <el-select
          v-model="queryParams.enabled"
          placeholder="请选择是否启用"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.YES_OR_NO)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['site:bus-site:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['site:bus-site:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table 
      v-loading="loading" 
      :data="list" 
      :stripe="true" 
      :show-overflow-tooltip="true"
      border
    >
      <el-table-column label="站点id" align="center" prop="siteId" v-if="false" />
      <el-table-column label="站点图" align="center" prop="siteImg"  v-if="false">
        <template #default="scope">
          <el-image
            v-if="scope.row.siteImg"
            :src="scope.row.siteImg"
            :preview-src-list="[scope.row.siteImg]"
            fit="cover"
            style="width: 50px; height: 50px"
          />
        </template>
      </el-table-column>
      <el-table-column label="站点号" align="center" prop="siteNo" />
      <el-table-column label="站点名称" align="center" prop="siteName" />
      <el-table-column label="地址" align="center" prop="siteAdress" />
<!--      <el-table-column label="省" align="center" prop="province" />-->
<!--      <el-table-column label="市" align="center" prop="city" />-->
<!--      <el-table-column label="区" align="center" prop="district" />-->
      <el-table-column label="经度" align="center" prop="longitude" min-width="110"/>
      <el-table-column label="维度" align="center" prop="latitude" min-width="110"/>
      <el-table-column label="营业开始时间" align="center" prop="startTime" min-width="90">
        <template #default="scope">
          {{ formatTime(scope.row.startTime) }}
        </template>
      </el-table-column>
      <el-table-column label="营业结束时间" align="center" prop="endTime" min-width="90">
        <template #default="scope">
          {{ formatTime(scope.row.endTime) }}
        </template>
      </el-table-column>
      <el-table-column label="换电时长" align="center" prop="changeTime" />
      <el-table-column label="换电状态" align="center" prop="siteStatus" min-width="85">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SITE_STATUS" :value="scope.row.siteStatus" />
        </template>
      </el-table-column>
      <el-table-column label="是否启用" align="center" prop="enabled">
        <template #default="scope">
          <el-switch
            v-model="scope.row.enabled"
            :active-value="1"
            :inactive-value="0"
            :disabled="!checkPermi(['site:bus-site:enabled'])"
            @change="(value: number) => handleStatusChange(value, scope.row)"
            inline-prompt
            active-text="启用"
            inactive-text="禁用"
            class="reverse-switch"
          />
        </template>
      </el-table-column>
      <el-table-column label="是否隐藏" align="center" prop="hidden">
        <template #default="scope">
          <el-switch
            v-model="scope.row.hidden"
            :active-value="0"
            :inactive-value="1"
            :disabled="!checkPermi(['site:bus-site:deleted'])"
            @change="(value: number) => handleDeleteChange(value , scope.row)"
            inline-prompt
            :active-text="scope.row.hidden === 0 ? '显示' : ''"
            :inactive-text="scope.row.hidden === 1 ? '隐藏' : ''"
          />
        </template>
      </el-table-column>
      <el-table-column label="换电实景" align="center" prop="siteImgs"  v-if="false" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="城市code" align="center" prop="cityCode" v-if="false" />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.siteId)"
            v-hasPermi="['site:bus-site:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['site:bus-site:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <BusSiteForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { BusSiteApi, BusSiteVO } from '@/api/site/bussite'
import BusSiteForm from './BusSiteForm.vue'
import { checkPermi } from '@/utils/permission'

/** 站点 列表 */
defineOptions({ name: 'BusSite' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<BusSiteVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  siteNo: undefined,
  siteName: undefined,
  province: undefined,
  city: undefined,
  district: undefined,
  siteStatus: undefined,
  enabled: undefined,
  hidden: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await BusSiteApi.getBusSitePage(queryParams)
    console.log('API返回数据:', data)
    console.log('列表数据:', data.list)
    console.log('第一条数据:', data.list[1])
    console.log('开始时间:', data.list[1]?.startTime)
    console.log('结束时间:', data.list[1]?.endTime)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await BusSiteApi.deleteBusSite(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await BusSiteApi.exportBusSite(queryParams)
    download.excel(data, '站点.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 修改状态操作 */
const handleStatusChange = async (value: number, row: BusSiteVO) => {
  try {
    await BusSiteApi.updateBusSiteStatus(row.siteId, value)
    message.success(t('common.updateSuccess'))
  } catch {
    // 如果更新失败，恢复原来的状态
    row.enabled = value === 1 ? 0 : 1
  }
}

/** 修改隐藏状态操作 */
const handleDeleteChange = async (value: number, row: BusSiteVO) => {
  try {
    console.log(value)
    const res =await BusSiteApi.updateBusSiteHidden(row.siteId, value)
    console.log(res);
    message.success(t('common.updateSuccess'))
  } catch {
    // 如果更新失败，恢复原来的状态
    row.hidden = row.hidden === 1 ? 0 : 1; // 恢复到原来的状态
    message.error(t('common.updateFailed'))
  }
}

/** 格式化时间 */
const formatTime = (time: any) => {
  if (!time) return ''
  if (Array.isArray(time)) {
    // 确保小时和分钟都是两位数
    const hours = time[0].toString().padStart(2, '0')
    const minutes = time[1].toString().padStart(2, '0')
    // 如果有秒数，保留秒数
    const seconds = time.length > 2 ? time[2].toString().padStart(2, '0') : '00'
    return `${hours}:${minutes}:${seconds}`
  }
  if (typeof time === 'string') {
    return time.includes(':') ? time : time + ':00'
  }
  return time
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>


