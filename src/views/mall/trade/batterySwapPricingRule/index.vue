<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="规则名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入规则名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable class="!w-240px">
          <el-option
            v-for="dict in BATTERY_SWAP_PRICING_RULE_STATUS_OPTIONS"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="日期类型" prop="dateType">
        <el-select v-model="queryParams.dateType" placeholder="请选择日期类型" clearable class="!w-240px">
          <el-option
            v-for="dict in BATTERY_SWAP_PRICING_RULE_DATE_TYPE_OPTIONS"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="适用换电站" prop="stationCode">
        <el-select
          v-model="queryParams.stationCode"
          placeholder="请选择换电站"
          clearable
          filterable
          class="!w-240px"
        >
          <el-option label="全部站点" value="" />
          <el-option
            v-for="station in stationOptions"
            :key="station.siteId"
            :label="`${station.siteName} (${station.siteNo})`"
            :value="station.siteNo"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="优先级范围">
        <el-input-number
          v-model="queryParams.minPriority"
          placeholder="最小优先级"
          :min="0"
          :max="999"
          class="!w-110px mr-2"
        />
        <span class="mx-2">-</span>
        <el-input-number
          v-model="queryParams.maxPriority"
          placeholder="最大优先级"
          :min="0"
          :max="999"
          class="!w-110px"
        />
      </el-form-item>
      <el-form-item label="价格范围(分)">
        <el-input-number
          v-model="queryParams.minPrice"
          placeholder="最小价格"
          :min="1"
          class="!w-110px mr-2"
        />
        <span class="mx-2">-</span>
        <el-input-number
          v-model="queryParams.maxPrice"
          placeholder="最大价格"
          :min="1"
          class="!w-110px"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['trade:battery-swap-pricing-rule:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['trade:battery-swap-pricing-rule:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
        <el-button
          type="warning"
          plain
          @click="handleBatchEnable"
          :disabled="!multipleSelection.length"
          v-hasPermi="['trade:battery-swap-pricing-rule:update']"
        >
          <Icon icon="ep:check" class="mr-5px" /> 批量启用
        </el-button>
        <el-button
          type="info"
          plain
          @click="handleBatchDisable"
          :disabled="!multipleSelection.length"
          v-hasPermi="['trade:battery-swap-pricing-rule:update']"
        >
          <Icon icon="ep:close" class="mr-5px" /> 批量禁用
        </el-button>
        <el-button
          type="danger"
          plain
          @click="handleBatchDelete"
          :disabled="!multipleSelection.length"
          v-hasPermi="['trade:battery-swap-pricing-rule:delete']"
        >
          <Icon icon="ep:delete" class="mr-5px" /> 批量删除
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-loading="loading"
      :data="list"
      @selection-change="handleSelectionChange"
      show-overflow-tooltip
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="规则名称" align="center" prop="name" min-width="150" />
      <el-table-column label="优先级" align="center" prop="priority" width="80" />
      <el-table-column label="状态" align="center" prop="statusDisplay" width="80">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="适用站点" align="center" min-width="120">
        <template #default="scope">
          <span>{{ formatStationDisplay(scope.row.stationCodes) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="日期类型" align="center" prop="dateTypeDisplay" width="100" />
      <el-table-column label="时间范围" align="center" prop="timeDisplay" min-width="120" />
      <el-table-column label="电价(元/度)" align="center" prop="priceDisplay" width="100" />
      <el-table-column label="匹配次数" align="center" prop="matchCount" width="100" />
      <el-table-column label="有效期" align="center" prop="validPeriodDisplay" min-width="150" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" width="200" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['trade:battery-swap-pricing-rule:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="primary"
            @click="handleCopy(scope.row)"
            v-hasPermi="['trade:battery-swap-pricing-rule:create']"
          >
            复制
          </el-button>
          <el-button
            link
            type="primary"
            @click="handleTest(scope.row)"
            v-hasPermi="['trade:battery-swap-pricing-rule:query']"
          >
            测试
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['trade:battery-swap-pricing-rule:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <BatterySwapPricingRuleForm ref="formRef" @success="getList" />
  
  <!-- 测试弹窗 -->
  <BatterySwapPricingRuleTest ref="testRef" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import { DICT_TYPE } from '@/utils/dict'
import download from '@/utils/download'
import {
  getBatterySwapPricingRulePage,
  deleteBatterySwapPricingRule,
  exportBatterySwapPricingRule,
  batchUpdateBatterySwapPricingRuleStatus,
  batchDeleteBatterySwapPricingRules,
  copyBatterySwapPricingRule,
  BATTERY_SWAP_PRICING_RULE_STATUS_OPTIONS,
  BATTERY_SWAP_PRICING_RULE_DATE_TYPE_OPTIONS,
  type BatterySwapPricingRuleVO
} from '@/api/mall/trade/batterySwapPricingRule'
import { BusSiteApi, type BusSiteVO } from '@/api/site/bussite'
import BatterySwapPricingRuleForm from './BatterySwapPricingRuleForm.vue'
import BatterySwapPricingRuleTest from './BatterySwapPricingRuleTest.vue'

/** 换电动态定价规则 列表 */
defineOptions({ name: 'BatterySwapPricingRule' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<BatterySwapPricingRuleVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  status: undefined,
  stationCode: undefined,
  dateType: undefined,
  minPriority: undefined,
  maxPriority: undefined,
  minPrice: undefined,
  maxPrice: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const multipleSelection = ref<BatterySwapPricingRuleVO[]>([]) // 多选

// 站点选择相关
const stationOptions = ref<BusSiteVO[]>([]) // 站点选项数据

/** 加载站点数据 */
const loadStationOptions = async () => {
  try {
    const response = await BusSiteApi.getSimpleBusSiteList()
    if (Array.isArray(response)) {
      stationOptions.value = response
    } else if (response && Array.isArray(response.data)) {
      stationOptions.value = response.data
    } else {
      stationOptions.value = []
    }
  } catch (error) {
    console.error('加载站点数据失败:', error)
    stationOptions.value = []
  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await getBatterySwapPricingRulePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await deleteBatterySwapPricingRule(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await exportBatterySwapPricingRule(queryParams)
    download.excel(data, '换电动态定价规则.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 多选操作 */
const handleSelectionChange = (selection: BatterySwapPricingRuleVO[]) => {
  multipleSelection.value = selection
}

/** 批量启用 */
const handleBatchEnable = async () => {
  try {
    await message.confirm('确认要启用选中的规则吗？')
    const ids = multipleSelection.value.map(item => item.id!)
    await batchUpdateBatterySwapPricingRuleStatus(ids, 0)
    message.success('批量启用成功')
    await getList()
  } catch {}
}

/** 批量禁用 */
const handleBatchDisable = async () => {
  try {
    await message.confirm('确认要禁用选中的规则吗？')
    const ids = multipleSelection.value.map(item => item.id!)
    await batchUpdateBatterySwapPricingRuleStatus(ids, 1)
    message.success('批量禁用成功')
    await getList()
  } catch {}
}

/** 批量删除 */
const handleBatchDelete = async () => {
  try {
    await message.delConfirm('确认要删除选中的规则吗？')
    const ids = multipleSelection.value.map(item => item.id!)
    await batchDeleteBatterySwapPricingRules(ids)
    message.success('批量删除成功')
    await getList()
  } catch {}
}

/** 复制规则 */
const handleCopy = async (row: BatterySwapPricingRuleVO) => {
  try {
    const result = await message.prompt('请输入新规则名称', '复制规则', {
      inputValue: `${row.name}_副本`
    })

    // message.prompt 返回的是一个对象，需要提取 value 属性
    const newName = typeof result === 'string' ? result : result.value

    if (!newName || newName.trim() === '') {
      message.warning('规则名称不能为空')
      return
    }

    console.log('[handleCopy] 复制规则:', { id: row.id, originalName: row.name, newName })

    await copyBatterySwapPricingRule(row.id!, newName.trim())
    message.success('复制成功')
    await getList()
  } catch (error) {
    console.error('[handleCopy] 复制失败:', error)
  }
}

/** 测试规则 */
const testRef = ref()
const handleTest = (row: BatterySwapPricingRuleVO) => {
  testRef.value.open(row)
}

/** 格式化站点显示 */
const formatStationDisplay = (stationCodes?: string) => {
  if (!stationCodes || stationCodes.trim() === '') {
    return '全部站点'
  }

  const codes = stationCodes.split(',').map(code => code.trim()).filter(code => code)
  if (codes.length === 0) {
    return '全部站点'
  }

  // 根据站点ID或站点编码查找站点名称
  const stationNames = codes.map(code => {
    // 尝试按siteId查找
    let station = stationOptions.value.find(s => s.siteId.toString() === code)
    // 如果没找到，尝试按siteNo查找
    if (!station) {
      station = stationOptions.value.find(s => s.siteNo === code)
    }
    // 如果还是没找到，尝试按siteName查找
    if (!station) {
      station = stationOptions.value.find(s => s.siteName === code)
    }
    return station ? station.siteName : code
  })

  // 如果站点太多，只显示前几个
  if (stationNames.length > 3) {
    return `${stationNames.slice(0, 3).join('、')} 等${stationNames.length}个站点`
  }

  return stationNames.join('、')
}

/** 初始化 **/
onMounted(async () => {
  await loadStationOptions()
  await getList()
})
</script>
