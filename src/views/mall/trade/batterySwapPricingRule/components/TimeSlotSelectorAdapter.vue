<template>
  <div class="time-slot-selector-adapter">
    <TimeSlotSelector
      v-model="timeSlots"
      @change="handleTimeSlotsChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import TimeSlotSelector from '@/components/TimeSlotSelector/index.vue'

// 时间段接口定义
interface TimeSlot {
  startTime: string
  endTime: string
}

interface Props {
  modelValue?: string
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: TimeSlot[]): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: ''
})

const emit = defineEmits<Emits>()

// 内部使用的时间段数组
const timeSlots = ref<TimeSlot[]>([])

/**
 * 将字符串格式转换为时间段数组
 * "09:00-17:00,19:00-21:00" → [{startTime: "09:00", endTime: "17:00"}, {startTime: "19:00", endTime: "21:00"}]
 */
const stringToTimeSlots = (str: string): TimeSlot[] => {
  if (!str || str.trim() === '') {
    return []
  }
  
  return str.split(',')
    .filter(range => range.trim())
    .map(range => {
      const [startTime, endTime] = range.split('-').map(time => time.trim())
      return { startTime, endTime }
    })
    .filter(slot => slot.startTime && slot.endTime)
}

/**
 * 将时间段数组转换为字符串格式
 * [{startTime: "09:00", endTime: "17:00"}, {startTime: "19:00", endTime: "21:00"}] → "09:00-17:00,19:00-21:00"
 */
const timeSlotsToString = (slots: TimeSlot[]): string => {
  if (!slots || slots.length === 0) {
    return ''
  }
  
  return slots
    .map(slot => `${slot.startTime}-${slot.endTime}`)
    .join(',')
}

// 监听外部值变化，转换为内部数组格式
watch(
  () => props.modelValue,
  (newVal) => {
    const newTimeSlots = stringToTimeSlots(newVal || '')
    // 避免无限循环，只有当数据真正不同时才更新
    if (JSON.stringify(newTimeSlots) !== JSON.stringify(timeSlots.value)) {
      timeSlots.value = newTimeSlots
    }
  },
  { immediate: true }
)

// 监听内部数组变化，转换为外部字符串格式
watch(
  timeSlots,
  (newVal) => {
    const stringValue = timeSlotsToString(newVal)
    if (stringValue !== props.modelValue) {
      emit('update:modelValue', stringValue)
      emit('change', newVal)
    }
  },
  { deep: true }
)

// 处理TimeSlotSelector的change事件
const handleTimeSlotsChange = (slots: TimeSlot[]) => {
  // TimeSlotSelector内部已经更新了timeSlots.value
  // 这里的watch会自动处理转换和emit
  console.log('TimeSlotSelectorAdapter - 时间段变化:', slots)
}

// 暴露一些有用的计算属性和方法
const totalDuration = computed(() => {
  return timeSlots.value.reduce((total, slot) => {
    const startHour = parseTimeToHour(slot.startTime)
    const endHour = parseTimeToHour(slot.endTime)
    return total + (endHour - startHour)
  }, 0)
})

const coverageRate = computed(() => {
  if (totalDuration.value === 0) return 0
  const rate = (totalDuration.value / 24) * 100
  return Math.round(rate * 10) / 10
})

const parseTimeToHour = (time: string): number => {
  const [hour, minute] = time.split(':').map(Number)
  return hour + (minute || 0) / 60
}

// 暴露方法供父组件使用
defineExpose({
  getTimeSlots: () => timeSlots.value,
  setTimeSlots: (slots: TimeSlot[]) => {
    timeSlots.value = [...slots]
  },
  getTotalDuration: () => totalDuration.value,
  getCoverageRate: () => coverageRate.value,
  clearAll: () => {
    timeSlots.value = []
  }
})
</script>

<style scoped>
.time-slot-selector-adapter {
  width: 100%;
}
</style>
