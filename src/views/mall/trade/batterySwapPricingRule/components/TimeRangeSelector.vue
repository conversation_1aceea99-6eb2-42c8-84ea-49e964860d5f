<template>
  <div class="time-range-selector">
    <!-- 时间段列表 -->
    <div v-if="timeSlots.length > 0" class="time-slots-display mb-4">
      <div class="slots-header">
        <span class="text-sm text-gray-600">已选择时间段：</span>
        <el-button size="small" type="text" @click="clearAllRanges" class="clear-all-btn">
          <el-icon><Delete /></el-icon>
          清空全部
        </el-button>
      </div>
      <transition-group name="slot-fade" tag="div" class="slots-container">
        <div
          v-for="(slot, index) in timeSlots"
          :key="`${slot.startTime}-${slot.endTime}`"
          class="time-slot-item"
        >
          <div class="slot-content">
            <el-icon class="slot-icon"><Clock /></el-icon>
            <span class="slot-text">{{ slot.startTime }} - {{ slot.endTime }}</span>
            <span class="slot-duration">({{ calculateDuration(slot) }})</span>
          </div>
          <el-button
            size="small"
            type="text"
            class="remove-btn"
            @click="removeTimeSlot(index)"
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </transition-group>
    </div>

    <!-- 添加时间段 -->
    <div class="add-time-section mb-4">
      <div class="section-title">
        <el-icon><Plus /></el-icon>
        <span>添加时间段</span>
      </div>
      <div class="time-input-row">
        <el-time-picker
          v-model="startTime"
          placeholder="开始时间"
          format="HH:mm"
          value-format="HH:mm"
          class="time-picker"
          :clearable="false"
        />
        <span class="time-separator">至</span>
        <el-time-picker
          v-model="endTime"
          placeholder="结束时间"
          format="HH:mm"
          value-format="HH:mm"
          class="time-picker"
          :clearable="false"
        />
        <el-button
          type="primary"
          size="default"
          @click="addTimeRange"
          :disabled="!canAddRange"
          class="add-btn"
        >
          <el-icon><Plus /></el-icon>
          添加
        </el-button>
      </div>
    </div>

    <!-- 快捷设置 -->
    <div class="quick-settings mb-4">
      <div class="section-title">
        <el-icon><Lightning /></el-icon>
        <span>快捷设置</span>
      </div>
      <div class="quick-buttons">
        <el-button-group>
          <el-button size="small" @click="setQuickRange('peak')" class="quick-btn">
            <el-icon><Sunny /></el-icon>
            峰时电价
            <span class="time-hint">(09:00-22:00)</span>
          </el-button>
          <el-button size="small" @click="setQuickRange('valley')" class="quick-btn">
            <el-icon><Moon /></el-icon>
            谷时电价
            <span class="time-hint">(00:00-06:00)</span>
          </el-button>
        </el-button-group>
        <el-button-group class="ml-2">
          <el-button size="small" @click="setQuickRange('offPeak')" class="quick-btn">
            <el-icon><PartlyCloudy /></el-icon>
            平时电价
            <span class="time-hint">(06:00-09:00,22:00-24:00)</span>
          </el-button>
          <el-button size="small" @click="setQuickRange('workHours')" class="quick-btn">
            <el-icon><OfficeBuilding /></el-icon>
            工作时间
            <span class="time-hint">(08:00-18:00)</span>
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 24小时时间轴可视化 -->
    <div class="time-axis">
      <div class="section-title">
        <el-icon><View /></el-icon>
        <span>时间轴预览</span>
        <div class="axis-legend">
          <span class="legend-item">
            <span class="legend-color active"></span>
            已选择
          </span>
          <span class="legend-item">
            <span class="legend-color inactive"></span>
            未选择
          </span>
        </div>
      </div>
      <div class="axis-container">
        <!-- 时间标签行 -->
        <div class="time-labels">
          <span
            v-for="hour in 25"
            :key="`label-${hour - 1}`"
            class="time-label"
            :class="{ 'major-label': (hour - 1) % 6 === 0 }"
          >
            {{ String(hour - 1).padStart(2, '0') }}:00
          </span>
        </div>
        <!-- 时间轴主体 -->
        <div class="axis-line">
          <div
            v-for="hour in 24"
            :key="`block-${hour - 1}`"
            :class="[
              'hour-block',
              isHourInRange(hour - 1) ? 'active' : 'inactive'
            ]"
            :title="getHourTooltip(hour - 1)"
            @click="toggleHour(hour - 1)"
          >
            <div class="hour-content">
              <span class="hour-number">{{ String(hour - 1).padStart(2, '0') }}</span>
            </div>
          </div>
        </div>
        <!-- 统计信息 -->
        <div class="axis-stats">
          <span class="stat-item">
            <el-icon><Timer /></el-icon>
            总时长：{{ totalDuration }}小时
          </span>
          <span class="stat-item">
            <el-icon><DataLine /></el-icon>
            覆盖率：{{ coveragePercentage }}%
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  Clock, Delete, Plus, Close, Lightning, Sunny, Moon,
  PartlyCloudy, OfficeBuilding, View, Timer, DataLine
} from '@element-plus/icons-vue'

// 时间段接口定义
interface TimeSlot {
  startTime: string
  endTime: string
}

interface Props {
  modelValue?: string
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: TimeSlot[]): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: ''
})

const emit = defineEmits<Emits>()

const startTime = ref('')
const endTime = ref('')
const timeSlots = ref<TimeSlot[]>([])

// 监听外部值变化
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal && newVal !== timeSlots.value.map(slot => `${slot.startTime}-${slot.endTime}`).join(',')) {
      timeSlots.value = newVal.split(',')
        .filter(range => range.trim())
        .map(range => {
          const [startTime, endTime] = range.split('-')
          return { startTime: startTime.trim(), endTime: endTime.trim() }
        })
    } else if (!newVal) {
      timeSlots.value = []
    }
  },
  { immediate: true }
)

// 监听内部值变化
watch(
  timeSlots,
  (newVal) => {
    const value = newVal.map(slot => `${slot.startTime}-${slot.endTime}`).join(',')
    if (value !== props.modelValue) {
      emit('update:modelValue', value)
      emit('change', newVal)
    }
  },
  { deep: true }
)

// 计算属性
const canAddRange = computed(() => {
  return startTime.value && endTime.value && startTime.value < endTime.value
})

const totalDuration = computed(() => {
  return timeSlots.value.reduce((total, slot) => {
    const duration = calculateDurationInHours(slot)
    return total + duration
  }, 0)
})

const coveragePercentage = computed(() => {
  return Math.round((totalDuration.value / 24) * 100)
})

// 添加时间段
const addTimeRange = () => {
  if (!canAddRange.value) return

  const newSlot: TimeSlot = {
    startTime: startTime.value,
    endTime: endTime.value
  }

  // 检查是否重复
  if (timeSlots.value.some(slot =>
    slot.startTime === newSlot.startTime && slot.endTime === newSlot.endTime
  )) {
    ElMessage.warning('该时间段已存在')
    return
  }

  // 检查是否有重叠
  if (hasOverlap(newSlot.startTime, newSlot.endTime)) {
    ElMessage.warning('时间段不能重叠')
    return
  }

  timeSlots.value.push(newSlot)

  // 清空输入
  startTime.value = ''
  endTime.value = ''

  // 排序
  sortTimeSlots()

  ElMessage.success('时间段添加成功')
}

// 移除时间段
const removeTimeSlot = (index: number) => {
  timeSlots.value.splice(index, 1)
  ElMessage.success('时间段已移除')
}

// 检查时间段是否重叠
const hasOverlap = (newStart: string, newEnd: string): boolean => {
  const newStartMinutes = timeToMinutes(newStart)
  const newEndMinutes = timeToMinutes(newEnd)

  return timeSlots.value.some(slot => {
    const startMinutes = timeToMinutes(slot.startTime)
    const endMinutes = timeToMinutes(slot.endTime)

    return !(newEndMinutes <= startMinutes || newStartMinutes >= endMinutes)
  })
}

// 时间转换为分钟数
const timeToMinutes = (time: string): number => {
  const [hours, minutes] = time.split(':').map(Number)
  return hours * 60 + minutes
}

// 排序时间段
const sortTimeSlots = () => {
  timeSlots.value.sort((a, b) => {
    const aStart = timeToMinutes(a.startTime)
    const bStart = timeToMinutes(b.startTime)
    return aStart - bStart
  })
}

// 快捷设置
const setQuickRange = (type: string) => {
  timeSlots.value = []

  switch (type) {
    case 'peak':
      timeSlots.value = [{ startTime: '09:00', endTime: '22:00' }]
      break
    case 'valley':
      timeSlots.value = [{ startTime: '00:00', endTime: '06:00' }]
      break
    case 'offPeak':
      timeSlots.value = [
        { startTime: '06:00', endTime: '09:00' },
        { startTime: '22:00', endTime: '24:00' }
      ]
      break
    case 'workHours':
      timeSlots.value = [{ startTime: '08:00', endTime: '18:00' }]
      break
  }

  ElMessage.success(`已设置${getQuickRangeLabel(type)}时间段`)
}

// 获取快捷设置标签
const getQuickRangeLabel = (type: string): string => {
  const labels = {
    peak: '峰时电价',
    valley: '谷时电价',
    offPeak: '平时电价',
    workHours: '工作时间'
  }
  return labels[type] || '未知'
}

// 清空所有时间段
const clearAllRanges = () => {
  if (timeSlots.value.length === 0) {
    ElMessage.info('当前没有时间段需要清空')
    return
  }

  ElMessageBox.confirm('确认要清空所有时间段吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    timeSlots.value = []
    ElMessage.success('已清空所有时间段')
  }).catch(() => {
    // 用户取消
  })
}

// 计算时间段持续时间
const calculateDuration = (slot: TimeSlot): string => {
  const durationHours = calculateDurationInHours(slot)
  const hours = Math.floor(durationHours)
  const minutes = Math.round((durationHours - hours) * 60)

  if (hours === 0) {
    return `${minutes}分钟`
  } else if (minutes === 0) {
    return `${hours}小时`
  } else {
    return `${hours}小时${minutes}分钟`
  }
}

// 计算时间段持续时间（小时）
const calculateDurationInHours = (slot: TimeSlot): number => {
  const startMinutes = timeToMinutes(slot.startTime)
  const endMinutes = timeToMinutes(slot.endTime)

  // 处理跨天的情况
  let duration = endMinutes - startMinutes
  if (duration < 0) {
    duration += 24 * 60 // 加上一天的分钟数
  }

  return duration / 60 // 转换为小时
}

// 检查某个小时是否在时间段内
const isHourInRange = (hour: number): boolean => {
  return timeSlots.value.some(slot => {
    const startHour = parseInt(slot.startTime.split(':')[0])
    const endHour = parseInt(slot.endTime.split(':')[0])
    const endMinute = parseInt(slot.endTime.split(':')[1])

    // 处理跨天的情况
    if (endHour === 0 && endMinute === 0) {
      return hour >= startHour || hour < 24
    }

    // 处理整点结束的情况
    if (endMinute === 0) {
      return hour >= startHour && hour < endHour
    }

    return hour >= startHour && hour <= endHour
  })
}

// 获取小时的提示文本
const getHourTooltip = (hour: number): string => {
  const startTime = `${String(hour).padStart(2, '0')}:00`
  const endTime = `${String(hour + 1).padStart(2, '0')}:00`
  const status = isHourInRange(hour) ? '已选择' : '未选择'
  return `${startTime}-${endTime} (${status})`
}

// 点击时间轴上的小时块
const toggleHour = (hour: number) => {
  const isSelected = isHourInRange(hour)

  if (isSelected) {
    // 如果已选择，则移除包含该小时的时间段
    removeHourFromTimeSlots(hour)
  } else {
    // 如果未选择，则添加该小时的时间段
    addHourToTimeSlots(hour)
  }
}

// 从时间段中移除指定小时
const removeHourFromTimeSlots = (hour: number) => {
  // 找出包含该小时的所有时间段
  const affectedSlots = timeSlots.value.filter(slot => {
    const startHour = parseInt(slot.startTime.split(':')[0])
    const endHour = parseInt(slot.endTime.split(':')[0])

    // 处理跨天的情况
    if (endHour < startHour) {
      return hour >= startHour || hour < endHour
    }

    return hour >= startHour && hour < endHour
  })

  if (affectedSlots.length === 0) return

  // 从时间段列表中移除这些时间段
  affectedSlots.forEach(slot => {
    const index = timeSlots.value.findIndex(s =>
      s.startTime === slot.startTime && s.endTime === slot.endTime
    )
    if (index !== -1) {
      timeSlots.value.splice(index, 1)
    }
  })

  ElMessage.success(`已移除 ${String(hour).padStart(2, '0')}:00-${String(hour + 1).padStart(2, '0')}:00 时间段`)
}

// 添加指定小时到时间段
const addHourToTimeSlots = (hour: number) => {
  const newSlot: TimeSlot = {
    startTime: `${String(hour).padStart(2, '0')}:00`,
    endTime: `${String(hour + 1).padStart(2, '0')}:00`
  }

  // 检查是否有重叠
  if (hasOverlap(newSlot.startTime, newSlot.endTime)) {
    ElMessage.warning('时间段不能重叠')
    return
  }

  timeSlots.value.push(newSlot)
  sortTimeSlots()

  ElMessage.success(`已添加 ${newSlot.startTime}-${newSlot.endTime} 时间段`)
}
</script>

<style scoped>
.time-range-selector {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 时间段显示区域 */
.time-slots-display {
  background: white;
  border-radius: 6px;
  padding: 16px;
  border: 1px solid #e4e7ed;
}

.slots-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.clear-all-btn {
  color: #f56c6c;
  padding: 4px 8px;
}

.clear-all-btn:hover {
  background-color: #fef0f0;
}

.slots-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.time-slot-item {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 14px;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
}

.time-slot-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(64, 158, 255, 0.4);
}

.slot-content {
  display: flex;
  align-items: center;
  gap: 6px;
}

.slot-icon {
  font-size: 16px;
}

.slot-text {
  font-weight: 500;
}

.slot-duration {
  font-size: 12px;
  opacity: 0.9;
  margin-left: 4px;
}

.remove-btn {
  margin-left: 8px;
  color: white;
  padding: 2px;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.remove-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* 动画效果 */
.slot-fade-enter-active,
.slot-fade-leave-active {
  transition: all 0.3s ease;
}

.slot-fade-enter-from {
  opacity: 0;
  transform: scale(0.8) translateY(-10px);
}

.slot-fade-leave-to {
  opacity: 0;
  transform: scale(0.8) translateY(10px);
}

/* 添加时间段区域 */
.add-time-section {
  background: white;
  border-radius: 6px;
  padding: 16px;
  border: 1px solid #e4e7ed;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 500;
  color: #303133;
}

.time-input-row {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.time-picker {
  width: 140px;
}

.time-separator {
  color: #909399;
  font-weight: 500;
  margin: 0 4px;
}

.add-btn {
  min-width: 80px;
}

/* 快捷设置区域 */
.quick-settings {
  background: white;
  border-radius: 6px;
  padding: 16px;
  border: 1px solid #e4e7ed;
}

.quick-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.quick-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.quick-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.time-hint {
  font-size: 12px;
  opacity: 0.8;
  margin-left: 4px;
}

/* 时间轴区域 */
.time-axis {
  background: white;
  border-radius: 6px;
  padding: 16px;
  border: 1px solid #e4e7ed;
}

.axis-legend {
  display: flex;
  gap: 16px;
  margin-left: auto;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.active {
  background-color: #409eff;
}

.legend-color.inactive {
  background-color: #f5f7fa;
}

.axis-container {
  background: #fafbfc;
  border-radius: 6px;
  padding: 16px;
  border: 1px solid #e4e7ed;
}

.time-labels {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  padding: 0 2px;
}

.time-label {
  font-size: 11px;
  color: #909399;
  text-align: center;
  min-width: 35px;
}

.time-label.major-label {
  font-weight: 600;
  color: #606266;
}

.axis-line {
  display: flex;
  height: 50px;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.hour-block {
  flex: 1;
  border-right: 1px solid #e4e7ed;
  position: relative;
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hour-block:last-child {
  border-right: none;
}

.hour-block.active {
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hour-block.inactive {
  background-color: #f5f7fa;
}

.hour-block:hover {
  transform: scale(1.05);
  z-index: 1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.hour-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}

.hour-number {
  font-size: 10px;
  font-weight: 500;
  color: #666;
}

.hour-block.active .hour-number {
  color: white;
  font-weight: 600;
}

.axis-stats {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e4e7ed;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  color: #606266;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .time-input-row {
    flex-direction: column;
    align-items: stretch;
  }

  .time-picker {
    width: 100%;
  }

  .quick-buttons {
    flex-direction: column;
  }

  .axis-stats {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
