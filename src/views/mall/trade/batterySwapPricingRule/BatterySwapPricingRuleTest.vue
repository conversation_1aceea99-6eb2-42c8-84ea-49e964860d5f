<template>
  <Dialog title="规则匹配测试" v-model="dialogVisible" width="800px">
    <div v-loading="loading">
      <!-- 测试参数 -->
      <el-form :model="testParams" label-width="120px" class="mb-4">
        <el-form-item label="换电站" required>
          <el-select
            v-model="selectedStationId"
            filterable
            placeholder="请选择换电站进行测试"
            class="!w-300px"
            @change="handleStationChange"
            clearable
          >
            <el-option
              v-for="station in stationOptions"
              :key="station.siteId"
              :label="`${station.siteName} (${station.siteNo})`"
              :value="station.siteId"
            >
              <div class="station-option">
                <div class="station-main">
                  <span class="station-name">{{ station.siteName }}</span>
                  <span class="station-no">{{ station.siteNo }}</span>
                </div>
                <div class="station-info">
                  <span class="station-address">{{ station.siteAdress }}</span>
                  <el-tag
                    :type="station.siteStatus === 1 ? 'success' : 'danger'"
                    size="small"
                  >
                    {{ station.siteStatus === 1 ? '营业中' : '暂停使用' }}
                  </el-tag>
                </div>
              </div>
            </el-option>
          </el-select>
          <div class="text-xs text-gray-500 mt-1">
            选择要测试的换电站
          </div>
        </el-form-item>
        <el-form-item label="换电时间">
          <el-date-picker
            v-model="testParams.swapTime"
            type="datetime"
            placeholder="选择换电时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            class="!w-300px"
          />
          <div class="text-xs text-gray-500 mt-1">
            留空使用当前时间
          </div>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="runTest" :disabled="!selectedStationId">
            开始测试
          </el-button>
          <el-button @click="resetTest">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 测试结果 -->
      <div v-if="testResult" class="test-result">
        <el-divider content-position="left">测试结果</el-divider>
        
        <div v-if="testResult && testResult.matchedRule" class="matched-rule">
          <el-alert
            title="找到匹配规则"
            type="success"
            :closable="false"
            class="mb-4"
          >
            <template #default>
              <div class="rule-info">
                <div class="rule-header">
                  <span class="rule-name">{{ testResult.matchedRule.name }}</span>
                  <el-tag :type="testResult.matchedRule.status === 0 ? 'success' : 'danger'" size="small">
                    {{ testResult.matchedRule.statusDisplay }}
                  </el-tag>
                </div>
                <div class="rule-details mt-2">
                  <div class="detail-item">
                    <span class="label">优先级：</span>
                    <span class="value">{{ testResult.matchedRule.priority }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">电价：</span>
                    <span class="value price">{{ testResult.matchedRule.priceDisplay }} 元/度</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">适用站点：</span>
                    <span class="value">{{ testResult.matchedRule.stationDisplay }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">日期类型：</span>
                    <span class="value">{{ testResult.matchedRule.dateTypeDisplay }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">时间范围：</span>
                    <span class="value">{{ testResult.matchedRule.timeDisplay }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">有效期：</span>
                    <span class="value">{{ testResult.matchedRule.validPeriodDisplay }}</span>
                  </div>
                </div>

                <!-- 优先级提示 -->
                <div v-if="isMatchedRuleDifferentFromCurrent()" class="priority-warning mt-3 p-3 border rounded"
                     :class="getPriorityRelation()?.type === 'higher' ? 'bg-blue-50 border-blue-200' :
                             getPriorityRelation()?.type === 'lower' ? 'bg-orange-50 border-orange-200' :
                             'bg-yellow-50 border-yellow-200'">
                  <div class="flex items-start gap-2">
                    <el-icon class="mt-0.5"
                             :class="getPriorityRelation()?.type === 'higher' ? 'text-blue-600' :
                                     getPriorityRelation()?.type === 'lower' ? 'text-orange-600' :
                                     'text-yellow-600'">
                      <Warning />
                    </el-icon>
                    <div :class="getPriorityRelation()?.type === 'higher' ? 'text-blue-800' :
                                 getPriorityRelation()?.type === 'lower' ? 'text-orange-800' :
                                 'text-yellow-800'">
                      <p class="font-medium">注意：匹配到的不是当前测试的规则</p>
                      <p class="text-sm mt-1">
                        {{ getPriorityRelation()?.message }}
                        <strong>"{{ testResult.matchedRule.name }}"</strong>
                        （优先级 {{ testResult.matchedRule.priority }}）
                      </p>
                      <p class="text-sm mt-1">
                        当前规则 <strong>"{{ currentRule?.name }}"</strong>
                        的优先级为 {{ currentRule?.priority }}
                      </p>
                      <p class="text-sm mt-1 opacity-90">
                        {{ getPriorityRelation()?.description }}
                      </p>
                      <p class="text-sm mt-2">
                        <el-button type="primary" size="small" @click="showPriorityHelp">
                          了解优先级规则
                        </el-button>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </el-alert>
        </div>

        <div v-else class="no-match">
          <el-alert
            title="未找到匹配规则"
            type="warning"
            :closable="false"
            class="mb-4"
          >
            <template #default>
              <div>
                <p>在当前条件下未找到匹配的定价规则，将使用默认价格。</p>
                <p class="mt-2">
                  <span class="text-gray-600">测试条件：</span>
                  <span>站点 {{ getSelectedStationNames() }}，时间 {{ displayTime }}</span>
                </p>
              </div>
            </template>
          </el-alert>
        </div>

        <!-- 测试详情 -->
        <div class="test-details">
          <el-descriptions title="测试详情" :column="2" border>
            <el-descriptions-item label="测试站点">
              {{ getSelectedStationNames() }}
            </el-descriptions-item>
            <el-descriptions-item label="测试时间">
              {{ displayTime }}
            </el-descriptions-item>
            <el-descriptions-item label="星期">
              {{ getWeekday(testParams.swapTime || new Date().toISOString()) }}
            </el-descriptions-item>
            <el-descriptions-item label="日期类型">
              {{ getDateType(testParams.swapTime || new Date().toISOString()) }}
            </el-descriptions-item>
            <el-descriptions-item label="匹配结果" :span="2">
              <el-tag :type="(testResult && testResult.matchedRule) ? 'success' : 'warning'">
                {{ (testResult && testResult.matchedRule) ? '匹配成功' : '无匹配规则' }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 当前规则信息 -->
      <div v-if="currentRule" class="current-rule mt-4">
        <el-divider content-position="left">当前规则信息</el-divider>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="规则名称">
            {{ currentRule.name }}
          </el-descriptions-item>
          <el-descriptions-item label="优先级">
            {{ currentRule.priority }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="currentRule.status" />
          </el-descriptions-item>
          <el-descriptions-item label="电价">
            {{ currentRule.priceDisplay }} 元/度
          </el-descriptions-item>
          <el-descriptions-item label="适用站点">
            {{ currentRule.stationDisplay }}
          </el-descriptions-item>
          <el-descriptions-item label="日期类型">
            {{ currentRule.dateTypeDisplay }}
          </el-descriptions-item>
          <el-descriptions-item label="时间范围">
            {{ currentRule.timeDisplay }}
          </el-descriptions-item>
          <el-descriptions-item label="有效期">
            {{ currentRule.validPeriodDisplay }}
          </el-descriptions-item>
          <el-descriptions-item label="匹配次数">
            {{ currentRule.matchCount || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="最后匹配">
            {{ currentRule.lastMatchedTime ? formatDate(new Date(currentRule.lastMatchedTime)) : '从未匹配' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <template #footer>
      <el-button @click="dialogVisible = false">关闭</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { h } from 'vue'
import { DICT_TYPE } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import {
  testBatterySwapPricingRule,
  type BatterySwapPricingRuleVO
} from '@/api/mall/trade/batterySwapPricingRule'
import { BusSiteApi, type BusSiteVO } from '@/api/site/bussite'

/** 换电动态定价规则测试 */
defineOptions({ name: 'BatterySwapPricingRuleTest' })

const message = useMessage()

const dialogVisible = ref(false)
const loading = ref(false)
const currentRule = ref<BatterySwapPricingRuleVO>()

// 站点选择相关
const selectedStationId = ref<number>() // 选中的站点ID
const stationOptions = ref<BusSiteVO[]>([]) // 站点选项数据

const testParams = ref({
  stationCode: '',
  swapTime: ''
})

const testResult = ref<{
  matchedRule?: BatterySwapPricingRuleVO
}>({
  matchedRule: undefined
})

// 显示时间
const displayTime = computed(() => {
  return testParams.value.swapTime || new Date().toLocaleString()
})

/** 打开弹窗 */
const open = (rule?: BatterySwapPricingRuleVO) => {
  dialogVisible.value = true
  currentRule.value = rule
  resetTest()
}

defineExpose({ open })

/** 处理站点选择变化 */
const handleStationChange = (stationId: number) => {
  selectedStationId.value = stationId
  // 将站点ID转换为字符串存储到测试参数中
  testParams.value.stationCode = stationId ? stationId.toString() : ''
}

/** 加载站点数据 */
const loadStationOptions = async () => {
  try {
    const response = await BusSiteApi.getSimpleBusSiteList()
    if (Array.isArray(response)) {
      stationOptions.value = response
    } else if (response && Array.isArray(response.data)) {
      stationOptions.value = response.data
    } else {
      stationOptions.value = []
    }
  } catch (error) {
    console.error('加载站点数据失败:', error)
    stationOptions.value = []
  }
}

/** 运行测试 */
const runTest = async () => {
  if (!selectedStationId.value) {
    message.warning('请选择换电站')
    return
  }

  loading.value = true
  try {
    // 格式化时间参数，确保后端能正确解析
    let formattedSwapTime = testParams.value.swapTime
    if (formattedSwapTime && typeof formattedSwapTime === 'string') {
      // 确保时间格式正确，后端的parseSwapTime方法支持多种格式
      // 主要格式：yyyy-MM-dd HH:mm:ss
      console.log('[runTest] 原始时间格式:', formattedSwapTime)
    }

    // 获取选中的站点信息
    const selectedStation = stationOptions.value.find(s => s.siteId === selectedStationId.value)
    const stationCode = selectedStationId.value.toString()

    console.log('[runTest] 测试站点信息:', {
      stationId: selectedStationId.value,
      stationCode: stationCode,
      stationName: selectedStation?.siteName,
      stationNo: selectedStation?.siteNo
    })

    const result = await testBatterySwapPricingRule(
      stationCode,
      formattedSwapTime
    )

    console.log('[runTest] API返回结果:', {
      fullResult: result,
      resultType: typeof result,
      isNull: result === null,
      isUndefined: result === undefined
    })

    // 处理API返回结果：
    // 1. 如果result为空，表示没有匹配到规则（正常情况）
    // 2. 如果result.data是null，表示没有匹配到规则（正常情况）
    // 3. 如果result.data是对象，表示匹配到了规则
    // 4. 如果规则数据直接在result根级别，也是匹配到了规则
    let matchedRule = null

    if (!result) {
      // API返回结果为空，表示没有匹配到规则，这是正常情况
      console.log('[runTest] API返回结果为空，没有匹配到规则')
      matchedRule = null
    } else {
      console.log('[runTest] API返回详细信息:', {
        data: result.data,
        dataType: typeof result.data,
        isNull: result.data === null,
        isUndefined: result.data === undefined,
        hasData: !!result.data,
        // 检查是否规则数据直接在result中
        hasDirectRuleData: !!(result.id && result.name),
        resultKeys: Object.keys(result)
      })

      if (result.data === null) {
        // API返回data为null，表示没有匹配到规则，这是正常情况
        console.log('[runTest] API返回data为null，没有匹配到规则')
        matchedRule = null
      } else if (result.data && typeof result.data === 'object') {
        // 标准情况：数据在result.data中
        matchedRule = result.data
      } else if (result.id && result.name) {
        // 特殊情况：规则数据直接在result根级别
        matchedRule = result
      }
    }

    testResult.value = {
      matchedRule: matchedRule
    }

    console.log('[runTest] 设置测试结果:', {
      testResult: testResult.value,
      matchedRule: testResult.value.matchedRule,
      hasMatchedRule: !!testResult.value.matchedRule,
      matchedRuleName: testResult.value.matchedRule?.name,
      matchedRulePriority: testResult.value.matchedRule?.priority
    })

    // 根据匹配结果显示不同的提示信息
    const hasMatchedRule = !!testResult.value.matchedRule
    const stationInfo = `（测试站点：${selectedStation?.siteName || stationCode}）`

    if (hasMatchedRule) {
      message.success(`找到匹配规则：${testResult.value.matchedRule.name}${stationInfo}`)
    } else {
      message.warning(`未找到匹配规则${stationInfo}，请检查规则配置或测试条件`)
    }
  } catch (error) {
    console.error('[runTest] 测试失败:', error)
    message.error('测试失败，请检查参数或联系管理员')
  } finally {
    loading.value = false
  }
}

/** 重置测试 */
const resetTest = () => {
  selectedStationId.value = undefined
  testParams.value = {
    stationCode: '',
    swapTime: ''
  }
  testResult.value = undefined
}

/** 获取选中站点名称 */
const getSelectedStationNames = () => {
  if (!selectedStationId.value) {
    return '未选择'
  }

  const station = stationOptions.value.find(s => s.siteId === selectedStationId.value)
  return station ? station.siteName : selectedStationId.value.toString()
}

/** 检查匹配到的规则是否与当前规则不同 */
const isMatchedRuleDifferentFromCurrent = () => {
  if (!testResult.value?.matchedRule || !currentRule.value) {
    return false
  }
  return testResult.value.matchedRule.id !== currentRule.value.id
}

/** 获取优先级关系信息 */
const getPriorityRelation = () => {
  if (!testResult.value?.matchedRule || !currentRule.value) {
    return null
  }

  const matchedPriority = testResult.value.matchedRule.priority
  const currentPriority = currentRule.value.priority

  if (matchedPriority > currentPriority) {
    return {
      type: 'higher',
      message: '由于优先级机制，系统匹配到了优先级更高的规则',
      description: '这是正常的优先级工作机制，高优先级规则会优先匹配'
    }
  } else if (matchedPriority < currentPriority) {
    return {
      type: 'lower',
      message: '当前测试的规则未匹配成功，但匹配到了优先级较低的规则',
      description: '这说明当前规则的匹配条件不满足，系统找到了其他可用的规则'
    }
  } else {
    return {
      type: 'equal',
      message: '匹配到了相同优先级的其他规则',
      description: '在相同优先级下，系统按创建时间等其他条件进行了选择'
    }
  }
}

/** 显示优先级帮助信息 */
const showPriorityHelp = () => {
  message.info({
    message: h('div', [
      h('p', { class: 'mb-2 font-medium' }, '规则优先级说明：'),
      h('ul', { class: 'list-disc list-inside space-y-1 text-sm' }, [
        h('li', '数值越大，优先级越高'),
        h('li', '当多个规则都匹配时，系统选择优先级最高的规则'),
        h('li', '优先级相同时，按创建时间倒序选择'),
        h('li', '建议为特殊场景设置更高的优先级')
      ])
    ]),
    duration: 8000,
    showClose: true
  })
}

/** 初始化 */
onMounted(async () => {
  await loadStationOptions()
})

/** 获取星期 */
const getWeekday = (dateStr: string) => {
  const date = new Date(dateStr)
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  return weekdays[date.getDay()]
}

/** 获取日期类型 */
const getDateType = (dateStr: string) => {
  const date = new Date(dateStr)
  const dayOfWeek = date.getDay()
  
  if (dayOfWeek >= 1 && dayOfWeek <= 5) {
    return '工作日'
  } else {
    return '周末'
  }
}
</script>

<style scoped>
.test-result {
  margin-top: 20px;
}

.rule-info {
  color: #333;
}

.rule-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rule-name {
  font-weight: bold;
  font-size: 16px;
}

.rule-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.detail-item {
  display: flex;
  align-items: center;
}

.label {
  color: #666;
  margin-right: 8px;
  min-width: 80px;
}

.value {
  color: #333;
}

.value.price {
  color: #e6a23c;
  font-weight: bold;
}

.test-details {
  margin-top: 16px;
}

.current-rule {
  border-top: 1px solid #e4e7ed;
  padding-top: 16px;
}

/* 站点选择器样式 */
.station-option {
  padding: 8px 0;
}

.station-main {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.station-name {
  font-weight: 500;
  color: #303133;
}

.station-no {
  color: #909399;
  font-size: 12px;
  background: #f4f4f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.station-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
}

.station-address {
  color: #909399;
  flex: 1;
  margin-right: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
