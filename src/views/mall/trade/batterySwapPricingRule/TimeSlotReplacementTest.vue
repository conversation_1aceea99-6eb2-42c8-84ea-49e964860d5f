<template>
  <div class="time-slot-replacement-test">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>TimeRangeSelector → TimeSlotSelector 替换效果对比</span>
          <el-button @click="clearAll">清空测试</el-button>
        </div>
      </template>

      <div class="comparison-section">
        <!-- 原始TimeRangeSelector -->
        <div class="component-demo">
          <h3>原始 TimeRangeSelector</h3>
          <p class="description">
            手动输入时间 + 点击时间轴，重叠检测，字符串格式数据
          </p>
          <TimeRangeSelector v-model="timeRangeValue" @change="handleTimeRangeChange" />
          <div class="data-display">
            <strong>数据格式：</strong>
            <code>{{ timeRangeValue || '(空)' }}</code>
          </div>
        </div>

        <!-- 新的TimeSlotSelectorAdapter -->
        <div class="component-demo">
          <h3>新的 TimeSlotSelectorAdapter</h3>
          <p class="description">
            拖拽选择 + 智能合并，总时长覆盖率统计，兼容字符串格式
          </p>
          <TimeSlotSelectorAdapter v-model="timeSlotValue" @change="handleTimeSlotChange" />
          <div class="data-display">
            <strong>数据格式：</strong>
            <code>{{ timeSlotValue || '(空)' }}</code>
          </div>
        </div>
      </div>

      <!-- 数据同步测试 -->
      <div class="sync-test-section">
        <h3>数据同步测试</h3>
        <div class="sync-controls">
          <el-button @click="syncToTimeSlot">
            将TimeRange数据同步到TimeSlot →
          </el-button>
          <el-button @click="syncToTimeRange">
            ← 将TimeSlot数据同步到TimeRange
          </el-button>
        </div>
        
        <div class="test-buttons">
          <h4>快速测试数据</h4>
          <div class="button-group">
            <el-button @click="setTestData1">
              峰时电价 (09:00-22:00)
            </el-button>
            <el-button @click="setTestData2">
              多时段 (08:00-12:00,14:00-18:00)
            </el-button>
            <el-button @click="setTestData3">
              复杂时段 (06:00-09:00,12:00-14:00,18:00-22:00)
            </el-button>
          </div>
        </div>
      </div>

      <!-- 功能对比表格 -->
      <div class="feature-comparison">
        <h3>功能对比</h3>
        <el-table :data="featureComparison" border>
          <el-table-column prop="feature" label="功能特性" width="200" />
          <el-table-column prop="timeRange" label="TimeRangeSelector" width="250">
            <template #default="{ row }">
              <el-tag :type="row.timeRange.type">{{ row.timeRange.text }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="timeSlot" label="TimeSlotSelector" width="250">
            <template #default="{ row }">
              <el-tag :type="row.timeSlot.type">{{ row.timeSlot.text }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="advantage" label="优势说明">
            <template #default="{ row }">
              <span :class="row.advantage.class">{{ row.advantage.text }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import TimeRangeSelector from './components/TimeRangeSelector.vue'
import TimeSlotSelectorAdapter from './components/TimeSlotSelectorAdapter.vue'

interface TimeSlot {
  startTime: string
  endTime: string
}

const timeRangeValue = ref('')
const timeSlotValue = ref('')

const handleTimeRangeChange = (slots: TimeSlot[]) => {
  console.log('TimeRange变化:', slots)
}

const handleTimeSlotChange = (slots: TimeSlot[]) => {
  console.log('TimeSlot变化:', slots)
}

const clearAll = () => {
  timeRangeValue.value = ''
  timeSlotValue.value = ''
}

const syncToTimeSlot = () => {
  timeSlotValue.value = timeRangeValue.value
}

const syncToTimeRange = () => {
  timeRangeValue.value = timeSlotValue.value
}

const setTestData1 = () => {
  const testData = '09:00-22:00'
  timeRangeValue.value = testData
  timeSlotValue.value = testData
}

const setTestData2 = () => {
  const testData = '08:00-12:00,14:00-18:00'
  timeRangeValue.value = testData
  timeSlotValue.value = testData
}

const setTestData3 = () => {
  const testData = '06:00-09:00,12:00-14:00,18:00-22:00'
  timeRangeValue.value = testData
  timeSlotValue.value = testData
}

// 功能对比数据
const featureComparison = [
  {
    feature: '交互方式',
    timeRange: { text: '手动输入 + 点击', type: 'info' },
    timeSlot: { text: '拖拽选择', type: 'success' },
    advantage: { text: 'TimeSlot更直观', class: 'text-green-600' }
  },
  {
    feature: '重叠处理',
    timeRange: { text: '检测阻止', type: 'warning' },
    timeSlot: { text: '智能合并', type: 'success' },
    advantage: { text: 'TimeSlot更智能', class: 'text-green-600' }
  },
  {
    feature: '数据格式',
    timeRange: { text: '字符串', type: 'info' },
    timeSlot: { text: '数组(适配器转换)', type: 'success' },
    advantage: { text: '兼容性良好', class: 'text-blue-600' }
  },
  {
    feature: '统计信息',
    timeRange: { text: '有总时长覆盖率', type: 'success' },
    timeSlot: { text: '有总时长覆盖率', type: 'success' },
    advantage: { text: '功能相同', class: 'text-gray-600' }
  },
  {
    feature: '预设选项',
    timeRange: { text: '4种电价预设', type: 'success' },
    timeSlot: { text: '4种电价预设', type: 'success' },
    advantage: { text: '功能相同', class: 'text-gray-600' }
  },
  {
    feature: 'UI设计',
    timeRange: { text: '传统界面', type: 'info' },
    timeSlot: { text: '现代化设计', type: 'success' },
    advantage: { text: 'TimeSlot更美观', class: 'text-green-600' }
  }
]
</script>

<style scoped>
.time-slot-replacement-test {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.comparison-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

.component-demo {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  background: #fafbfc;
}

.component-demo h3 {
  margin: 0 0 8px 0;
  color: #303133;
}

.description {
  font-size: 14px;
  color: #606266;
  margin-bottom: 16px;
  line-height: 1.5;
}

.data-display {
  margin-top: 16px;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.data-display code {
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  color: #e6a23c;
}

.sync-test-section {
  margin-bottom: 32px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.sync-controls {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 24px;
}

.test-buttons h4 {
  margin-bottom: 12px;
  color: #303133;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.feature-comparison {
  margin-top: 32px;
}

.feature-comparison h3 {
  margin-bottom: 16px;
  color: #303133;
}

.text-green-600 {
  color: #16a085;
  font-weight: 500;
}

.text-blue-600 {
  color: #3498db;
  font-weight: 500;
}

.text-gray-600 {
  color: #7f8c8d;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .comparison-section {
    grid-template-columns: 1fr;
  }
  
  .sync-controls {
    flex-direction: column;
    align-items: center;
  }
}
</style>
