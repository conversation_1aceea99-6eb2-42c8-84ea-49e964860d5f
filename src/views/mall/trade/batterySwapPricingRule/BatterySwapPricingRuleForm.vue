<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="1200px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="规则名称" prop="name">
            <el-input v-model="formData.name" placeholder="请输入规则名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="优先级" prop="priority">
            <el-input-number
              v-model="formData.priority"
              :min="0"
              :max="999"
              placeholder="数字越大优先级越高"
              class="!w-full"
            />
            <div class="text-xs text-gray-500 mt-1">
              规则优先级，数字越大优先级越高
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio
                v-for="dict in BATTERY_SWAP_PRICING_RULE_STATUS_OPTIONS"
                :key="dict.value"
                :label="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电价(分/度)" prop="price">
            <el-input-number
              v-model="formData.price"
              :min="1"
              :max="99999"
              placeholder="单位：分/度"
              class="!w-full"
            />
            <div class="text-xs text-gray-500 mt-1">
              当前设置：{{ (formData.price / 100).toFixed(2) }} 元/度
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="规则描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="2"
          placeholder="请输入规则描述"
        />
      </el-form-item>

      <el-form-item label="价格说明" prop="priceDescription">
        <el-input v-model="formData.priceDescription" placeholder="如：峰时电价 1.8元/度" />
      </el-form-item>

      <!-- 适用范围配置 -->
      <el-divider content-position="left">适用范围配置</el-divider>

      <el-form-item label="适用换电站" prop="stationCodes">
        <div class="station-selector">
          <el-select
            v-model="selectedStationIds"
            multiple
            filterable
            placeholder="请选择适用换电站，留空表示全部站点"
            style="width: 100%"
            @change="handleStationChange"
            clearable
            collapse-tags
            collapse-tags-tooltip
            :max-collapse-tags="3"
          >
            <el-option
              v-for="station in stationOptions"
              :key="station.siteId"
              :label="`${station.siteName} (${station.siteNo})`"
              :value="station.siteId"
            >
              <div class="station-option">
                <div class="station-main">
                  <span class="station-name">{{ station.siteName }}</span>
                  <span class="station-no">{{ station.siteNo }}</span>
                </div>
                <div class="station-info">
                  <span class="station-address">{{ station.siteAdress }}</span>
                  <el-tag
                    :type="station.siteStatus === 1 ? 'success' : 'danger'"
                    size="small"
                  >
                    {{ station.siteStatus === 1 ? '营业中' : '暂停使用' }}
                  </el-tag>
                </div>
              </div>
            </el-option>
          </el-select>

          <!-- 已选择站点显示 -->
          <div v-if="selectedStations.length > 0" class="selected-stations mt-3">
            <div class="selected-header">
              <span class="text-sm text-gray-600">已选择 {{ selectedStations.length }} 个站点：</span>
              <el-button size="small" type="text" @click="clearAllStations">
                <el-icon><Delete /></el-icon>
                清空
              </el-button>
            </div>
            <div class="stations-grid">
              <div
                v-for="station in selectedStations"
                :key="station.siteId"
                class="station-card"
              >
                <div class="station-card-content">
                  <div class="station-card-header">
                    <span class="station-card-name">{{ station.siteName }}</span>
                    <el-button
                      size="small"
                      type="text"
                      class="remove-station-btn"
                      @click="removeStation(station.siteId)"
                    >
                      <el-icon><Close /></el-icon>
                    </el-button>
                  </div>
                  <div class="station-card-info">
                    <span class="station-card-no">{{ station.siteNo }}</span>
                    <el-tag
                      :type="station.siteStatus === 1 ? 'success' : 'danger'"
                      size="small"
                    >
                      {{ station.siteStatus === 1 ? '营业中' : '暂停使用' }}
                    </el-tag>
                  </div>
                  <div class="station-card-address">{{ station.siteAdress }}</div>
                </div>
              </div>
            </div>
          </div>

          <div class="text-xs text-gray-500 mt-2">
            <div class="flex items-center gap-2 mb-1">
              <el-icon class="text-blue-500"><InfoFilled /></el-icon>
              <span class="font-medium">留空表示适用于全部站点</span>
            </div>
            <div class="ml-5">
              选择特定站点则仅在这些站点生效，支持多选和搜索
            </div>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="日期类型" prop="dateType">
        <el-radio-group v-model="formData.dateType" @change="onDateTypeChange">
          <el-radio
            v-for="dict in BATTERY_SWAP_PRICING_RULE_DATE_TYPE_OPTIONS"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 指定日期配置 -->
      <el-form-item
        v-if="formData.dateType === 4"
        label="指定日期"
        prop="specificDates"
      >
        <el-input
          v-model="formData.specificDates"
          type="textarea"
          :rows="2"
          placeholder="格式：yyyy-MM-dd，多个日期用逗号分隔"
        />
        <div class="text-xs text-gray-500 mt-1">
          示例：2024-01-01,2024-01-02,2024-01-03
        </div>
      </el-form-item>

      <!-- 节假日类型配置 -->
      <el-form-item
        v-if="formData.dateType === 5"
        label="节假日类型"
        prop="holidayTypes"
      >
        <el-input
          v-model="formData.holidayTypes"
          type="textarea"
          :rows="2"
          placeholder="节假日类型，多个用逗号分隔"
        />
        <div class="text-xs text-gray-500 mt-1">
          示例：NATIONAL_DAY,NEW_YEAR,SPRING_FESTIVAL
        </div>
      </el-form-item>

      <el-form-item label="适用时间段" prop="timeRanges">
        <TimeSlotSelectorAdapter v-model="formData.timeRanges" />
        <div class="text-xs text-gray-500 mt-1">
          <div class="flex items-center gap-2 mb-1">
            <el-icon class="text-blue-500"><InfoFilled /></el-icon>
            <span class="font-medium">留空表示全天有效（24小时）</span>
          </div>
          <div class="ml-5">
            选择特定时间段则仅在这些时间段内生效，支持拖拽选择多个时间段
          </div>
        </div>
      </el-form-item>

      <!-- 有效期配置 -->
      <el-divider content-position="left">有效期配置</el-divider>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="生效开始时间" prop="validFrom">
            <el-date-picker
              v-model="formData.validFrom"
              type="datetime"
              placeholder="选择开始时间"
              format="YYYY-MM-DD HH:mm:ss"
              class="!w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="生效结束时间" prop="validTo">
            <el-date-picker
              v-model="formData.validTo"
              type="datetime"
              placeholder="选择结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              class="!w-full"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <div class="text-xs text-gray-500">
        留空表示长期有效。如果设置了有效期，规则只在指定时间范围内生效。
      </div>
    </el-form>

    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import {
  getBatterySwapPricingRule,
  createBatterySwapPricingRule,
  updateBatterySwapPricingRule,
  validateBatterySwapPricingRule,
  BATTERY_SWAP_PRICING_RULE_STATUS_OPTIONS,
  BATTERY_SWAP_PRICING_RULE_DATE_TYPE_OPTIONS,
  type BatterySwapPricingRuleVO
} from '@/api/mall/trade/batterySwapPricingRule'
import { BusSiteApi, type BusSiteVO } from '@/api/site/bussite'
import TimeSlotSelectorAdapter from './components/TimeSlotSelectorAdapter.vue'
import { Delete, Close, InfoFilled } from '@element-plus/icons-vue'

/** 换电动态定价规则 表单 */
defineOptions({ name: 'BatterySwapPricingRuleForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  name: '',
  description: '',
  priority: 50,
  status: 0,
  stationCodes: '',
  dateType: 1,
  specificDates: '',
  holidayTypes: '',
  timeRanges: '',
  price: 150,
  priceDescription: '',
  validFrom: undefined,
  validTo: undefined
})
const formRules = reactive({
  name: [{ required: true, message: '规则名称不能为空', trigger: 'blur' }],
  priority: [{ required: true, message: '优先级不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'change' }],
  dateType: [{ required: true, message: '日期类型不能为空', trigger: 'change' }],
  price: [{ required: true, message: '电价不能为空', trigger: 'blur' }],
  specificDates: [
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (formData.value.dateType === 4 && (!value || value.trim() === '')) {
          callback(new Error('指定日期不能为空'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  holidayTypes: [
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (formData.value.dateType === 5 && (!value || value.trim() === '')) {
          callback(new Error('节假日类型不能为空'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
})
const formRef = ref() // 表单 Ref

// 站点选择相关
const selectedStationIds = ref<number[]>([]) // 选中的站点ID数组
const stationOptions = ref<BusSiteVO[]>([]) // 站点选项数据

// 计算属性：已选择的站点详情
const selectedStations = computed(() => {
  return stationOptions.value.filter(station =>
    selectedStationIds.value.includes(station.siteId)
  )
})

/** 处理站点选择变化 */
const handleStationChange = (stationIds: number[]) => {
  selectedStationIds.value = stationIds
  // 将站点ID数组转换为逗号分隔的字符串存储到表单数据中
  formData.value.stationCodes = stationIds.length > 0 ? stationIds.join(',') : ''
}

/** 移除单个站点 */
const removeStation = (stationId: number) => {
  const index = selectedStationIds.value.indexOf(stationId)
  if (index > -1) {
    selectedStationIds.value.splice(index, 1)
    handleStationChange(selectedStationIds.value)
  }
}

/** 清空所有站点 */
const clearAllStations = () => {
  selectedStationIds.value = []
  handleStationChange([])
}

/** 将逗号分隔的站点ID字符串转换为数组 */
const parseStationIds = (stationStr: string): number[] => {
  if (!stationStr || stationStr.trim() === '') {
    return []
  }
  return stationStr.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id))
}

/** 加载站点数据 */
const loadStationOptions = async () => {
  try {
    const response = await BusSiteApi.getSimpleBusSiteList()
    // 处理响应数据，确保是数组格式
    if (Array.isArray(response)) {
      stationOptions.value = response
    } else if (response && Array.isArray(response.data)) {
      stationOptions.value = response.data
    } else {
      stationOptions.value = []
    }
  } catch (error) {
    console.error('加载站点数据失败:', error)
    stationOptions.value = []
    message.error('加载站点数据失败')
  }
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = type === 'create' ? '添加换电动态定价规则' : '修改换电动态定价规则'
  formType.value = type
  resetForm()

  // 加载站点数据
  await loadStationOptions()

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const ruleData = await getBatterySwapPricingRule(id)
      formData.value = {
        ...ruleData,
        // 转换日期字符串为Date对象
        validFrom: ruleData.validFrom ? new Date(ruleData.validFrom) : undefined,
        validTo: ruleData.validTo ? new Date(ruleData.validTo) : undefined
      }
      // 解析已选择的站点ID
      selectedStationIds.value = parseStationIds(formData.value.stationCodes || '')
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  
  // 验证规则配置
const isValid = await validateBatterySwapPricingRule(formData.value)
  if (!isValid) {
    message.error('规则配置验证失败，请检查配置项')
    return
  }
  
  // 提交请求
  formLoading.value = true
  try {
    // 只提取后端需要的字段，避免发送显示字段
    const data = {
      id: formData.value.id,
      name: formData.value.name,
      description: formData.value.description,
      priority: formData.value.priority,
      status: formData.value.status,
      stationCodes: formData.value.stationCodes,
      dateType: formData.value.dateType,
      specificDates: formData.value.specificDates,
      holidayTypes: formData.value.holidayTypes,
      timeRanges: formData.value.timeRanges,
      price: formData.value.price,
      priceDescription: formData.value.priceDescription,
      // 格式化日期字段，确保正确传递给后端
      validFrom: formatDateForSubmit(formData.value.validFrom),
      validTo: formatDateForSubmit(formData.value.validTo)
    }

    console.log('[submitForm] 提交数据:', {
      validFrom: data.validFrom,
      validTo: data.validTo,
      validFromDate: data.validFrom ? new Date(data.validFrom) : null,
      validToDate: data.validTo ? new Date(data.validTo) : null,
      originalValidFrom: formData.value.validFrom,
      originalValidTo: formData.value.validTo,
      fullData: data
    })

    if (formType.value === 'create') {
      await createBatterySwapPricingRule(data)
      message.success(t('common.createSuccess'))
    } else {
      await updateBatterySwapPricingRule(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: '',
    description: '',
    priority: 50,
    status: 0,
    stationCodes: '',
    dateType: 1,
    specificDates: '',
    holidayTypes: '',
    timeRanges: '',
    price: 150,
    priceDescription: '',
    validFrom: undefined,
    validTo: undefined
  }
  selectedStationIds.value = []
  formRef.value?.resetFields()
}

/** 格式化日期用于提交 */
const formatDateForSubmit = (date: Date | string | undefined | null): number | undefined => {
  if (!date) {
    return undefined
  }

  // 如果已经是字符串，尝试解析为Date对象
  if (typeof date === 'string') {
    const parsedDate = new Date(date)
    if (isNaN(parsedDate.getTime())) {
      console.warn('[formatDateForSubmit] 无效的日期字符串:', date)
      return undefined
    }
    // 返回时间戳（毫秒）
    return parsedDate.getTime()
  }

  // 如果是Date对象，返回时间戳（毫秒）
  if (date instanceof Date) {
    if (isNaN(date.getTime())) {
      console.warn('[formatDateForSubmit] 无效的Date对象:', date)
      return undefined
    }
    return date.getTime()
  }

  return undefined
}

/** 日期类型变化处理 */
const onDateTypeChange = () => {
  // 清空相关字段
  formData.value.specificDates = ''
  formData.value.holidayTypes = ''
}
</script>

<style scoped>
/* 站点选择器样式 */
.station-selector {
  width: 100%;
}

.station-option {
  padding: 8px 0;
}

.station-main {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.station-name {
  font-weight: 500;
  color: #303133;
}

.station-no {
  font-size: 12px;
  color: #909399;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
}

.station-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.station-address {
  font-size: 12px;
  color: #666;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 已选择站点显示 */
.selected-stations {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  background: #fafbfc;
}

.selected-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.stations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 12px;
}

.station-card {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  transition: all 0.3s ease;
}

.station-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.station-card-content {
  width: 100%;
}

.station-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.station-card-name {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

.remove-station-btn {
  color: #f56c6c;
  padding: 2px;
}

.remove-station-btn:hover {
  background-color: #fef0f0;
}

.station-card-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.station-card-no {
  font-size: 12px;
  color: #909399;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
}

.station-card-address {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stations-grid {
    grid-template-columns: 1fr;
  }

  .station-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>
