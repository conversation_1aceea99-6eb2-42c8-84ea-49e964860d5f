<template>
  <div ref="formContainer">
    <div class="search">
      <el-card>
        <el-descriptions :column="6" border>
          <el-descriptions-item label="订单号" :span="2">
            {{formData?.orderId}}
          </el-descriptions-item>
          <el-descriptions-item label="车牌号">
            {{formData?.carNo}}
          </el-descriptions-item>
<!--          <el-descriptions-item label="车辆类型">-->
<!--            <span v-if="formData?.carType == 1">自有</span>-->
<!--            <span v-if="formData?.carType == 2">租赁</span>-->
<!--          </el-descriptions-item>-->
          <el-descriptions-item label="订单状态">
            <el-tag v-if="formData?.orderStatus == 1" type="success">已支付</el-tag>
            <el-tag v-if="formData?.orderStatus == 0 && formData?.cstaus == 1" type="warning">未支付</el-tag>
            <el-tag v-if="formData?.orderStatus == 0 && formData?.cstaus == 0" type="danger">异常</el-tag>
          </el-descriptions-item>
<!--          <el-descriptions-item label="订单标记">-->
<!--            <span v-if="formData?.orderMark == 0">实时</span>-->
<!--            <span v-if="formData?.orderMark == 1">延时</span>-->
<!--          </el-descriptions-item>-->
          <el-descriptions-item label="当前绑定用户" :span="2">
            {{formData?.nickname}}
          </el-descriptions-item>
          <el-descriptions-item label="用户手机号">
            {{formData?.mobile}}
          </el-descriptions-item>
          <el-descriptions-item label="车型">
            {{formData?.carTypeText}}
          </el-descriptions-item>
          <el-descriptions-item label="最大电池包数" :span="2">
            {{formData?.maxBatteries}}
          </el-descriptions-item>
          <el-descriptions-item label="站点号" :span="2">
            {{formData?.siteNo}}
          </el-descriptions-item>
          <el-descriptions-item label="站点名称">
            {{formData?.siteName}}
          </el-descriptions-item>
          <el-descriptions-item label="进站时间">
            {{ formData?.getInTime ? formatDate(formData.getInTime) : '' }}
          </el-descriptions-item>
          <el-descriptions-item label="离站时间" :span="2">
            {{ formData?.outInTime ? formatDate(formData.outInTime) : '' }}
          </el-descriptions-item>
<!--          <el-descriptions-item label="收费标准" :span="2">-->
<!--            {{formData?.chargeType}}-->
<!--          </el-descriptions-item>-->
          <el-descriptions-item label="换电电量（度）">
            {{formData?.chargeCtn}}
          </el-descriptions-item>
          <el-descriptions-item label="换电电价(元/度)">
            {{formData?.price}}
          </el-descriptions-item>
          <el-descriptions-item label="订单金额（元）" :span="2">
            {{formData?.paySum}}
          </el-descriptions-item>
<!--          <el-descriptions-item label="支付方式" :span="2">-->
<!--            <span v-if="formData?.payType == 2">余额</span>-->
<!--            <span v-if="formData?.payType == 3">电量</span>-->
<!--            <span v-if="formData?.payType == 4">权益</span>-->
<!--          </el-descriptions-item>-->
<!--          <el-descriptions-item label="实付">-->
<!--            <span v-if="formData?.orderStatus == 0">{{ formData?.realPaySum }}</span>-->
<!--            <span v-if="formData?.orderStatus == 1 && formData?.payType == 2">-->
<!--              总额:{{ formData?.paySum }}元,本金:{{ formData?.realPaySum }}元,赠金:{{ formData?.paySum - formData?.realPaySum }}元-->
<!--            </span>-->
<!--            <span v-if="formData?.orderStatus == 1 && formData?.payType == 3">{{ formData?.realPaySum }}度电</span>-->
<!--            <span v-if="formData?.orderStatus == 1 && formData?.payType == 4">一次权益</span>-->
<!--          </el-descriptions-item>-->
          <el-descriptions-item label="实付">
            {{formData?.realPaySum}}
          </el-descriptions-item>
          <el-descriptions-item label="支付时间" :span="1">
            {{ formData?.payTime ? formatDate(formData.payTime) : '' }}
          </el-descriptions-item>
          <el-descriptions-item label="生成时间" :span="6">
            {{ formData?.createTime ? formatDate(formData.createTime) : '' }}
          </el-descriptions-item>
<!--          <el-descriptions-item label="推送订单id" :span="6">-->
<!--            {{formData?.id}}-->
<!--          </el-descriptions-item>-->
          <el-descriptions-item label="电池包信息" :span="6">
            <div class="battery-tables">
              <div class="battery-table">
                <div class="table-title">换电前电池包信息</div>
                <el-table 
                  :data="beforeBatteriesList" 
                  :pagination="false" 
                  size="small" 
                  style="width: 100%;"
                  border
                >
                  <el-table-column prop="ctn" label="序号" width="60" align="center" />
                  <el-table-column prop="no" label="电池包条码" width="200" align="center" />
                  <el-table-column prop="soc" label="SOC" width="80" align="center" />
                  <el-table-column prop="volume" label="标称容量" width="80" align="center" />
                </el-table>
              </div>
              <div class="battery-table">
                <div class="table-title">换电后电池包信息</div>
                <el-table 
                  :data="afterBatteriesList" 
                  :pagination="false" 
                  size="small" 
                  style="width: 100%;"
                  border
                >
                  <el-table-column prop="ctn" label="序号" width="60" align="center" />
                  <el-table-column prop="no" label="电池包条码" width="200" align="center" />
                  <el-table-column prop="soc" label="SOC" width="80" align="center" />
                  <el-table-column prop="volume" label="标称容量" width="80" align="center" />
                </el-table>
              </div>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BocForm',
  props: {
    formData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      beforeBatteriesList: [],
      afterBatteriesList: []
    }
  },
  watch: {
    formData: {
      handler(newVal) {
        if (newVal) {
          if (newVal.beforeBatteriesList) {
            this.beforeBatteriesList = this.processBatteryList(newVal.beforeBatteriesList)
          }
          if (newVal.afterBatteriesList) {
            this.afterBatteriesList = this.processBatteryList(newVal.afterBatteriesList)
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    processBatteryList(listStr) {
      if (!listStr) return []
      return listStr.split(';').filter(item => item).map(item => {
        const parts = item.split(',')
        return {
          ctn: parts[0],
          no: parts[1],
          soc: parts[2],
          volume: parts.length >= 4 ? (parseFloat(parts[3]) / 100).toFixed(2) : ''
        }
      })
    },
    formatDate(date) {
      if (!date) return ''
      const d = new Date(date)
      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')
      const hours = String(d.getHours()).padStart(2, '0')
      const minutes = String(d.getMinutes()).padStart(2, '0')
      const seconds = String(d.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    }
  }
}
</script>

<style scoped lang="less" rel="stylesheet/less">
.el-descriptions {
  margin-left: 5px;
}

.battery-tables {
  display: flex;
  gap: 20px;
}

.battery-table {
  flex: 1;
}

.table-title {
  text-align: center;
  margin-bottom: 10px;
  font-weight: bold;
}
</style>
