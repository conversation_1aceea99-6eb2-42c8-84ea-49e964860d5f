<template>
  <div class="search">
    <el-drawer
      v-model="drawerVisible"
      title="订单详情"
      :size="'90%'"
      @close="handleClose"
    >
      <div class="drawer-content">
        <boc-form :form-data="currentData" />
      </div>
    </el-drawer>
  </div>
</template>

<script>
import BocForm from './BocForm.vue'
import { ElDrawer } from 'element-plus'

export default {
  name: 'BocDrawer',
  components: {
    BocForm,
    ElDrawer
  },
  props: {},
  data() {
    return {
      drawerVisible: false,
      currentData: null
    }
  },
  methods: {
    showDrawer(data) {
      console.log('<PERSON><PERSON><PERSON><PERSON><PERSON> - showDrawer called with data:', data)
      this.currentData = data
      this.drawerVisible = true
    },
    handleClose() {
      this.drawerVisible = false
      this.currentData = null
    }
  },
  computed: {
    drawerTitle: function() {
      return '订单详情'
    }
  }
}
</script>

<style scoped lang="less" rel="stylesheet/less">
.search {
  position: relative;
  z-index: 9999;
  height: 100%;
}
.drawer-content {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}
</style> 
