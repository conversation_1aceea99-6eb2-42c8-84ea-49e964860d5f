<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="支付时间" prop="payTime">
        <el-date-picker
          v-model="queryParams.payTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="userId">
        <el-select
          v-model="queryParams.userId"
          filterable
          remote
          clearable
          :remote-method="fetchMobileOptions"
          :loading="userLoading"
          placeholder="请输入手机号"
          class="!w-220px"
          @change="onUserIdChange"
        >
          <el-option
            v-for="user in userOptions"
            :key="user.id"
            :label="user.mobile"
            :value="user.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="站点名称" prop="siteNo">
        <el-select
          v-model="queryParams.siteNo"
          filterable
          remote
          clearable
          :remote-method="fetchSiteOptions"
          :loading="siteLoading"
          placeholder="请输入站点名称或站点号"
          class="!w-220px"
        >
          <el-option
            v-for="site in siteOptions"
            :key="site.siteNo"
            :label="site.siteName"
            :value="site.siteNo"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="用户" prop="userId">
        <el-select
          v-model="queryParams.userId"
          filterable
          remote
          clearable
          :remote-method="fetchNicknameOptions"
          :loading="userLoading"
          placeholder="请输入用户昵称"
          class="!w-220px"
          @change="onUserIdChange"
        >
          <el-option
            v-for="user in userOptions"
            :key="user.id"
            :label="user.nickname"
            :value="user.id"
          />
        </el-select>
      </el-form-item>
<!--      <el-form-item label="车辆类型" prop="carType">-->
<!--        <el-select-->
<!--          v-model="queryParams.carType"-->
<!--          placeholder="请选择车辆类型"-->
<!--          clearable-->
<!--          class="!w-220px"-->
<!--        >-->
<!--          <el-option-->
<!--            v-for="dict in getIntDictOptions(DICT_TYPE.CAR_TYPE)"-->
<!--            :key="dict.value"-->
<!--            :label="dict.label"-->
<!--            :value="dict.value"-->
<!--          />-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <el-form-item label="订单状态" prop="orderStatus">
        <el-select
          v-model="queryParams.orderStatus"
          placeholder="请选择订单状态"
          clearable
          class="!w-220px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ORDER_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
<!--      <el-form-item label="开票状态" prop="invoiced">-->
<!--        <el-select-->
<!--          v-model="queryParams.invoiced"-->
<!--          placeholder="请选择开票状态"-->
<!--          clearable-->
<!--          class="!w-220px"-->
<!--        >-->
<!--          <el-option-->
<!--            v-for="dict in getIntDictOptions(DICT_TYPE.INVOICE_STATUS)"-->
<!--            :key="dict.value"-->
<!--            :label="dict.label"-->
<!--            :value="dict.value"-->
<!--          />-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['mall:order:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['mall:order:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true" border resizable>
      <el-table-column label="订单id" align="center" prop="orderId" width="190px" fixed="left" />
      <el-table-column label="车牌" align="center" prop="carNo" width="120px" fixed="left"/>
<!--      <el-table-column label="车辆类型" align="center" prop="carType">-->
<!--        <template #default="scope">-->
<!--          <dict-tag :type="DICT_TYPE.CAR_TYPE" :value="scope.row.carType" />-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="最多电池包数" align="center" prop="maxBatteries" />
      <el-table-column label="手机号" align="center" prop="mobile" />
      <el-table-column label="用户" align="center" prop="nickname" />
      <el-table-column label="站点号" align="center" prop="siteNo" />
      <el-table-column label="站点名称" align="center" prop="siteName" />
      <el-table-column
        label="进站时间"
        align="center"
        prop="getInTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column
        label="出站时间"
        align="center"
        prop="outInTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column
        label="支付时间"
        align="center"
        prop="payTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column
        label="生成时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
<!--      <el-table-column label="收费标准" align="center" prop="chargeType">-->
<!--        <template #default="scope">-->
<!--          <dict-tag :type="DICT_TYPE.CAR_CHARGE" :value="scope.row.chargeType" />-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="收费标准" align="center" prop="chargeType" />-->
      <el-table-column label="换电量（度）" align="center" prop="chargeCtn" />
      <el-table-column label="换电电价" align="center" prop="price" />
      <el-table-column label="订单状态" align="center" prop="orderStatus" width="100px">
        <template #default="scope">
          <el-tag v-if="scope.row.orderStatus == 1" type="success">已支付</el-tag>
          <el-tag v-if="scope.row.orderStatus == 0 && scope.row.cstaus == 1" type="warning">未支付</el-tag>
          <el-tag v-if="scope.row.orderStatus == 0 && scope.row.cstaus == 0" type="danger">异常</el-tag>
        </template>
      </el-table-column>
<!--      <el-table-column label="订单标记" align="center" prop="orderMark">-->
<!--        <template #default="scope">-->
<!--          <span v-if="scope.row.orderMark === '0'">实时</span>-->
<!--          <span v-if="scope.row.orderMark === '1'">延时</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="支付方式" align="center" prop="payType">-->
<!--        <template #default="scope">-->
<!--          <dict-tag :type="DICT_TYPE.PAY_TYPE" :value="scope.row.payType" />-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="实付" align="center" prop="realPaySum" width="200px">-->
<!--        <template #default="scope">-->
<!--          <span v-if="scope.row.orderStatus == 0">{{ scope.row.realPaySum }}</span>-->
<!--          <span v-if="scope.row.orderStatus == 1 && scope.row.payType == 2">-->
<!--            总额:{{ scope.row.paySum }}元,本金:{{ scope.row.realPaySum }}元,赠金:{{ scope.row.paySum - scope.row.realPaySum }}元-->
<!--          </span>-->
<!--          <span v-if="scope.row.orderStatus == 1 && scope.row.payType == 3">{{ scope.row.realPaySum }}度电</span>-->
<!--          <span v-if="scope.row.orderStatus == 1 && scope.row.payType == 4">一次权益</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="实付" align="center" prop="realPaySum" width="200px"/>
<!--      <el-table-column label="开票状态" align="center" prop="invoiced" width="100px">-->
<!--        <template #default="scope">-->
<!--          <dict-tag :type="DICT_TYPE.INVOICE_STATUS" :value="scope.row.invoiced" />-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="推送订单id" align="center" prop="id" width="300px" />-->
      <el-table-column label="操作" align="center" min-width="120px" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="showDetail(scope.row)"
            v-hasPermi="['trade:bus-order:query']"
          >
            详情
          </el-button>
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['mall:busorder:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['mall:busorder:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <OrderForm ref="formRef" @success="getList" />

  <!-- 详情抽屉 -->
  <BocDrawer ref="bocDrawerRef" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { OrderApi, OrderVO } from '@/api/mall/busorder'
import OrderForm from './OrderForm.vue'
import BocDrawer from './BocDrawer.vue'
import { getUserPage } from '@/api/member/user'
import type { UserVO } from '@/api/member/user'
import { BusSiteApi, BusSiteVO } from '@/api/site/bussite'

/** 订单 列表 */
defineOptions({ name: 'Order' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<OrderVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  payTime: [],
  createTime: [],
  userId: undefined as number | undefined,
  carType: undefined,
  orderStatus: undefined,
  invoiced: undefined,
  siteNo: undefined // 修改为siteNo
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const userOptions = ref<UserVO[]>([])
const userLoading = ref(false)
const siteOptions = ref<BusSiteVO[]>([])
const siteLoading = ref(false)

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await OrderApi.getOrderPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await OrderApi.deleteOrder(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await OrderApi.exportOrder(queryParams)
    download.excel(data, '订单.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 详情按钮操作 */
const bocDrawerRef = ref()
const showDetail = (row: OrderVO) => {
  console.log('showDetail called', row)
  console.log('bocDrawerRef', bocDrawerRef.value)
  bocDrawerRef.value?.showDrawer(row)
}

/** 远程搜索手机号选项 */
const fetchMobileOptions = async (query: string) => {
  if (!query) {
    userOptions.value = []
    return
  }
  userLoading.value = true
  try {
    const params = { mobile: query, pageNo: 1, pageSize: 20 }
    const res = await getUserPage(params)
    userOptions.value = (res.list || []) as UserVO[]
  } finally {
    userLoading.value = false
  }
}

/** 远程搜索昵称选项 */
const fetchNicknameOptions = async (query: string) => {
  if (!query) {
    userOptions.value = []
    return
  }
  userLoading.value = true
  try {
    const params = { nickname: query, pageNo: 1, pageSize: 20 }
    const res = await getUserPage(params)
    userOptions.value = (res.list || []) as UserVO[]
  } finally {
    userLoading.value = false
  }
}

/** 远程搜索站点选项 */
const fetchSiteOptions = async (query: string) => {
  if (!query) {
    siteOptions.value = []
    return
  }
  siteLoading.value = true
  try {
    const params = { pageNo: 1, pageSize: 20, siteName: query }
    const res = await BusSiteApi.getBusSitePage(params)
    siteOptions.value = res.list || []
  } finally {
    siteLoading.value = false
  }
}

const onUserIdChange = (userId: number) => {
  const user = userOptions.value.find(u => u.id === userId)
  if (user) {
    // 仅用于展示
    queryParams.mobile = user.mobile
  } else {
    queryParams.mobile = undefined
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
