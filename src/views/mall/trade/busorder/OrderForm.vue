<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="用户id" prop="userId">
        <el-input v-model="formData.userId" placeholder="请输入用户id" />
      </el-form-item>
      <el-form-item label="价格" prop="price">
        <el-input v-model="formData.price" placeholder="请输入价格" />
      </el-form-item>
      <el-form-item label="订单状态" prop="orderStatus">
        <el-select v-model="formData.orderStatus" placeholder="请选择订单状态">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="支付类型" prop="payType">
        <el-select v-model="formData.payType" placeholder="请选择支付类型">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="支付成功时间" prop="payTime">
        <el-date-picker
          v-model="formData.payTime"
          type="date"
          value-format="x"
          placeholder="选择支付成功时间"
        />
      </el-form-item>
      <el-form-item label="订单金额" prop="paySum">
        <el-input v-model="formData.paySum" placeholder="请输入订单金额" />
      </el-form-item>
      <el-form-item label="实际支付金额" prop="realPaySum">
        <el-input v-model="formData.realPaySum" placeholder="请输入实际支付金额" />
      </el-form-item>
      <el-form-item label="订单类型" prop="orderType">
        <el-select v-model="formData.orderType" placeholder="请选择订单类型">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="第三订单号" prop="orderNo">
        <el-input v-model="formData.orderNo" placeholder="请输入第三订单号" />
      </el-form-item>
      <el-form-item label="车辆分组" prop="carGroupId">
        <el-input v-model="formData.carGroupId" placeholder="请输入车辆分组" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { OrderApi, OrderVO } from '@/api/mall/busorder'

/** 订单 表单 */
defineOptions({ name: 'OrderForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  orderId: undefined,
  userId: undefined,
  price: undefined,
  orderStatus: undefined,
  payType: undefined,
  payTime: undefined,
  paySum: undefined,
  realPaySum: undefined,
  orderType: undefined,
  orderNo: undefined,
  carGroupId: undefined
})
const formRules = reactive({
  userId: [{ required: true, message: '用户id不能为空', trigger: 'blur' }],
  price: [{ required: true, message: '价格不能为空', trigger: 'blur' }],
  orderStatus: [{ required: true, message: '订单状态不能为空', trigger: 'change' }],
  payType: [{ required: true, message: '支付类型不能为空', trigger: 'change' }],
  paySum: [{ required: true, message: '订单金额不能为空', trigger: 'blur' }],
  realPaySum: [{ required: true, message: '实际支付金额不能为空', trigger: 'blur' }],
  orderType: [{ required: true, message: '订单类型不能为空', trigger: 'change' }],
  orderNo: [{ required: true, message: '第三订单号不能为空', trigger: 'blur' }],
  carGroupId: [{ required: true, message: '车辆分组不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await OrderApi.getOrder(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as OrderVO
    if (formType.value === 'create') {
      await OrderApi.createOrder(data)
      message.success(t('common.createSuccess'))
    } else {
      await OrderApi.updateOrder(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    orderId: undefined,
    userId: undefined,
    price: undefined,
    orderStatus: undefined,
    payType: undefined,
    payTime: undefined,
    paySum: undefined,
    realPaySum: undefined,
    orderType: undefined,
    orderNo: undefined,
    carGroupId: undefined
  }
  formRef.value?.resetFields()
}
</script>
