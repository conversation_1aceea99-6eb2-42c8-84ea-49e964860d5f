<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="用户编号" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入用户编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="车辆编号" prop="vehicleNo">
        <el-input
          v-model="queryParams.vehicleNo"
          placeholder="请输入车辆编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="套餐名称" prop="packageName">
        <el-input
          v-model="queryParams.packageName"
          placeholder="请输入套餐名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable class="!w-240px">
          <el-option label="生效中" value="1" />
          <el-option label="已过期" value="2" />
          <el-option label="待生效" value="3" />
          <el-option label="已取消" value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="编号" align="center" prop="id" />
      <el-table-column label="用户编号" align="center" prop="userId" />
      <el-table-column label="车辆编号" align="center" prop="vehicleNo" />
      <el-table-column label="套餐名称" align="center" prop="packageName" />
      <el-table-column label="套餐周期" align="center" width="100">
        <template #default="scope">
          {{ scope.row.packagePeriod }} 天
        </template>
      </el-table-column>
      <el-table-column label="套餐额度" align="center" prop="packageQuota" width="100" />
      <el-table-column label="套餐价格" align="center" width="100">
        <template #default="scope">
          ¥{{ (scope.row.packagePrice / 100).toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column label="有效期" align="center" width="200">
        <template #default="scope">
          <div>{{ formatDate(scope.row.startTime) }}</div>
          <div>至 {{ formatDate(scope.row.endTime) }}</div>
        </template>
      </el-table-column>
      <el-table-column label="剩余额度" align="center" prop="remainingQuota" width="100" />
      <el-table-column label="状态" align="center" width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)" size="small">
            {{ scope.row.statusName }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import * as MemberVehicleApi from '@/api/member/vehicle'
import { MemberVehiclePackageRecordVO } from '@/api/member/vehicle'

/** 车辆套餐记录 列表 */
defineOptions({ name: 'MemberVehiclePackageRecord' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<MemberVehiclePackageRecordVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  userId: undefined,
  vehicleNo: undefined,
  packageName: undefined,
  status: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await MemberVehicleApi.getVehiclePackageRecordPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 获取状态类型 */
const getStatusType = (status: number) => {
  switch (status) {
    case 1: return 'success' // 生效中
    case 2: return 'info'    // 已过期
    case 3: return 'warning' // 待生效
    case 4: return 'danger'  // 已取消
    default: return 'info'
  }
}

/** 格式化日期 */
const formatDate = (date: Date | string) => {
  if (!date) return '-'
  return dateFormatter(date)
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
