<template>
  <div class="app-container">
    <div class="page-header">
      <h2>我的车辆管理</h2>
      <p>管理您的车辆信息和套餐配置</p>
    </div>

    <el-button type="primary" @click="openBindVehicleDialog" class="mb-4">
      <Icon icon="ep:plus" class="mr-5px" />
      绑定车辆
    </el-button>
    
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="车辆编号" prop="vehicleNo" />
      <el-table-column label="车辆型号" prop="vehicleModel" />
      <el-table-column label="当前套餐">
        <template #default="{ row }">
          <div v-if="row.currentPackageName">
            <div class="package-name">{{ row.currentPackageName }}</div>
            <el-tag :type="getPackageStatusType(row)" size="small">
              {{ getPackageStatusText(row) }}
            </el-tag>
          </div>
          <span v-else class="text-gray-400">未设置套餐</span>
        </template>
      </el-table-column>
      <el-table-column label="套餐有效期" width="200">
        <template #default="{ row }">
          <div v-if="row.currentPackageStartTime && row.currentPackageEndTime">
            <div>{{ formatDate(row.currentPackageStartTime) }}</div>
            <div>至 {{ formatDate(row.currentPackageEndTime) }}</div>
          </div>
          <span v-else class="text-gray-400">-</span>
        </template>
      </el-table-column>
      <el-table-column label="剩余额度" width="100">
        <template #default="{ row }">
          <span v-if="row.currentPackageRemainingQuota !== null">
            {{ row.currentPackageRemainingQuota }}
          </span>
          <span v-else class="text-gray-400">-</span>
        </template>
      </el-table-column>
      <el-table-column label="自动续期" width="100">
        <template #default="{ row }">
          <el-switch v-model="row.autoRenewal" @change="updateAutoRenewal(row)" />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="300">
        <template #default="{ row }">
          <el-button type="primary" link @click="openPurchaseDialog(row)">购买套餐</el-button>
          <el-button type="success" link @click="openSetPackageDialog(row)">配置套餐</el-button>
          <el-button type="info" link @click="openPackageHistoryDialog(row)">历史记录</el-button>
          <el-button type="danger" link @click="unbindVehicle(row)">解绑</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 绑定车辆弹窗 -->
    <el-dialog v-model="bindDialogVisible" title="绑定车辆" width="500px">
      <el-form :model="bindForm" label-width="100px">
        <el-form-item label="车牌号" required>
          <el-input v-model="bindForm.vehicleNo" placeholder="请输入车牌号" />
        </el-form-item>
        <el-form-item label="车辆型号">
          <el-input v-model="bindForm.vehicleModel" placeholder="请输入车辆型号" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="bindDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleBindVehicle">确定</el-button>
      </template>
    </el-dialog>

    <!-- 购买套餐弹窗 -->
    <el-dialog v-model="purchaseDialogVisible" title="购买套餐" width="800px">
      <div class="purchase-header">
        <span>车辆：{{ currentVehicle?.vehicleNo }} ({{ currentVehicle?.vehicleModel }})</span>
      </div>
      
      <div class="package-grid">
        <div 
          v-for="pkg in packageList" 
          :key="pkg.id"
          class="package-card"
          :class="{ 'selected': selectedPackageId === pkg.id }"
          @click="selectPackage(pkg)"
        >
          <div class="package-header">
            <h3>{{ pkg.name }}</h3>
            <div class="package-price">¥{{ (pkg.price / 100).toFixed(2) }}</div>
          </div>
          <div class="package-details">
            <div>周期：{{ pkg.period }} 天</div>
            <div>额度：{{ pkg.quota }}</div>
            <div class="package-desc">{{ pkg.description }}</div>
          </div>
        </div>
      </div>
      
      <div v-if="purchasePreview" class="purchase-preview">
        <h4>购买预览</h4>
        <div class="preview-info">
          <div>套餐名称：{{ purchasePreview.packageName }}</div>
          <div>生效时间：{{ purchasePreview.effectTime }}</div>
          <div>生效说明：{{ purchasePreview.effectDescription }}</div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="purchaseDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handlePurchasePackage" :disabled="!selectedPackageId">
          确认购买
        </el-button>
      </template>
    </el-dialog>

    <!-- 配置套餐弹窗 -->
    <el-dialog v-model="configDialogVisible" title="配置套餐" width="600px">
      <el-form :model="configForm" label-width="100px">
        <el-form-item label="当前车辆">
          <span>{{ currentVehicle?.vehicleNo }} ({{ currentVehicle?.vehicleModel }})</span>
        </el-form-item>
        <el-form-item label="选择套餐" required>
          <el-select v-model="configForm.packageId" placeholder="请选择套餐" style="width: 100%">
            <el-option
              v-for="pkg in packageList"
              :key="pkg.id"
              :label="`${pkg.name} - ¥${(pkg.price / 100).toFixed(2)}`"
              :value="pkg.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="configDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfigPackage">确定</el-button>
      </template>
    </el-dialog>

    <!-- 历史记录弹窗 -->
    <el-dialog v-model="historyDialogVisible" title="套餐历史记录" width="1000px">
      <div class="history-header">
        <span>车辆：{{ currentVehicle?.vehicleNo }} ({{ currentVehicle?.vehicleModel }})</span>
      </div>
      
      <el-table v-loading="historyLoading" :data="historyList" style="width: 100%">
        <el-table-column label="套餐名称" prop="packageName" />
        <el-table-column label="套餐周期" width="100">
          <template #default="{ row }">
            {{ row.packagePeriod }} 天
          </template>
        </el-table-column>
        <el-table-column label="套餐额度" prop="packageQuota" width="100" />
        <el-table-column label="套餐价格" width="100">
          <template #default="{ row }">
            ¥{{ (row.packagePrice / 100).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="有效期" width="200">
          <template #default="{ row }">
            <div>{{ formatDate(row.startTime) }}</div>
            <div>至 {{ formatDate(row.endTime) }}</div>
          </template>
        </el-table-column>
        <el-table-column label="剩余额度" prop="remainingQuota" width="100" />
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getHistoryStatusType(row.status)" size="small">
              {{ row.statusName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>
      </el-table>
      
      <template #footer>
        <el-button @click="historyDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { formatDate } from '@/utils/formatTime'
import * as MemberVehicleApi from '@/api/member/vehicle'
import * as VehiclePackageApi from '@/api/pay/vehicle/package'
import { MemberVehicleVO, MemberVehiclePackageRecordVO } from '@/api/member/vehicle'

/** 用户车辆管理 */
defineOptions({ name: 'UserVehicleManagement' })

const message = useMessage() // 消息弹窗

const loading = ref(false) // 列表的加载中
const list = ref<MemberVehicleVO[]>([]) // 列表的数据
const bindDialogVisible = ref(false) // 绑定车辆弹窗
const purchaseDialogVisible = ref(false) // 购买套餐弹窗
const configDialogVisible = ref(false) // 配置套餐弹窗
const historyDialogVisible = ref(false) // 历史记录弹窗
const packageList = ref([]) // 套餐列表
const currentVehicle = ref<MemberVehicleVO>() // 当前操作的车辆
const selectedPackageId = ref<number>() // 选中的套餐ID
const purchasePreview = ref() // 购买预览信息
const historyLoading = ref(false) // 历史记录加载中
const historyList = ref<MemberVehiclePackageRecordVO[]>([]) // 历史记录列表

// 绑定车辆表单
const bindForm = ref({
  vehicleNo: '',
  vehicleModel: ''
})

// 配置套餐表单
const configForm = ref({
  vehicleId: 0,
  packageId: 0
})

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    // 这里应该调用用户端的API，暂时使用管理端API模拟
    // const data = await MemberVehicleApi.getMyVehicleList()
    // 模拟数据
    list.value = []
  } finally {
    loading.value = false
  }
}

/** 打开绑定车辆弹窗 */
const openBindVehicleDialog = () => {
  bindForm.value = {
    vehicleNo: '',
    vehicleModel: ''
  }
  bindDialogVisible.value = true
}

/** 绑定车辆 */
const handleBindVehicle = async () => {
  try {
    // await MemberVehicleApi.bindMyVehicle(bindForm.value)
    message.success('绑定成功')
    bindDialogVisible.value = false
    await getList()
  } catch {}
}

/** 打开购买套餐弹窗 */
const openPurchaseDialog = async (row: MemberVehicleVO) => {
  currentVehicle.value = row
  selectedPackageId.value = undefined
  purchasePreview.value = null
  
  // 获取套餐列表
  try {
    const data = await VehiclePackageApi.getVehiclePackageList()
    packageList.value = data
  } catch {}
  
  purchaseDialogVisible.value = true
}

/** 选择套餐 */
const selectPackage = async (pkg: any) => {
  selectedPackageId.value = pkg.id
  
  // 获取购买预览
  try {
    // const preview = await MemberVehicleApi.previewPurchase(currentVehicle.value.id, pkg.id)
    // purchasePreview.value = preview
    // 模拟预览数据
    purchasePreview.value = {
      packageName: pkg.name,
      effectTime: new Date().toLocaleString(),
      effectDescription: '新套餐将立即生效'
    }
  } catch {}
}

/** 购买套餐 */
const handlePurchasePackage = async () => {
  try {
    // await MemberVehicleApi.purchasePackage(currentVehicle.value.id, selectedPackageId.value)
    message.success('购买成功')
    purchaseDialogVisible.value = false
    await getList()
  } catch {}
}

/** 打开配置套餐弹窗 */
const openSetPackageDialog = async (row: MemberVehicleVO) => {
  currentVehicle.value = row
  configForm.value = {
    vehicleId: row.id,
    packageId: row.currentPackageId || 0
  }
  
  // 获取套餐列表
  try {
    const data = await VehiclePackageApi.getVehiclePackageList()
    packageList.value = data
  } catch {}
  
  configDialogVisible.value = true
}

/** 配置套餐 */
const handleConfigPackage = async () => {
  try {
    // await MemberVehicleApi.setMyVehiclePackage(configForm.value.vehicleId, configForm.value.packageId)
    message.success('配置成功')
    configDialogVisible.value = false
    await getList()
  } catch {}
}

/** 打开历史记录弹窗 */
const openPackageHistoryDialog = async (row: MemberVehicleVO) => {
  currentVehicle.value = row
  historyDialogVisible.value = true
  
  // 获取历史记录
  historyLoading.value = true
  try {
    // const data = await MemberVehicleApi.getMyPackageRecordList(row.vehicleNo)
    // historyList.value = data
    historyList.value = [] // 模拟空数据
  } catch {
    historyList.value = []
  } finally {
    historyLoading.value = false
  }
}

/** 更新自动续期 */
const updateAutoRenewal = async (row: MemberVehicleVO) => {
  try {
    // await MemberVehicleApi.updateMyAutoRenewal(row.id, row.autoRenewal)
    message.success('更新成功')
  } catch {
    // 失败时恢复原值
    row.autoRenewal = !row.autoRenewal
  }
}

/** 解绑车辆 */
const unbindVehicle = async (row: MemberVehicleVO) => {
  try {
    await message.delConfirm('确定要解绑该车辆吗？')
    // await MemberVehicleApi.unbindMyVehicle(row.id)
    message.success('解绑成功')
    await getList()
  } catch {}
}

/** 获取套餐状态类型 */
const getPackageStatusType = (vehicle: MemberVehicleVO) => {
  if (!vehicle.currentPackageEndTime) return 'info'
  
  const now = new Date()
  const endTime = new Date(vehicle.currentPackageEndTime)
  
  if (endTime < now) return 'danger' // 已过期
  
  const diffDays = Math.ceil((endTime.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
  if (diffDays <= 3) return 'warning' // 即将过期
  
  return 'success' // 正常
}

/** 获取套餐状态文本 */
const getPackageStatusText = (vehicle: MemberVehicleVO) => {
  if (!vehicle.currentPackageEndTime) return '未知状态'
  
  const now = new Date()
  const endTime = new Date(vehicle.currentPackageEndTime)
  
  if (endTime < now) return '已过期'
  
  const diffDays = Math.ceil((endTime.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
  if (diffDays <= 3) return `${diffDays}天后过期`
  
  return '生效中'
}

/** 获取历史记录状态类型 */
const getHistoryStatusType = (status: number) => {
  switch (status) {
    case 1: return 'success' // 生效中
    case 2: return 'info'    // 已过期
    case 3: return 'warning' // 待生效
    case 4: return 'danger'  // 已取消
    default: return 'info'
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<style scoped>
.page-header {
  margin-bottom: 20px;
  padding: 20px 0;
  border-bottom: 1px solid #ebeef5;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.package-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.text-gray-400 {
  color: #9ca3af;
}

.purchase-header {
  margin-bottom: 20px;
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
  font-weight: bold;
}

.package-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.package-card {
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s;
}

.package-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.package-card.selected {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.package-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.package-header h3 {
  margin: 0;
  color: #303133;
}

.package-price {
  font-size: 18px;
  font-weight: bold;
  color: #e6a23c;
}

.package-details div {
  margin-bottom: 4px;
  font-size: 14px;
  color: #606266;
}

.package-desc {
  color: #909399 !important;
  font-size: 12px !important;
}

.purchase-preview {
  padding: 16px;
  background-color: #f0f9ff;
  border-radius: 6px;
  border: 1px solid #b3d8ff;
}

.purchase-preview h4 {
  margin: 0 0 12px 0;
  color: #409eff;
}

.preview-info div {
  margin-bottom: 4px;
  font-size: 14px;
  color: #606266;
}

.history-header {
  margin-bottom: 16px;
  padding: 8px 0;
  border-bottom: 1px solid #ebeef5;
  font-weight: bold;
}
</style>
