<template>
  <div>
    <div style="margin-bottom: 16px;">
      <el-button type="primary" @click="openBindVehicleDialog">绑定车辆</el-button>
      <el-button type="default" @click="handleRefresh" :loading="loading" style="margin-left: 8px;">
        <Icon icon="ep:refresh" class="mr-5px" />
        刷新
      </el-button>
    </div>
    
    <el-table v-loading="loading" :data="list">
      <el-table-column label="车辆编号" prop="vehicleNo" />
      <el-table-column label="车辆型号" prop="vehicleModel" />
      <el-table-column label="当前套餐">
        <template #default="{ row }">
          <div v-if="row.currentPackageName">
            <div>{{ row.currentPackageName }}</div>
            <el-tag :type="getPackageStatusType(row)" size="small">
              {{ getPackageStatusText(row) }}
            </el-tag>
          </div>
          <span v-else class="text-gray-400">未设置套餐</span>
        </template>
      </el-table-column>
      <el-table-column label="套餐有效期">
        <template #default="{ row }">
          <div v-if="row.currentPackageStartTime && row.currentPackageEndTime">
            <div>{{ formatDate(row.currentPackageStartTime) }}</div>
            <div>至 {{ formatDate(row.currentPackageEndTime) }}</div>
          </div>
          <span v-else class="text-gray-400">-</span>
        </template>
      </el-table-column>
      <el-table-column label="剩余额度">
        <template #default="{ row }">
          <span v-if="row.currentPackageRemainingQuota !== null">
            {{ row.currentPackageRemainingQuota }}
          </span>
          <span v-else class="text-gray-400">-</span>
        </template>
      </el-table-column>
      <el-table-column label="待生效套餐">
        <template #default="{ row }">
          <div v-if="row.pendingPackageName">
            <div>{{ row.pendingPackageName }}</div>
            <el-tag type="warning" size="small">待生效</el-tag>
          </div>
          <span v-else class="text-gray-400">无</span>
        </template>
      </el-table-column>
      <el-table-column label="自动续期">
        <template #default="{ row }">
          <el-switch v-model="row.autoRenewal" @change="updateAutoRenewal(row)" />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button type="primary" link @click="openSetPackageDialog(row)">设置套餐</el-button>
          <el-button type="info" link @click="openPackageHistoryDialog(row)">历史记录</el-button>
          <el-button type="danger" link @click="unbindVehicle(row)">解绑</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 绑定车辆弹窗 -->
    <el-dialog v-model="bindDialogVisible" title="绑定车辆" width="500px">
      <el-form :model="bindForm" label-width="100px">
        <el-form-item label="车牌号" required>
          <el-input v-model="bindForm.vehicleNo" placeholder="请输入车牌号" />
        </el-form-item>
        <el-form-item label="车辆型号">
          <el-input v-model="bindForm.vehicleModel" placeholder="请输入车辆型号" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="bindDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleBindVehicle">确定</el-button>
      </template>
    </el-dialog>

    <!-- 设置套餐弹窗 -->
    <el-dialog v-model="packageDialogVisible" title="设置套餐" width="700px">
      <el-form :model="packageForm" label-width="120px">
        <el-form-item label="当前车辆">
          <span>{{ currentVehicle?.vehicleNo }} ({{ currentVehicle?.vehicleModel }})</span>
        </el-form-item>

        <!-- 当前套餐状态 -->
        <el-form-item label="当前套餐详情" v-if="currentVehicle?.currentPackageName">
          <div class="package-status-info">
            <div class="package-header">
              <div class="package-name">{{ currentVehicle.currentPackageName }}</div>
              <el-tag :type="getPackageStatusType(currentVehicle)" size="small">
                {{ getPackageStatusText(currentVehicle) }}
              </el-tag>
            </div>
            <div class="package-details">
              <div class="detail-row">
                <span class="label">套餐周期：</span>
                <span>{{ getCurrentPackageDetail('period') }} 天</span>
              </div>
              <div class="detail-row">
                <span class="label">套餐额度：</span>
                <span>{{ getCurrentPackageDetail('quota') }}</span>
              </div>
              <div class="detail-row">
                <span class="label">套餐价格：</span>
                <span>¥{{ getCurrentPackageDetail('price') }}</span>
              </div>
              <div class="detail-row">
                <span class="label">有效期：</span>
                <span>{{ formatDate(currentVehicle.currentPackageStartTime) }} 至 {{ formatDate(currentVehicle.currentPackageEndTime) }}</span>
              </div>
              <div class="detail-row">
                <span class="label">剩余额度：</span>
                <span class="remaining-quota">{{ currentVehicle.currentPackageRemainingQuota }}</span>
              </div>
            </div>
          </div>
        </el-form-item>

        <!-- 待生效套餐 -->
        <el-form-item label="待生效套餐" v-if="currentVehicle?.pendingPackageName">
          <div class="pending-package-info">
            <div class="package-name">{{ currentVehicle.pendingPackageName }}</div>
            <el-tag type="warning" size="small">待生效</el-tag>
          </div>
        </el-form-item>

        <el-form-item label="选择套餐" required>
          <el-select v-model="packageForm.packageId" placeholder="请选择套餐" style="width: 100%" @change="onPackageChange">
            <el-option
              v-for="pkg in packageList"
              :key="pkg.id"
              :label="`${pkg.name} - ¥${(pkg.price / 100).toFixed(2)}`"
              :value="pkg.id"
            >
              <div>
                <div>{{ pkg.name }} - ¥{{ (pkg.price / 100).toFixed(2) }}</div>
                <div style="font-size: 12px; color: #999;">
                  {{ pkg.description }}
                </div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 新套餐生效预览 -->
        <el-form-item label="生效情况预览" v-if="selectedPackage">
          <div class="package-preview">
            <div class="preview-title">新套餐：{{ selectedPackage.name }}</div>
            <div class="preview-info">
              <div>套餐周期：{{ selectedPackage.period }} 天</div>
              <div>套餐额度：{{ selectedPackage.quota }}</div>
              <div>套餐价格：¥{{ (selectedPackage.price / 100).toFixed(2) }}</div>
            </div>
            <div class="preview-effect">
              <el-alert
                :title="getEffectPreviewText()"
                type="info"
                :closable="false"
                show-icon
              />
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="packageDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSetPackage">确定</el-button>
      </template>
    </el-dialog>

    <!-- 套餐历史记录弹窗 -->
    <el-dialog v-model="historyDialogVisible" title="车辆套餐历史记录" width="1000px">
      <div class="history-header">
        <span>车辆：{{ currentVehicle?.vehicleNo }} ({{ currentVehicle?.vehicleModel }})</span>
      </div>

      <el-table v-loading="historyLoading" :data="historyList" style="width: 100%">
        <el-table-column label="套餐名称" prop="packageName" />
        <el-table-column label="套餐周期" width="100">
          <template #default="{ row }">
            {{ row.packagePeriod }} 天
          </template>
        </el-table-column>
        <el-table-column label="套餐额度" prop="packageQuota" width="100" />
        <el-table-column label="套餐价格" width="100">
          <template #default="{ row }">
            ¥{{ (row.packagePrice / 100).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="有效期" width="200">
          <template #default="{ row }">
            <div>{{ formatDate(row.startTime) }}</div>
            <div>至 {{ formatDate(row.endTime) }}</div>
          </template>
        </el-table-column>
        <el-table-column label="剩余额度" prop="remainingQuota" width="100" />
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getHistoryStatusType(row.status)" size="small">
              {{ row.statusName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>
      </el-table>

      <template #footer>
        <el-button @click="historyDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { formatDate } from '@/utils/formatTime'
import * as MemberVehicleApi from '@/api/member/vehicle'
import * as VehiclePackageApi from '@/api/pay/vehicle/package'
import { MemberVehicleVO, MemberVehiclePackageRecordVO } from '@/api/member/vehicle'

/** 用户车辆列表 */
defineOptions({ name: 'UserVehicleList' })

const props = defineProps<{
  userId: number
}>()

const message = useMessage() // 消息弹窗

const loading = ref(false) // 列表的加载中
const list = ref<MemberVehicleVO[]>([]) // 列表的数据
const bindDialogVisible = ref(false) // 绑定车辆弹窗
const packageDialogVisible = ref(false) // 设置套餐弹窗
const historyDialogVisible = ref(false) // 历史记录弹窗
const packageList = ref([]) // 套餐列表
const currentVehicle = ref<MemberVehicleVO>() // 当前操作的车辆
const selectedPackage = ref() // 选中的套餐
const currentPackageDetails = ref() // 当前套餐详情
const historyLoading = ref(false) // 历史记录加载中
const historyList = ref<MemberVehiclePackageRecordVO[]>([]) // 历史记录列表

// 绑定车辆表单
const bindForm = ref({
  vehicleNo: '',
  vehicleModel: ''
})

// 套餐设置表单
const packageForm = ref({
  vehicleId: 0,
  packageId: 0
})

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await MemberVehicleApi.getMemberVehicleList(props.userId)
    list.value = data
  } finally {
    loading.value = false
  }
}

/** 打开绑定车辆弹窗 */
const openBindVehicleDialog = () => {
  bindForm.value = {
    vehicleNo: '',
    vehicleModel: ''
  }
  bindDialogVisible.value = true
}

/** 绑定车辆 */
const handleBindVehicle = async () => {
  try {
    await MemberVehicleApi.bindVehicle({
      userId: props.userId,
      vehicleNo: bindForm.value.vehicleNo,
      vehicleModel: bindForm.value.vehicleModel
    })
    message.success('绑定成功')
    bindDialogVisible.value = false
    await getList()
  } catch {}
}

/** 打开设置套餐弹窗 */
const openSetPackageDialog = async (row: MemberVehicleVO) => {
  currentVehicle.value = row
  packageForm.value = {
    vehicleId: row.id,
    packageId: 0 // 重置为0，不要设置为当前套餐ID
  }
  selectedPackage.value = null
  currentPackageDetails.value = null

  // 获取套餐列表
  try {
    const data = await VehiclePackageApi.getVehiclePackageList()
    packageList.value = data

    // 如果有当前套餐，获取当前套餐详情（不影响selectedPackage）
    if (row.currentPackageId) {
      try {
        const currentPackageData = await VehiclePackageApi.getVehiclePackage(row.currentPackageId)
        currentPackageDetails.value = currentPackageData
      } catch {
        // 如果API调用失败，从列表中查找
        currentPackageDetails.value = data.find(pkg => pkg.id === row.currentPackageId)
      }
    }
  } catch {}

  packageDialogVisible.value = true
}

/** 设置套餐 */
const handleSetPackage = async () => {
  try {
    await MemberVehicleApi.setVehiclePackage(packageForm.value.vehicleId, packageForm.value.packageId)
    message.success('设置成功')
    packageDialogVisible.value = false
    await getList()
  } catch {}
}

/** 更新自动续期 */
const updateAutoRenewal = async (row: MemberVehicleVO) => {
  try {
    await MemberVehicleApi.updateAutoRenewal(row.id, row.autoRenewal)
    message.success('更新成功')
  } catch {
    // 失败时恢复原值
    row.autoRenewal = !row.autoRenewal
  }
}

/** 解绑车辆 */
const unbindVehicle = async (row: MemberVehicleVO) => {
  try {
    await message.delConfirm('确定要解绑该车辆吗？解绑后，车辆关联的套餐将全部失效！')
    await MemberVehicleApi.unbindVehicle(row.id)
    message.success('解绑成功')
    await getList()
  } catch {}
}

/** 套餐选择变化 */
const onPackageChange = (packageId: number) => {
  selectedPackage.value = packageList.value.find(pkg => pkg.id === packageId)
}

/** 获取生效情况预览文本 */
const getEffectPreviewText = () => {
  if (!selectedPackage.value || !currentVehicle.value) return ''

  const now = new Date()
  const currentEndTime = currentVehicle.value.currentPackageEndTime ? new Date(currentVehicle.value.currentPackageEndTime) : null

  if (currentVehicle.value.currentPackageId) {
    if (currentEndTime && currentEndTime > now) {
      return `新套餐将在当前套餐到期后（${formatDate(currentEndTime)}）自动生效`
    } else {
      return '新套餐将立即生效，替换当前已过期的套餐'
    }
  } else {
    return '新套餐将立即生效'
  }
}

/** 获取套餐状态类型 */
const getPackageStatusType = (vehicle: MemberVehicleVO) => {
  if (!vehicle.currentPackageEndTime) return 'info'

  const now = new Date()
  const endTime = new Date(vehicle.currentPackageEndTime)

  if (endTime < now) return 'danger' // 已过期

  const diffDays = Math.ceil((endTime.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
  if (diffDays <= 3) return 'warning' // 即将过期

  return 'success' // 正常
}

/** 获取套餐状态文本 */
const getPackageStatusText = (vehicle: MemberVehicleVO) => {
  if (!vehicle.currentPackageEndTime) return '未知状态'

  const now = new Date()
  const endTime = new Date(vehicle.currentPackageEndTime)

  if (endTime < now) return '已过期'

  const diffDays = Math.ceil((endTime.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
  if (diffDays <= 3) return `${diffDays}天后过期`

  return '生效中'
}

/** 获取历史记录状态类型 */
const getHistoryStatusType = (status: number) => {
  switch (status) {
    case 1: return 'success' // 生效中
    case 2: return 'info'    // 已过期
    case 3: return 'warning' // 待生效
    case 4: return 'danger'  // 已取消
    default: return 'info'
  }
}

/** 获取当前套餐详情 */
const getCurrentPackageDetail = (field: string) => {
  if (!currentVehicle.value?.currentPackageId) {
    return '-'
  }

  // 优先使用弹窗中加载的详细信息
  if (currentPackageDetails.value) {
    const value = currentPackageDetails.value[field]
    if (field === 'price' && value) {
      return (value / 100).toFixed(2)
    }
    return value || '-'
  }

  // 如果没有详细信息，尝试从套餐列表中查找
  if (packageList.value.length > 0) {
    const packageInfo = packageList.value.find(pkg => pkg.id === currentVehicle.value.currentPackageId)
    if (packageInfo) {
      const value = packageInfo[field]
      if (field === 'price' && value) {
        return (value / 100).toFixed(2)
      }
      return value || '-'
    }
  }

  return '-'
}

/** 打开套餐历史记录弹窗 */
const openPackageHistoryDialog = async (row: MemberVehicleVO) => {
  currentVehicle.value = row
  historyDialogVisible.value = true

  // 获取历史记录
  historyLoading.value = true
  try {
    const data = await MemberVehicleApi.getVehiclePackageRecordList(props.userId, row.vehicleNo)
    historyList.value = data
  } catch {
    historyList.value = []
  } finally {
    historyLoading.value = false
  }
}

/** 刷新列表 */
const handleRefresh = async () => {
  await getList()
  message.success('刷新成功')
}

/** 暴露方法给父组件调用 */
defineExpose({
  getList,
  handleRefresh
})

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<style scoped>
.package-status-info {
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.package-status-info .package-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.package-status-info .package-name {
  font-weight: bold;
  font-size: 16px;
  color: #303133;
}

.package-status-info .package-details {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.package-status-info .detail-row {
  display: flex;
  align-items: center;
}

.package-status-info .detail-row .label {
  font-weight: 500;
  color: #606266;
  width: 80px;
  flex-shrink: 0;
}

.package-status-info .remaining-quota {
  font-weight: bold;
  color: #409eff;
}

.pending-package-info {
  padding: 8px;
  background-color: #fdf6ec;
  border-radius: 4px;
  border-left: 3px solid #e6a23c;
}

.pending-package-info .package-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.package-preview {
  padding: 12px;
  background-color: #f0f9ff;
  border-radius: 6px;
  border: 1px solid #b3d8ff;
}

.package-preview .preview-title {
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.package-preview .preview-info {
  margin-bottom: 8px;
}

.package-preview .preview-info div {
  font-size: 13px;
  color: #666;
  margin-bottom: 2px;
}

.history-header {
  margin-bottom: 16px;
  padding: 8px 0;
  border-bottom: 1px solid #ebeef5;
  font-weight: bold;
}

.text-gray-400 {
  color: #9ca3af;
}
</style>
