<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="分组名称" prop="groupName">
        <el-input v-model="formData.groupName" placeholder="请输入分组名称" />
      </el-form-item>
      <el-form-item label="排序号" prop="indexs">
        <el-input-number 
          v-model="formData.indexs" 
          placeholder="请输入排序号"
          :min="0"
          :max="9999"
          controls-position="right"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="是否启用" prop="groupStatus">
        <el-switch
          v-model="formData.groupStatus"
          :active-value="1"
          :inactive-value="0"
          inline-prompt
          active-text="启用"
          inactive-text="禁用"
          class="reverse-switch"
        />
      </el-form-item>
      <el-form-item label="套餐ids" prop="mealIds">
        <el-input v-model="formData.mealIds" placeholder="请输入套餐ids" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { CarGroupApi, CarGroupVO } from '@/api/vehicle/cargroup'

/** 车辆分组 表单 */
defineOptions({ name: 'CarGroupForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  carGroupId: undefined,
  groupName: undefined,
  indexs: undefined,
  groupStatus: 1,
  mealIds: undefined
})
const formRules = reactive({
  groupName: [{ required: true, message: '分组名称不能为空', trigger: 'blur' }],
  indexs: [{ required: true, message: '排序号不能为空', trigger: 'blur' }],
  mealIds: [{ required: true, message: '套餐ids不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await CarGroupApi.getCarGroup(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as CarGroupVO
    if (formType.value === 'create') {
      await CarGroupApi.createCarGroup(data)
      message.success(t('common.createSuccess'))
    } else {
      await CarGroupApi.updateCarGroup(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    carGroupId: undefined,
    groupName: undefined,
    indexs: undefined,
    groupStatus: 1,
    mealIds: undefined
  }
  formRef.value?.resetFields()
}
</script>