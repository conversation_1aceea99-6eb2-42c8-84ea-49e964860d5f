<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['vehicle:car-group:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['vehicle:car-group:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-loading="loading"
      :data="list" :stripe="true"
      :show-overflow-tooltip="true"
      border
    >
      <el-table-column label="车辆分组" align="center" prop="carGroupId" resizable/>
      <el-table-column label="分组名称" align="center" prop="groupName" resizable/>
      <el-table-column label="排序号" align="center" prop="indexs" resizable/>
      <el-table-column label="是否启用" align="center" prop="groupStatus" resizable>
        <template #default="scope">
          <el-switch
            v-model="scope.row.groupStatus"
            :active-value="1"
            :inactive-value="0"
            :disabled="!checkPermi(['vehicle:car-group:update'])"
            :loading="statusUpdating[scope.row.carGroupId]"
            @change="(value: number) => handleStatusChange(value, scope.row)"
            inline-prompt
            active-text="启用"
            inactive-text="禁用"
            class="reverse-switch"
          />
        </template>
      </el-table-column>
      <el-table-column label="套餐ids" align="center" prop="mealIds" resizable/>
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['vehicle:car-group:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['vehicle:car-group:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <CarGroupForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import download from '@/utils/download'
import { CarGroupApi, CarGroupVO } from '@/api/vehicle/cargroup'
import CarGroupForm from './CarGroupForm.vue'
import { checkPermi } from '@/utils/permission'

/** 车辆分组 列表 */
defineOptions({ name: 'CarGroup' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<CarGroupVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

// 状态更新中的映射
const statusUpdating = ref<Record<number, boolean>>({})

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await CarGroupApi.getCarGroupPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await CarGroupApi.deleteCarGroup(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await CarGroupApi.exportCarGroup(queryParams)
    download.excel(data, '车辆分组.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 修改状态操作 */
const handleStatusChange = async (value: number, row: CarGroupVO) => {
  // 标记正在更新
  statusUpdating.value[row.carGroupId] = true
  try {
    // 更新状态
    await CarGroupApi.updateCarGroup({
      ...row,
      groupStatus: value
    })
    message.success(t('common.updateSuccess'))
  } catch {
    // 如果更新失败，恢复原来的状态
    row.groupStatus = value === 1 ? 0 : 1
    message.error(t('common.updateFailed'))
  } finally {
    // 标记更新完成
    statusUpdating.value[row.carGroupId] = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
