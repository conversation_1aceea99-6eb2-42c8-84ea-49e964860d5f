<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item :label="t('vehicle.buscar.carNo')" prop="carNo" label-width="90px">
        <el-input
          v-model="queryParams.carNo"
          placeholder="车牌号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
<!--      <el-form-item label="车辆类型" prop="carType">-->
<!--        <el-select-->
<!--          v-model="queryParams.carType"-->
<!--          placeholder="请选择车辆类型"-->
<!--          clearable-->
<!--          class="!w-240px"-->
<!--        >-->
<!--          <el-option-->
<!--            v-for="dict in getIntDictOptions(DICT_TYPE.CAR_TYPE)"-->
<!--            :key="dict.value"-->
<!--            :label="dict.label"-->
<!--            :value="dict.value"-->
<!--          />-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="租赁开始时间" prop="leaseStartTime">-->
<!--        <el-date-picker-->
<!--          v-model="queryParams.leaseStartTime"-->
<!--          value-format="YYYY-MM-DD HH:mm:ss"-->
<!--          type="daterange"-->
<!--          start-placeholder="开始日期"-->
<!--          end-placeholder="结束日期"-->
<!--          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"-->
<!--          class="!w-220px"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="租赁结束时间" prop="leaseEndTime">-->
<!--        <el-date-picker-->
<!--          v-model="queryParams.leaseEndTime"-->
<!--          value-format="YYYY-MM-DD HH:mm:ss"-->
<!--          type="daterange"-->
<!--          start-placeholder="开始日期"-->
<!--          end-placeholder="结束日期"-->
<!--          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"-->
<!--          class="!w-220px"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="充电开关" prop="charged">-->
<!--        <el-select-->
<!--          v-model="queryParams.charged"-->
<!--          placeholder="请选择充电开关"-->
<!--          clearable-->
<!--          class="!w-240px"-->
<!--        >-->
<!--          <el-option-->
<!--            v-for="dict in getIntDictOptions(DICT_TYPE.OPENORCLOSE)"-->
<!--            :key="dict.value"-->
<!--            :label="dict.label"-->
<!--            :value="dict.value"-->
<!--          />-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="开仓开关" prop="opened">-->
<!--        <el-select-->
<!--          v-model="queryParams.opened"-->
<!--          placeholder="请选择开仓开关"-->
<!--          clearable-->
<!--          class="!w-240px"-->
<!--        >-->
<!--          <el-option-->
<!--            v-for="dict in getIntDictOptions(DICT_TYPE.OPENORCLOSE)"-->
<!--            :key="dict.value"-->
<!--            :label="dict.label"-->
<!--            :value="dict.value"-->
<!--          />-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <el-form-item label="是否绑定" prop="binged">
        <el-select
          v-model="queryParams.binged"
          placeholder="请选择是否绑定"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.YES_OR_NO)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="关联用户" prop="userId">
        <el-select
          v-model="queryParams.userId"
          filterable
          remote
          clearable
          :remote-method="fetchNicknameOptions"
          :loading="userLoading"
          placeholder="请输入用户昵称"
          class="!w-240px"
          @change="onUserIdChange"
        >
          <el-option
            v-for="user in userOptions"
            :key="user.id"
            :label="user.nickname"
            :value="user.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="手机号" prop="userIdMobile">
        <el-select
          v-model="queryParams.userId"
          filterable
          remote
          clearable
          :remote-method="fetchMobileOptions"
          :loading="userLoading"
          placeholder="请输入手机号"
          class="!w-240px"
          @change="onUserIdChange"
        >
          <el-option
            v-for="user in userOptions"
            :key="user.id"
            :label="user.mobile"
            :value="user.id"
          />
        </el-select>
      </el-form-item>
<!--      <el-form-item label="车型" prop="carTypeId">-->
<!--        <el-select-->
<!--          v-model="queryParams.carTypeId"-->
<!--          placeholder="请选择车型"-->
<!--          clearable-->
<!--          class="!w-240px"-->
<!--        >-->
<!--          <el-option-->
<!--            v-for="dict in getIntDictOptions(DICT_TYPE.CAR_MODEL)"-->
<!--            :key="dict.value"-->
<!--            :label="dict.label"-->
<!--            :value="dict.value"-->
<!--          />-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['vehicle:bus-car:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['vehicle:bus-car:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
        <el-button
          type="warning"
          plain
          @click="handleImportTemplate"
          v-hasPermi="['vehicle:bus-car:import']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 下载导入模板
        </el-button>
        <el-button
          type="primary"
          plain
          @click="handleImport"
          v-hasPermi="['vehicle:bus-car:import']"
        >
          <Icon icon="ep:upload" class="mr-5px" /> 导入
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table 
      v-loading="loading" 
      :data="list" 
      :stripe="true" 
      :show-overflow-tooltip="true"
      border
    >
      <el-table-column label="车辆id" align="center" prop="carId" v-if="false" resizable />
      <el-table-column label="车牌号" align="center" prop="carNo" min-width="120px" resizable />
<!--      <el-table-column label="车辆类型" align="center" prop="carType" resizable>-->
<!--        <template #default="scope">-->
<!--          <dict-tag :type="DICT_TYPE.CAR_TYPE" :value="scope.row.carType" />-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="车辆分组" align="center" prop="groupName" resizable>-->
<!--        <template #default="scope">-->
<!--          <span>{{ scope.row.groupName }}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="收费标准" align="center" prop="chargeId" min-width="110px" resizable>-->
<!--        <template #default="scope">-->
<!--          <dict-tag :type="DICT_TYPE.CAR_CHARGE" :value="scope.row.chargeId" />-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column-->
<!--        label="租赁开始时间"-->
<!--        align="center"-->
<!--        prop="leaseStartTime"-->
<!--        :formatter="dateFormatter"-->
<!--        width="180px"-->
<!--        resizable-->
<!--      />-->
<!--      <el-table-column-->
<!--        label="租赁结束时间"-->
<!--        align="center"-->
<!--        prop="leaseEndTime"-->
<!--        :formatter="dateFormatter"-->
<!--        width="180px"-->
<!--        resizable-->
<!--      />-->
<!--      <el-table-column label="充电开关" align="center" prop="charged" resizable>-->
<!--        <template #default="scope">-->
<!--          <dict-tag :type="DICT_TYPE.OPENORCLOSE" :value="scope.row.charged" />-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="开仓开关" align="center" prop="opened" resizable>-->
<!--        <template #default="scope">-->
<!--          <dict-tag :type="DICT_TYPE.OPENORCLOSE" :value="scope.row.opened" />-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
        resizable
      />
      <el-table-column
        label="更新时间"
        align="center"
        prop="updateTime"
        :formatter="dateFormatter"
        width="180px"
        resizable
      />
      <el-table-column label="是否绑定" align="center" prop="binged" resizable>
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.YES_OR_NO" :value="scope.row.binged" />
        </template>
      </el-table-column>
      <el-table-column label="关联用户" align="center" prop="nickname" resizable />
      <el-table-column label="手机号" align="center" prop="mobile" min-width="120px" resizable />
      <el-table-column label="车型id" align="center" prop="carTypeId" v-if="false" resizable>
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.CAR_MODEL" :value="scope.row.carTypeId" />
        </template>
      </el-table-column>
      <el-table-column label="车型名称" align="center" prop="carTypeText" resizable />
      <el-table-column label="操作" align="center" min-width="200px" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.carId)"
            v-hasPermi="['vehicle:bus-car:update']"
          >
            编辑
          </el-button>
          <el-button
            v-if="scope.row.binged === 1"
            link
            type="danger"
            @click="handleDelete(scope.row.carId)"
            v-hasPermi="['vehicle:bus-car:unbind']"
          >
            解绑
          </el-button>
          <el-button
            link
            type="primary"
            @click="showCarList(scope.row.carNo)"
          >
            换电记录
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <BusCarForm ref="formRef" @success="getList" />

  <!-- 换电记录组件 -->
  <CarList ref="carListRef" />

  <!-- 隐藏的上传组件 -->
  <el-upload
    ref="uploadRef"
    action=""
    :show-file-list="false"
    :auto-upload="false"
    :on-change="handleFileChange"
    :on-success="handleImportSuccess"
    :on-error="handleImportError"
    style="display: none"
  >
    <el-button ref="uploadButton">上传</el-button>
  </el-upload>
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { BusCarApi, BusCarVO } from '@/api/vehicle/buscar'
import BusCarForm from './BusCarForm.vue'
import CarList from './CarList.vue'
import { getUserPage } from '@/api/member/user'
import type { UserVO } from '@/api/member/user'

/** 车辆管理 列表 */
defineOptions({ name: 'BusCar' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<BusCarVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  carNo: undefined,
  carType: undefined,
  leaseStartTime: [],
  leaseEndTime: [],
  charged: undefined,
  opened: undefined,
  binged: undefined,
  nickname: undefined as string | undefined,
  mobile: undefined as string | undefined,
  carTypeId: undefined,
  userId: undefined,
  userIdMobile: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

const userOptions = ref<UserVO[]>([])
const userLoading = ref(false)

const carListRef = ref()

const uploadRef = ref()
const uploadButton = ref()

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await BusCarApi.getBusCarPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = async (type: string, id?: number) => {
    formRef.value.open(type, id)
}

/** 删除按钮操作 */
// const handleDelete = async (id: number) => {
//   try {
//     // 删除的二次确认
//     await message.delConfirm()
//     // 发起删除
//     await BusCarApi.deleteBusCar(id)
//     message.success(t('common.delSuccess'))
//     // 刷新列表
//     await getList()
//   } catch {}
// }
/** 解绑按钮操作 @todo为什么是delete？提示也是删除？ */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await BusCarApi.unBind(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await BusCarApi.exportBusCar(queryParams)
    download.excel(data, '车辆管理.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 下载导入模板 */
const handleImportTemplate = async () => {
  try {
    window.location.href = '../../../车辆导入模板.xlsx'
  } catch {}
}

/** 导入按钮操作 */
const handleImport = () => {
  // 直接触发文件选择
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.xlsx,.xls'
  input.onchange = (e: any) => {
    const file = e.target.files[0]
    if (file) {
      handleFileChange(file)
    }
  }
  input.click()
}

/** 文件选择改变 */
const handleFileChange = (file: any) => {
  console.log('选择的文件:', file) // 添加日志
  if (!file) {
    message.error('请选择文件')
    return
  }
  
  // 检查文件类型
  const isExcel = file.name.endsWith('.xlsx') || file.name.endsWith('.xls')
  if (!isExcel) {
    message.error('只能上传 Excel 文件!')
    return
  }

  loading.value = true
  const formData = new FormData()
  formData.append('file', file)
  
  // 直接使用 API 调用而不是通过 el-upload
  BusCarApi.importBusCar(formData)
    .then(response => {
      loading.value = false
      if (response.code === 0) {
        message.success('导入成功')
        getList()
      } else {
        message.error(response.msg || '导入失败')
      }
    })
    .catch(error => {
      loading.value = false
      message.error('导入失败：' + (error.message || '未知错误'))
    })
}

/** 导入成功 */
const handleImportSuccess = (response: any) => {
  loading.value = false
  if (response.code === 0) {
    message.success('导入成功')
    getList()
  } else {
    message.error(response.msg || '导入失败')
  }
}

/** 导入失败 */
const handleImportError = () => {
  loading.value = false
  message.error('导入失败')
}

/** 显示换电记录 */
const showCarList = (carNo?: string) => {
  if (!carListRef.value) {
    message.error('换电记录组件加载失败')
    return
  }
  carListRef.value.open(carNo)
}

const fetchMobileOptions = async (query: string) => {
  if (!query) {
    userOptions.value = []
    return
  }
  userLoading.value = true
  try {
    const params = { mobile: query, pageNo: 1, pageSize: 20 }
    const res = await getUserPage(params)
    userOptions.value = (res.list || []) as UserVO[]
  } finally {
    userLoading.value = false
  }
}

const fetchNicknameOptions = async (query: string) => {
  if (!query) {
    userOptions.value = []
    return
  }
  userLoading.value = true
  try {
    const params = { nickname: query, pageNo: 1, pageSize: 20 }
    const res = await getUserPage(params)
    userOptions.value = (res.list || []) as UserVO[]
  } finally {
    userLoading.value = false
  }
}

const onUserIdChange = (userId: number) => {
  const user = userOptions.value.find(u => u.id === userId)
  if (user) {
    queryParams.nickname = user.nickname
    queryParams.mobile = user.mobile
  } else {
    queryParams.nickname = undefined
    queryParams.mobile = undefined
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
