<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="车牌号" prop="carNo">
        <el-input v-model="formData.carNo" placeholder="请输入车牌号" />
      </el-form-item>
<!--      <el-form-item label="车辆类型" prop="carType">-->
<!--        <el-select v-model="formData.carType" placeholder="请选择车辆类型">-->
<!--          <el-option-->
<!--            v-for="dict in getIntDictOptions(DICT_TYPE.CAR_TYPE)"-->
<!--            :key="dict.value"-->
<!--            :label="dict.label"-->
<!--            :value="dict.value"-->
<!--          />-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="车辆分组" prop="carGroupId">-->
<!--        <el-select v-model="formData.carGroupId" placeholder="请选择车辆分组">-->
<!--          <el-option-->
<!--            v-for="item in carGroupList"-->
<!--            :key="item.carGroupId"-->
<!--            :label="item.groupName"-->
<!--            :value="item.carGroupId"-->
<!--          />-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="收费标准" prop="chargeId">-->
<!--        <el-radio-group v-model="formData.chargeId">-->
<!--          <el-radio-->
<!--            v-for="dict in getIntDictOptions(DICT_TYPE.CAR_CHARGE)"-->
<!--            :key="dict.value"-->
<!--            :label="dict.value"-->
<!--          >-->
<!--            {{ dict.label }}-->
<!--          </el-radio>-->
<!--        </el-radio-group>-->
<!--      </el-form-item>-->
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { BusCarApi, BusCarVO } from '@/api/vehicle/buscar'
import { CarGroupApi } from '@/api/vehicle/cargroup'

/** 车辆管理 表单 */
defineOptions({ name: 'BusCarForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  carId: undefined,
  carNo: undefined,
  carType: undefined,
  carGroupId: undefined,
  chargeId: undefined
})

// 车辆分组列表
const carGroupList = ref<Array<{ carGroupId: number; groupName: string }>>([])

/** 检查车牌号 */
const checkCarNo = (rule: any, value: string, callback: any) => {
  if (value.length !== 8) {
    callback('车牌号长度必须为8位')
    return
  }
  
  // 调用后端接口检查车牌号
  BusCarApi.checkCarNo(value).then(res => {
    if (res === '0') {
      callback('未查询到车牌')
    } else {
      callback()
    }
  }).catch(() => {
    callback('车牌号检查异常')
  })
}

// 更新表单验证规则
const formRules = reactive({
  carNo: [
    { required: true, message: '绑定的车辆号不能为空', trigger: 'blur' }
    // ,{ validator: checkCarNo, trigger: 'blur' }
  ],
  carType: [{ required: true, message: '车辆类型不能为空', trigger: 'change' }],
  carGroupId: [{ required: true, message: '车辆分组不能为空', trigger: 'change' }],
  chargeId: [{ required: true, message: '收费标准不能为空', trigger: 'blur' }]
})

const formRef = ref() // 表单 Ref

/** 获取车辆分组列表 */
const getCarGroupList = async () => {
  try {
    const data = await CarGroupApi.getCarGroupPage({ pageNo: 1, pageSize: 100 })
    carGroupList.value = data.list || []
  } catch (error) {
    console.error('获取车辆分组列表失败:', error)
    carGroupList.value = []
  }
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  
  // 获取车辆分组列表
  await getCarGroupList()
  
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const data = await BusCarApi.getBusCar(id)
      // 只保留表单需要的字段，避免将不需要的字段发送到后端
      formData.value = {
        carId: data.carId, // 保留ID用于更新
        carNo: data.carNo,
        carType: data.carType,
        carGroupId: data.carGroupId,
        chargeId: data.chargeId
      }
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 定义 success 事件 */
const emit = defineEmits(['success'])

/** 处理API响应 */
const handleApiResponse = (res: any, type: 'create' | 'update') => {
  if (res ) {
    message.success(res.msg || t(`common.${type}Success`))
    dialogVisible.value = false
    emit('success')
  } else {
    message.error(res.msg || t(`common.${type}Fail`))
  }
}

/** 提交表单 */
const submitForm = async () => {
  await formRef.value.validate()
  formLoading.value = true
  try {
    const data = formData.value as unknown as BusCarVO
    const res = formType.value === 'create' 
      ? await BusCarApi.createBusCar(data)
      : await BusCarApi.updateBusCar(data)
    
    handleApiResponse(res, formType.value as 'create' | 'update')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    carId: undefined,
    carNo: undefined,
    carType: undefined,
    carGroupId: undefined,
    chargeId: undefined
  }
  formRef.value?.resetFields()
}
</script>
