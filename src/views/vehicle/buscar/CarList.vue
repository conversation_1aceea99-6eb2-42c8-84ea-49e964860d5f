<template>
  <el-drawer
    v-model="visible"
    title="换电记录"
    size="80%"
    :destroy-on-close="true"
  >
    <div class="car-list-content">
      <el-descriptions :column="6" v-if="drawerVisible">
        <el-descriptions-item label="换电车牌号：">
          {{ queryParam.carNo }}
        </el-descriptions-item>
        <el-descriptions-item label="最大电池包数：">
          {{ batteryCtn }}
        </el-descriptions-item>
      </el-descriptions>
      
      <el-table
        ref="tableRef"
        :data="tableData || []"
        row-key="orderId"
        @success="handleSuccess"
        :empty-text="'暂无数据'"
        :show-header="true"
        :show-overflow-tooltip="true"
      >
        <el-table-column type="index" label="#" width="60" />
        <el-table-column prop="orderId" label="换电订单" align="center" />
        <el-table-column prop="startTime" label="换电时间" align="center" />
        <el-table-column prop="siteName" label="换电站点" align="center">
          <template #default="{ row }">
            <el-tooltip :content="row.siteName" placement="top">
              <span>{{ row.siteName }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="beforeSocNum" label="换电前整车SOC" align="center">
          <template #default="{ row }">
            <span>{{ row.beforeSocNum }}%</span>
          </template>
        </el-table-column>
        <el-table-column prop="afterSocNum" label="换电后整车SOC" align="center">
          <template #default="{ row }">
            <span>{{ row.afterSocNum }}%</span>
          </template>
        </el-table-column>
        <el-table-column prop="beforeBatteriesList" label="换电前电池包列表信息(条码和SOC)" align="center">
          <template #default="{ row }">
            <el-table
              :data="getBatteriesList(row.beforeBatteriesList)"
              :columns="batteryColumns"
              size="small"
              style="width: 100%"
              :show-header="true"
              :border="true"
            >
              <el-table-column prop="no" label="电池包条码" align="center" />
              <el-table-column prop="soc" label="SOC" align="center" />
            </el-table>
          </template>
        </el-table-column>
        <el-table-column prop="afterBatteriesList" label="换电后电池包列表信息(条码和SOC)" align="center">
          <template #default="{ row }">
            <el-table
              :data="getBatteriesList(row.afterBatteriesList)"
              :columns="batteryColumns"
              size="small"
              style="width: 100%"
              :show-header="true"
              :border="true"
            >
              <el-table-column prop="no" label="电池包条码" align="center" />
              <el-table-column prop="soc" label="SOC" align="center" />
            </el-table>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 添加分页组件 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParam.pageNum"
          v-model:page-size="queryParam.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import type { TableInstance } from 'element-plus'
import { BusCarApi } from '@/api/vehicle/buscar'

const visible = ref(false)
const drawerVisible = ref(false)
const tableRef = ref<TableInstance>()
const batteryCtn = ref('')
const tableData = ref([])
const total = ref(0)
const queryParam = ref({
  carNo: '',
  pageNum: 1,
  pageSize: 10
})

const batteryColumns = [
  {
    prop: 'no',
    label: '电池包条码',
    align: 'center'
  },
  {
    prop: 'soc',
    label: 'SOC',
    align: 'center'
  }
]

const columns = [
  {
    prop: 'index',
    label: '#',
    slot: 'index',
    width: 60
  },
  {
    prop: 'orderChargeId',
    label: '换电订单',
    align: 'center'
  },
  {
    prop: 'getInTime',
    label: '换电时间',
    align: 'center'
  },
  {
    prop: 'siteName',
    label: '换电站点',
    slot: 'text',
    align: 'center'
  },
  {
    prop: 'beforeSocNum',
    label: '换电前整车SOC',
    slot: 'proportion',
    align: 'center'
  },
  {
    prop: 'afterSocNum',
    label: '换电后整车SOC',
    slot: 'proportion',
    align: 'center'
  },
  {
    prop: 'beforeBatteriesList',
    label: '换电前电池包列表信息(条码和SOC)',
    slot: 'batteriesList',
    align: 'center'
  },
  {
    prop: 'afterBatteriesList',
    label: '换电后电池包列表信息(条码和SOC)',
    slot: 'batteriesList',
    align: 'center'
  }
]

const getBatteriesList = (text: string) => {
  if (!text) return []
  // 去除首尾引号
  if (text.startsWith('"') && text.endsWith('"')) {
    text = text.slice(1, -1)
  }
  return text.split(';')
    .filter(Boolean)
    .map(item => {
      const arr = item.split(',')
      // 兼容新格式：1,条码,SOC,未知
      return {
        no: arr[1] || '',
        soc: arr[2] ? `${arr[2]}%` : ''
      }
    })
}

const handleSuccess = (data: any) => {
  // 取第一个的最大电池包数
  batteryCtn.value = data.list?.[0]?.maxBatteries || ''
}

const handleSizeChange = (val: number) => {
  queryParam.value.pageSize = val
  fetchData()
}

const handleCurrentChange = (val: number) => {
  queryParam.value.pageNum = val
  fetchData()
}

const fetchData = async () => {
  if (!queryParam.value.carNo) return
  try {
    const res = await BusCarApi.getBatteryRecord(queryParam.value)
    console.log(res)
    tableData.value = res.list || []
    total.value = res.total || 0
    handleSuccess(res)
  } catch (error) {
    console.error('获取换电记录失败:', error)
  }
}

const open = async (carNo?: string) => {
  visible.value = true
  drawerVisible.value = true
  if (carNo) {
    queryParam.value.carNo = carNo
    queryParam.value.pageNum = 1  // 重置页码
    await fetchData()
  }
}

defineExpose({
  open
})
</script>

<style scoped>
.car-list-content {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>

