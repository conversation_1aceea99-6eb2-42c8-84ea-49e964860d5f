import { DiyComponent } from '@/components/DiyEditor/util'

/** 谷歌地图属性 */
export interface GoogleMapProperty {
  // 标题
  title: string
  // 位置描述
  locationDesc: string
  // 经度坐标
  lng: number
  // 纬度坐标
  lat: number
  // 缩放级别
  zoom: number
  // 地图高度
  height: number
  // 是否显示标题
  showTitle: boolean
  // 是否显示位置描述
  showLocationDesc: boolean
  // 样式设置
  style: {
    // 背景颜色
    bgColor: string
    // 标题颜色
    titleColor: string
    // 位置描述颜色
    descColor: string
    // 圆角
    borderRadius: number
  }
  // 换电站相关配置
  showStations: boolean           // 是否显示换电站
  stationIcon: string            // 站点图标URL（兼容旧版本）
  autoCenter: boolean            // 是否自动居中到站点
  showStationInfo: boolean       // 是否显示站点信息弹窗
  stationRadius: number          // 站点搜索半径(km)
  currentLocation: {             // 当前位置
    lat: number
    lng: number
  }
  // 站点图标配置
  stationIcons: {
    online: string               // 营业中状态的图标URL
    offline: string              // 暂停营业状态的图标URL
    busy: string                 // 繁忙状态的图标URL
  }
  stationIconSize: {             // 图标大小
    width: number
    height: number
  }
  stationIconAnchor: {           // 图标锚点位置（相对于图标中心）
    x: number
    y: number
  }
}

// 定义组件
export const component = {
  id: 'GoogleMap',
  name: '谷歌地图',
  icon: 'mdi:map-marker',
  property: {
    title: '我们的位置',
    locationDesc: '上海市浦东新区陆家嘴',
    lng: 121.5,
    lat: 31.23,
    zoom: 15,
    height: 300,
    showTitle: true,
    showLocationDesc: true,
    style: {
      bgColor: '#ffffff',
      titleColor: '#333333',
      descColor: '#666666',
      borderRadius: 8
    },
    // 换电站默认配置
    showStations: false,
    stationIcon: 'https://maps.google.com/mapfiles/ms/icons/red-dot.png',
    autoCenter: true,
    showStationInfo: true,
    stationRadius: 10,
    currentLocation: {
      lat: 31.23,
      lng: 121.5
    },
    // 站点图标配置
    stationIcons: {
      online: 'https://maps.google.com/mapfiles/ms/icons/green-dot.png',    // 营业中 - 绿色
      offline: 'https://maps.google.com/mapfiles/ms/icons/red-dot.png',     // 暂停营业 - 红色
      busy: 'https://maps.google.com/mapfiles/ms/icons/yellow-dot.png'      // 繁忙 - 黄色
    },
    stationIconSize: {
      width: 32,
      height: 32
    },
    stationIconAnchor: {
      x: 16,  // 图标宽度的一半
      y: 32   // 图标高度（图标底部对齐到坐标点）
    }
  }
} as DiyComponent<GoogleMapProperty> 