<template>
  <div>
    <el-form label-width="100px" :model="formData">
      <el-form-item label="标题" prop="title">
        <el-input v-model="formData.title" placeholder="请输入标题" />
      </el-form-item>
      
      <el-form-item label="显示标题" prop="showTitle">
        <el-switch v-model="formData.showTitle" />
      </el-form-item>
      
      <el-form-item label="位置描述" prop="locationDesc">
        <el-input v-model="formData.locationDesc" placeholder="请输入位置描述" />
      </el-form-item>
      
      <el-form-item label="显示描述" prop="showLocationDesc">
        <el-switch v-model="formData.showLocationDesc" />
      </el-form-item>
      
      <el-form-item label="经度" prop="lng">
        <el-input-number v-model="formData.lng" :precision="6" :step="0.000001" style="width: 100%" />
      </el-form-item>
      
      <el-form-item label="纬度" prop="lat">
        <el-input-number v-model="formData.lat" :precision="6" :step="0.000001" style="width: 100%" />
      </el-form-item>
      
      <el-form-item label="缩放级别" prop="zoom">
        <el-slider v-model="formData.zoom" :min="1" :max="20" show-input />
      </el-form-item>
      
      <el-form-item label="地图高度" prop="height">
        <el-slider v-model="formData.height" :min="100" :max="600" :step="10" show-input />
      </el-form-item>
      
      <el-divider>样式设置</el-divider>
      
      <el-form-item label="背景颜色">
        <el-input v-model="formData.style.bgColor" placeholder="请输入颜色值" />
      </el-form-item>
      
      <el-form-item label="标题颜色">
        <el-input v-model="formData.style.titleColor" placeholder="请输入颜色值" />
      </el-form-item>
      
      <el-form-item label="描述颜色">
        <el-input v-model="formData.style.descColor" placeholder="请输入颜色值" />
      </el-form-item>
      
      <el-form-item label="圆角" prop="style.borderRadius">
        <el-slider 
          v-model="formData.style.borderRadius" 
          :min="0" 
          :max="20" 
          show-input 
        />
      </el-form-item>
      
      <el-divider>换电站设置</el-divider>
      
      <el-form-item label="显示换电站" prop="showStations">
        <el-switch v-model="formData.showStations" />
      </el-form-item>
      
      <!-- 站点图标配置 -->
      <template v-if="formData.showStations && formData.stationIcons">
        <el-divider>站点图标设置</el-divider>
        
        <el-form-item label="营业中图标" prop="stationIcons.online">
          <div class="icon-upload-container">
            <el-input v-model="formData.stationIcons.online" placeholder="请输入图标URL或上传图片" />
            <UploadImg
              v-model="formData.stationIcons.online"
              :directory="'google-map-station-icons'"
              height="60px"
              width="80px"
              class="min-w-80px"
            >
              <template #tip>建议尺寸32x32</template>
            </UploadImg>
          </div>
        </el-form-item>
        
        <el-form-item label="暂停营业图标" prop="stationIcons.offline">
          <div class="icon-upload-container">
            <el-input v-model="formData.stationIcons.offline" placeholder="请输入图标URL或上传图片" />
            <UploadImg
              v-model="formData.stationIcons.offline"
              :directory="'google-map-station-icons'"
              height="60px"
              width="80px"
              class="min-w-80px"
            >
              <template #tip>建议尺寸32x32</template>
            </UploadImg>
          </div>
        </el-form-item>
        
        <el-form-item label="繁忙状态图标" prop="stationIcons.busy">
          <div class="icon-upload-container">
            <el-input v-model="formData.stationIcons.busy" placeholder="请输入图标URL或上传图片" />
            <UploadImg
              v-model="formData.stationIcons.busy"
              :directory="'google-map-station-icons'"
              height="60px"
              width="80px"
              class="min-w-80px"
            >
              <template #tip>建议尺寸32x32</template>
            </UploadImg>
          </div>
        </el-form-item>
        
        <el-form-item label="图标大小" v-if="formData.stationIconSize">
          <div class="size-controls">
            <el-form-item label="宽度" prop="stationIconSize.width" label-width="60px">
              <el-input-number 
                v-model="formData.stationIconSize.width" 
                :min="16" 
                :max="64" 
                :step="2" 
                style="width: 100px" 
              />
            </el-form-item>
            <el-form-item label="高度" prop="stationIconSize.height" label-width="60px">
              <el-input-number 
                v-model="formData.stationIconSize.height" 
                :min="16" 
                :max="64" 
                :step="2" 
                style="width: 100px" 
              />
            </el-form-item>
          </div>
        </el-form-item>
        
        <el-form-item label="图标锚点" v-if="formData.stationIconAnchor">
          <div class="anchor-controls">
            <el-form-item label="X偏移" prop="stationIconAnchor.x" label-width="60px">
              <el-input-number 
                v-model="formData.stationIconAnchor.x" 
                :min="0" 
                :max="64" 
                :step="1" 
                style="width: 100px" 
              />
            </el-form-item>
            <el-form-item label="Y偏移" prop="stationIconAnchor.y" label-width="60px">
              <el-input-number 
                v-model="formData.stationIconAnchor.y" 
                :min="0" 
                :max="64" 
                :step="1" 
                style="width: 100px" 
              />
            </el-form-item>
          </div>
          <div class="anchor-tip">
            <small>锚点位置：X为水平偏移，Y为垂直偏移。通常X设为图标宽度的一半，Y设为图标高度。</small>
          </div>
        </el-form-item>
      </template>
      
      <el-form-item label="自动居中" prop="autoCenter" v-if="formData.showStations">
        <el-switch v-model="formData.autoCenter" />
      </el-form-item>
      
      <el-form-item label="显示站点信息" prop="showStationInfo" v-if="formData.showStations">
        <el-switch v-model="formData.showStationInfo" />
      </el-form-item>
      
      <el-form-item label="搜索半径(km)" prop="stationRadius" v-if="formData.showStations">
        <el-slider v-model="formData.stationRadius" :min="1" :max="50" :step="1" show-input />
      </el-form-item>
      
      <el-form-item label="当前位置经度" prop="currentLocation.lng" v-if="formData.showStations">
        <el-input-number v-model="formData.lng" :precision="6" :step="0.000001" style="width: 100%" />
      </el-form-item>
      
      <el-form-item label="当前位置纬度" prop="currentLocation.lat" v-if="formData.showStations">
        <el-input-number v-model="formData.lat" :precision="6" :step="0.000001" style="width: 100%" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { GoogleMapProperty } from './config'
import { useVModel } from '@vueuse/core'
import { onMounted } from 'vue'
import UploadImg from '@/components/UploadFile/src/UploadImg.vue'

// 谷歌地图属性面板
defineOptions({ name: 'GoogleMapProperty' })
const props = defineProps<{ modelValue: GoogleMapProperty }>()
const emit = defineEmits(['update:modelValue'])
const formData = useVModel(props, 'modelValue', emit)

// 初始化缺失的配置项
const initializeMissingProperties = () => {
  // 确保stationIcons对象存在
  if (!formData.value.stationIcons) {
    formData.value.stationIcons = {
      online: 'https://maps.google.com/mapfiles/ms/icons/green-dot.png',
      offline: 'https://maps.google.com/mapfiles/ms/icons/red-dot.png',
      busy: 'https://maps.google.com/mapfiles/ms/icons/yellow-dot.png'
    }
  }
  
  // 确保stationIconSize对象存在
  if (!formData.value.stationIconSize) {
    formData.value.stationIconSize = {
      width: 32,
      height: 32
    }
  }
  
  // 确保stationIconAnchor对象存在
  if (!formData.value.stationIconAnchor) {
    formData.value.stationIconAnchor = {
      x: 16,
      y: 32
    }
  }
}

// 组件挂载时初始化
onMounted(() => {
  initializeMissingProperties()
})
</script>

<style scoped lang="scss">
.icon-upload-container {
  display: flex;
  gap: 8px;
  align-items: flex-start;
  
  .el-input {
    flex: 1;
  }
}

.size-controls,
.anchor-controls {
  display: flex;
  gap: 16px;
  align-items: flex-start;
  
  .el-form-item {
    margin-bottom: 0;
  }
}

.anchor-tip {
  margin-top: 8px;
  color: #909399;
  font-size: 12px;
  line-height: 1.4;
}
</style>
