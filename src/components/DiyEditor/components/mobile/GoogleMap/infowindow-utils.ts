/**
 * Google Maps InfoWindow 工具函数
 * 基于Google Maps API最佳实践实现原生POI风格的信息弹窗
 */

// 站点信息接口
export interface StationInfo {
  siteNo: string
  siteName: string
  address: string
  latitude: number
  longitude: number
  status: number
  distance?: number
  replaceCarCtn?: number
  nowPrice?: number
  operatingHours?: string
  contactPhone?: string
}

// InfoWindow配置选项
export interface InfoWindowOptions {
  maxWidth?: number
  pixelOffset?: google.maps.Size
  zIndex?: number
  closeBoxMargin?: string
  closeBoxURL?: string
  infoBoxClearance?: google.maps.Size
  isHidden?: boolean
  pane?: string
  enableEventPropagation?: boolean
}

/**
 * 创建原生风格的InfoWindow内容
 * 参考Google Maps POI信息展示风格
 */
export const createInfoWindowContent = (station: StationInfo): string => {
  const statusText = getStatusText(station.status)
  const statusColor = getStatusColor(station.status)
  
  return `
    <div class="station-info-window">
      <div class="station-header">
        <h3 class="station-name">${station.siteName}</h3>
        <div class="station-status" style="color: ${statusColor};">
          <span class="status-dot" style="background-color: ${statusColor};"></span>
          ${statusText}
        </div>
      </div>
      
      <div class="station-details">
        <div class="detail-item">
          <span class="detail-icon">📍</span>
          <span class="detail-text">${station.address}</span>
        </div>
        
        ${station.distance ? `
        <div class="detail-item">
          <span class="detail-icon">📏</span>
          <span class="detail-text">距离 ${station.distance}m</span>
        </div>
        ` : ''}
        
        ${station.replaceCarCtn !== undefined ? `
        <div class="detail-item">
          <span class="detail-icon">🔋</span>
          <span class="detail-text">可用电池 ${station.replaceCarCtn}个</span>
        </div>
        ` : ''}
        
        ${station.nowPrice ? `
        <div class="detail-item">
          <span class="detail-icon">💰</span>
          <span class="detail-text">当前电价 ¥${station.nowPrice}/kWh</span>
        </div>
        ` : ''}
        
        ${station.operatingHours ? `
        <div class="detail-item">
          <span class="detail-icon">🕒</span>
          <span class="detail-text">${station.operatingHours}</span>
        </div>
        ` : ''}
        
        ${station.contactPhone ? `
        <div class="detail-item">
          <span class="detail-icon">📞</span>
          <span class="detail-text">${station.contactPhone}</span>
        </div>
        ` : ''}
      </div>
      
      <div class="station-actions">
        <button class="action-btn primary" onclick="navigateToStation(${station.latitude}, ${station.longitude})">
          <span class="btn-icon">🧭</span>
          导航
        </button>
        <button class="action-btn secondary" onclick="showStationDetails('${station.siteNo}')">
          <span class="btn-icon">ℹ️</span>
          详情
        </button>
      </div>
    </div>
  `
}

/**
 * 获取状态文本
 */
export const getStatusText = (status: number): string => {
  switch (status) {
    case 1: return '正常运营'
    case 2: return '维护中'
    case 3: return '故障'
    default: return '未知状态'
  }
}

/**
 * 获取状态颜色
 */
export const getStatusColor = (status: number): string => {
  switch (status) {
    case 1: return '#4CAF50' // 绿色
    case 2: return '#FF9800' // 橙色
    case 3: return '#F44336' // 红色
    default: return '#9E9E9E' // 灰色
  }
}

/**
 * 创建原生InfoWindow实例
 * 使用Google Maps API最佳实践
 */
export const createInfoWindow = (
  station: StationInfo, 
  options: InfoWindowOptions = {}
): google.maps.InfoWindow => {
  const defaultOptions: InfoWindowOptions = {
    maxWidth: 320,
    pixelOffset: new google.maps.Size(0, -10),
    zIndex: 1000,
    closeBoxMargin: '8px',
    enableEventPropagation: false
  }
  
  const finalOptions = { ...defaultOptions, ...options }
  
  const infoWindow = new google.maps.InfoWindow({
    content: createInfoWindowContent(station),
    maxWidth: finalOptions.maxWidth,
    pixelOffset: finalOptions.pixelOffset,
    zIndex: finalOptions.zIndex
  })
  
  return infoWindow
}

/**
 * InfoWindow样式CSS
 * 模仿Google Maps原生POI样式
 */
export const getInfoWindowStyles = (): string => {
  return `
    <style>
      .station-info-window {
        font-family: 'Roboto', 'Arial', sans-serif;
        font-size: 14px;
        line-height: 1.4;
        color: #333;
        max-width: 300px;
        padding: 0;
        margin: 0;
      }
      
      .station-header {
        padding: 12px 16px 8px;
        border-bottom: 1px solid #e0e0e0;
      }
      
      .station-name {
        margin: 0 0 4px 0;
        font-size: 16px;
        font-weight: 500;
        color: #1a73e8;
        line-height: 1.3;
      }
      
      .station-status {
        display: flex;
        align-items: center;
        font-size: 12px;
        font-weight: 400;
      }
      
      .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 6px;
        display: inline-block;
      }
      
      .station-details {
        padding: 8px 16px;
      }
      
      .detail-item {
        display: flex;
        align-items: center;
        margin: 4px 0;
        font-size: 13px;
      }
      
      .detail-icon {
        width: 16px;
        margin-right: 8px;
        text-align: center;
        font-size: 12px;
      }
      
      .detail-text {
        flex: 1;
        color: #5f6368;
      }
      
      .station-actions {
        padding: 8px 16px 12px;
        display: flex;
        gap: 8px;
        border-top: 1px solid #e0e0e0;
      }
      
      .action-btn {
        flex: 1;
        padding: 8px 12px;
        border: 1px solid #dadce0;
        border-radius: 4px;
        background: #fff;
        color: #1a73e8;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
      }
      
      .action-btn:hover {
        background: #f8f9fa;
        border-color: #1a73e8;
      }
      
      .action-btn.primary {
        background: #1a73e8;
        color: #fff;
        border-color: #1a73e8;
      }
      
      .action-btn.primary:hover {
        background: #1557b0;
      }
      
      .btn-icon {
        margin-right: 4px;
        font-size: 12px;
      }
      
      /* 移除默认的InfoWindow样式 */
      .gm-style .gm-style-iw-c {
        padding: 0;
      }
      
      .gm-style .gm-style-iw-d {
        overflow: hidden !important;
      }
      
      .gm-style .gm-style-iw-t::after {
        background: linear-gradient(45deg, #fff 50%, transparent 50%);
      }
    </style>
  `
}

/**
 * 全局函数：导航到站点
 */
declare global {
  interface Window {
    navigateToStation: (lat: number, lng: number) => void
    showStationDetails: (siteNo: string) => void
  }
}

// 导航到站点的全局函数
window.navigateToStation = (lat: number, lng: number) => {
  const url = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`
  window.open(url, '_blank')
}

// 显示站点详情的全局函数
window.showStationDetails = (siteNo: string) => {
  // 这里可以触发自定义事件或调用父组件方法
  console.log('Show station details:', siteNo)
  // 可以通过事件总线或其他方式通知父组件
}
