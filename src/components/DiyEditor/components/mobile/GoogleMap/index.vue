<template>
  <div
    class="google-map-container"
    :style="{
      backgroundColor: property.style.bgColor,
      borderRadius: property.style.borderRadius + 'px'
    }"
  >
    <!-- 标题和工具栏同行 -->
    <div v-if="property.showTitle" class="map-header">
      <div class="map-title" :style="{ color: property.style.titleColor }">
        {{ property.title }}
      </div>
      <button class="fullscreen-btn" @click="openFullscreen">
        <el-icon size="16"><FullScreen /></el-icon>
        <span class="btn-text">全屏</span>
      </button>
    </div>

    <!-- 如果没有标题但需要显示全屏按钮 -->
    <div v-if="!property.showTitle" class="map-toolbar">
      <button class="fullscreen-btn" @click="openFullscreen">
        <el-icon size="16"><FullScreen /></el-icon>
        <span class="btn-text">全屏</span>
      </button>
    </div>

    <div v-if="property.showLocationDesc" class="map-desc" :style="{ color: property.style.descColor }">
      {{ property.locationDesc }}
    </div>

    <div class="map-container" :style="{ height: property.height + 'px' }" ref="mapRef"></div>

    <!-- 站点信息弹窗 -->
    <el-dialog
      v-model="stationInfoVisible"
      title="站点详情"
      width="400px"
      :show-close="true"
    >
      <div v-if="selectedStation" class="station-info">
        <div class="station-name">{{ selectedStation.siteName }}</div>
        <div class="station-address">{{ selectedStation.address }}</div>
        <div class="station-status">
          <span class="status-label">状态：</span>
          <span :class="['status-value', selectedStation.status === 1 ? 'online' : 'offline']">
            {{ selectedStation.status === 1 ? '营业中' : '暂停营业' }}
          </span>
        </div>
        <div class="station-distance" v-if="selectedStation.distance">
          <span class="distance-label">距离：</span>
          <span class="distance-value">{{ selectedStation.distance }}km</span>
        </div>
      </div>
      <template #footer>
        <el-button @click="stationInfoVisible = false">关闭</el-button>
        <el-button type="primary" @click="navigateToStation">导航</el-button>
      </template>
    </el-dialog>

    <!-- 全屏地图对话框 -->
    <el-dialog
      v-model="fullscreenVisible"
      title="地图全览"
      :width="'90vw'"
      :top="'5vh'"
      :show-close="true"
      :close-on-click-modal="false"
      @opened="initFullscreenMap"
      @closed="destroyFullscreenMap"
      class="fullscreen-dialog"
    >
      <div class="fullscreen-map-wrapper">
        <div ref="fullscreenMapRef" class="fullscreen-map"></div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { GoogleMapProperty } from './config'
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import { FullScreen } from '@element-plus/icons-vue'
import { MarkerClusterer, SuperClusterAlgorithm } from '@googlemaps/markerclusterer'

/** 谷歌地图组件 */
defineOptions({ name: 'GoogleMap' })

const props = defineProps<{ property: GoogleMapProperty }>()
const mapRef = ref<HTMLElement | null>(null)
const fullscreenMapRef = ref<HTMLElement | null>(null)
let map: any = null
let marker: any = null
let googleMapScript: HTMLScriptElement | null = null

// 换电站相关状态
const stationInfoVisible = ref(false)
const selectedStation = ref<any>(null)
const stationMarkers: any[] = []
let markerClusterer: MarkerClusterer | null = null

// 全屏相关状态
const fullscreenVisible = ref(false)
let fullscreenMap: any = null
const fullscreenStationMarkers: any[] = []
let fullscreenMarkerClusterer: MarkerClusterer | null = null

// 创建专业的换电站图标
const createStationIcon = (station: any) => {
  const iconElement = document.createElement('div');
  iconElement.className = 'station-marker';
  iconElement.style.cursor = 'pointer';
  iconElement.title = station.siteName;

  // 优先使用配置的图标
  if (props.property.stationIcon) {
    iconElement.innerHTML = `<img src="${props.property.stationIcon}" width="32" height="32" alt="${station.siteName}">`;
  } else {
    // 备用方案：根据状态生成SVG图标
    const getIconColor = (status: number) => {
      switch (status) {
        case 1: return '#4CAF50'; // 正常运营
        case 2: return '#FF9800'; // 维护中
        case 3: return '#F44336'; // 故障
        default: return '#9E9E9E'; // 未知状态
      }
    };
    iconElement.innerHTML = `
      <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
        <circle cx="16" cy="16" r="14" fill="${getIconColor(station.status)}" stroke="#fff" stroke-width="2"/>
        <path d="M12 10h8v4h-2v6h-4v-6h-2z" fill="#fff"/>
        <circle cx="16" cy="22" r="2" fill="#fff"/>
      </svg>
    `;
  }
  return iconElement;
}


// 创建聚合图标
const createClusterIcon = (count: number) => {
  const clusterElement = document.createElement('div')
  clusterElement.className = 'cluster-marker'
  clusterElement.innerHTML = `
    <div style="
      background: #1976D2;
      color: white;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 14px;
      border: 2px solid white;
      box-shadow: 0 2px 6px rgba(0,0,0,0.3);
      cursor: pointer;
    ">${count}</div>
  `
  return clusterElement
}

// 获取站点列表
const getStationList = async () => {
  try {
    // 在管理后台中，我们使用模拟数据来展示效果
    // 实际在uni-app中会调用真实的API
    return [
      {
        siteNo: 'ST001',
        siteName: '陆家嘴换电站',
        address: '上海市浦东新区陆家嘴环路1000号',
        latitude: 31.23,
        longitude: 121.5,
        status: 1,
        distance: 1200,
        city: '上海',
        enabled: true,
        replaceCarCtn: 15,
        lowPrice: 0.8,
        normalPrice: 1.2,
        peakPrice: 1.8,
        nowPrice: 1.2
      },
      {
        siteNo: 'ST002', 
        siteName: '外滩换电站',
        address: '上海市黄浦区中山东一路1号',
        latitude: 31.24,
        longitude: 121.49,
        status: 2,
        distance: 2500,
        city: '上海',
        enabled: true,
        replaceCarCtn: 8,
        lowPrice: 0.9,
        normalPrice: 1.3,
        peakPrice: 1.9,
        nowPrice: 1.3
      },
      {
        siteNo: 'ST003',
        siteName: '南京路换电站', 
        address: '上海市黄浦区南京东路123号',
        latitude: 31.22,
        longitude: 121.48,
        status: 0,
        distance: 3800,
        city: '上海',
        enabled: false,
        replaceCarCtn: 0,
        lowPrice: 0.7,
        normalPrice: 1.1,
        peakPrice: 1.7,
        nowPrice: 1.1
      },
      {
        siteNo: 'ST004',
        siteName: '静安寺换电站',
        address: '上海市静安区南京西路1686号',
        latitude: 31.22,
        longitude: 121.46,
        status: 1,
        distance: 1800,
        city: '上海',
        enabled: true,
        replaceCarCtn: 12,
        lowPrice: 0.85,
        normalPrice: 1.25,
        peakPrice: 1.85,
        nowPrice: 1.25
      },
      {
        siteNo: 'ST005',
        siteName: '虹桥商务区换电站',
        address: '上海市长宁区虹桥路1号',
        latitude: 31.20,
        longitude: 121.40,
        status: 1,
        distance: 3200,
        city: '上海',
        enabled: true,
        replaceCarCtn: 20,
        lowPrice: 0.75,
        normalPrice: 1.15,
        peakPrice: 1.75,
        nowPrice: 1.15
      }
    ]
  } catch (error) {
    console.error('获取站点列表失败:', error)
    return []
  }
}

// 加载谷歌地图API
const loadGoogleMapAPI = () => {
  return new Promise<void>((resolve, reject) => {
    try {
      // 检查是否已经加载过
      if (window.google && window.google.maps) {
        resolve()
        return
      }

      // 创建script标签
      googleMapScript = document.createElement('script')
      googleMapScript.src = `https://maps.googleapis.com/maps/api/js?key=AIzaSyArla_C7JZ6kpEFq9ojEK4YRFg6h8uhoQA&libraries=marker,geometry&callback=initGoogleMap`
      googleMapScript.async = true
      googleMapScript.defer = true

      // 在window上定义回调函数
      window.initGoogleMap = () => {
        resolve()
      }

      // 处理加载失败
      googleMapScript.onerror = () => {
        reject(new Error('Google Maps API加载失败'))
      }

      // 添加到文档中
      document.head.appendChild(googleMapScript)
    } catch (error) {
      reject(error)
    }
  })
}

// 初始化地图
const initMap = () => {
  if (!mapRef.value || !window.google?.maps) return

  // 创建地图
  map = new window.google.maps.Map(mapRef.value, {
    center: { lat: props.property.lat, lng: props.property.lng },
    zoom: props.property.zoom,
    disableDefaultUI: true,  // 启用默认UI控件
    zoomControl: true,        // 缩放控件
    mapTypeControl: true,     // 地图类型控件（卫星、地形等）
    streetViewControl: true, // 街景控件
    fullscreenControl: true, // 全屏控件
    rotateControl: true,      // 旋转控件
    scaleControl: true,       // 比例尺控件
    mapId: 'DEMO_MAP_ID'
  })

  // 移除默认标记，只显示换电站图标
  // marker = new window.google.maps.Marker({
  //   position: { lat: props.property.lat, lng: props.property.lng },
  //   map: map,
  //   title: props.property.locationDesc,
  //   icon: {
  //     url: 'https://maps.google.com/mapfiles/ms/icons/red-dot.png',
  //     scaledSize: new window.google.maps.Size(32, 32),
  //     anchor: new window.google.maps.Point(16, 32)
  //   }
  // })

  // 移除缩放事件监听器 - 使用现代API不需要手动调整标记大小

  // 如果启用换电站显示，加载站点数据
  if (props.property.showStations) {
    loadStationMarkers()
  }
}

// 加载站点标记
const loadStationMarkers = async () => {
  try {
    const stations = await getStationList()
    renderStationMarkers(stations)
  } catch (error) {
    console.error('加载站点数据失败:', error)
  }
}

// 渲染站点标记（优化版本）
const renderStationMarkers = (stations: any[]) => {
  // 清除现有标记和聚合器
  if (markerClusterer) {
    markerClusterer.clearMarkers()
    markerClusterer = null
  }
  stationMarkers.forEach(marker => marker.map = null)
  stationMarkers.length = 0

  if (!stations.length) return

  // 创建新标记
  stations.forEach(station => {
    const marker = new window.google.maps.marker.AdvancedMarkerElement({
      map: map,
      position: { lat: station.latitude, lng: station.longitude },
      title: station.siteName,
      content: createStationIcon(station)
    })

    // 添加点击事件
    marker.addListener('click', () => {
      showStationInfo(station)
    })

    stationMarkers.push(marker)
  })

  // 创建标记聚合器
  if (stationMarkers.length > 1) {
    markerClusterer = new MarkerClusterer({
      map,
      markers: stationMarkers,
      algorithm: new SuperClusterAlgorithm({
        radius: 100,
        maxZoom: 15
      }),
      renderer: {
        render: ({ count, position }) => {
          return new window.google.maps.marker.AdvancedMarkerElement({
            position,
            content: createClusterIcon(count),
            zIndex: 1000 + count
          })
        }
      }
    })
  }

  // 自动调整视野
  if (props.property.autoCenter && stations.length > 0) {
    const bounds = new window.google.maps.LatLngBounds()
    stations.forEach(station => {
      bounds.extend({ lat: station.latitude, lng: station.longitude })
    })
    map.fitBounds(bounds)
  }
}

// 获取状态文本
const getStatusText = (status: number) => {
  switch (status) {
    case 1: return '正常运营'
    case 2: return '维护中'
    case 3: return '故障'
    default: return '未知状态'
  }
}

// 显示站点信息
const showStationInfo = (station: any) => {
  if (!props.property.showStationInfo) return
  
  selectedStation.value = station
  stationInfoVisible.value = true
}

// 导航到站点
const navigateToStation = () => {
  if (!selectedStation.value) return

  const { latitude, longitude } = selectedStation.value
  const url = `https://www.google.com/maps/dir/?api=1&destination=${latitude},${longitude}`
  window.open(url, '_blank')
}

// 打开全屏模式
const openFullscreen = () => {
  fullscreenVisible.value = true
}

// 初始化全屏地图
const initFullscreenMap = async () => {
  await nextTick()
  if (!fullscreenMapRef.value || !window.google?.maps) return

  // 创建全屏地图实例
  fullscreenMap = new window.google.maps.Map(fullscreenMapRef.value, {
    center: map ? map.getCenter() : { lat: props.property.lat, lng: props.property.lng },
    zoom: map ? map.getZoom() : props.property.zoom,
    disableDefaultUI: false,
    zoomControl: true,
    fullscreenControl: true,
    streetViewControl: true,
    mapTypeControl: true,
    mapId: 'DEMO_MAP_ID'
  })

  // 复制站点标记到全屏地图
  if (props.property.showStations) {
    await loadFullscreenStationMarkers()
  }
}

// 加载全屏地图站点标记
const loadFullscreenStationMarkers = async () => {
  try {
    const stations = await getStationList()
    renderFullscreenStationMarkers(stations)
  } catch (error) {
    console.error('加载全屏地图站点数据失败:', error)
  }
}

// 渲染全屏地图站点标记
const renderFullscreenStationMarkers = (stations: any[]) => {
  // 清除现有标记和聚合器
  if (fullscreenMarkerClusterer) {
    fullscreenMarkerClusterer.clearMarkers()
    fullscreenMarkerClusterer = null
  }
  fullscreenStationMarkers.forEach(marker => marker.map = null)
  fullscreenStationMarkers.length = 0

  if (!stations.length) return

  // 创建新标记
  stations.forEach(station => {
    const marker = new window.google.maps.marker.AdvancedMarkerElement({
      map: fullscreenMap,
      position: { lat: station.latitude, lng: station.longitude },
      title: station.siteName,
      content: createStationIcon(station)
    })

    // 添加点击事件
    marker.addListener('click', () => {
      showStationInfo(station)
    })

    fullscreenStationMarkers.push(marker)
  })

  // 创建标记聚合器
  if (fullscreenStationMarkers.length > 1) {
    fullscreenMarkerClusterer = new MarkerClusterer({
      map: fullscreenMap,
      markers: fullscreenStationMarkers,
      algorithm: new SuperClusterAlgorithm({
        radius: 100,
        maxZoom: 15
      }),
      renderer: {
        render: ({ count, position }) => {
          return new window.google.maps.marker.AdvancedMarkerElement({
            position,
            content: createClusterIcon(count),
            zIndex: 1000 + count
          })
        }
      }
    })
  }

  // 自动调整视野
  if (stations.length > 0) {
    const bounds = new window.google.maps.LatLngBounds()
    stations.forEach(station => {
      bounds.extend({ lat: station.latitude, lng: station.longitude })
    })
    fullscreenMap.fitBounds(bounds)
  }
}

// 销毁全屏地图
const destroyFullscreenMap = () => {
  if (fullscreenMarkerClusterer) {
    fullscreenMarkerClusterer.clearMarkers()
    fullscreenMarkerClusterer = null
  }
  if (fullscreenMap) {
    fullscreenStationMarkers.forEach(marker => marker.map = null)
    fullscreenStationMarkers.length = 0
    fullscreenMap = null
  }
}

// 更新地图
const updateMap = () => {
  if (!map) return
  
  const latLng = { lat: props.property.lat, lng: props.property.lng }
  map.setCenter(latLng)
  map.setZoom(props.property.zoom)
  // 移除默认标记的更新
  // if (marker) {
  //   marker.setPosition(latLng)
  // }
}

// 监听属性变化
watch(
  () => [props.property.lat, props.property.lng, props.property.zoom],
  () => {
    updateMap()
  }
)

// 监听换电站配置变化
watch(
  () => props.property.showStations,
  (newVal) => {
    if (newVal && map) {
      loadStationMarkers()
    } else {
      // 清除站点标记
      if (markerClusterer) {
        markerClusterer.clearMarkers()
        markerClusterer = null
      }
      stationMarkers.forEach(marker => marker.map = null)
      stationMarkers.length = 0
    }
  }
)

onMounted(async () => {
  try {
    await loadGoogleMapAPI()
    initMap()
  } catch (error) {
    console.error('加载谷歌地图失败:', error)
  }
})

onBeforeUnmount(() => {
  // 清理资源
  if (markerClusterer) {
    markerClusterer.clearMarkers()
  }
  if (fullscreenMarkerClusterer) {
    fullscreenMarkerClusterer.clearMarkers()
  }
  if (googleMapScript) {
    document.head.removeChild(googleMapScript)
  }
})
</script>

<script lang="ts">
// 为TypeScript声明全局Google Maps变量
declare global {
  interface Window {
    google: any;
    initGoogleMap: () => void;
  }
}
</script>

<style scoped lang="scss">
.google-map-container {
  width: 100%;
  padding: 12px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 10px;
}

// 标题和按钮同行布局
.map-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  .map-title {
    font-size: 16px;
    font-weight: bold;
    margin: 0;
    flex: 1;
  }

  .fullscreen-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    color: #495057;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
    margin-left: 12px;

    &:hover {
      background: #e9ecef;
      border-color: #dee2e6;
      color: #212529;
    }

    &:active {
      background: #dee2e6;
      transform: translateY(1px);
    }

    .btn-text {
      font-size: 12px;
      font-weight: 500;
    }
  }
}

// 独立的标题样式（当没有按钮时）
.map-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
}

.map-desc {
  font-size: 14px;
  margin-bottom: 12px;
}

// 独立的工具栏样式（当没有标题时）
.map-toolbar {
  margin-bottom: 8px;
  display: flex;
  justify-content: flex-end;

  .fullscreen-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    color: #495057;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: #e9ecef;
      border-color: #dee2e6;
      color: #212529;
    }

    &:active {
      background: #dee2e6;
      transform: translateY(1px);
    }

    .btn-text {
      font-size: 12px;
      font-weight: 500;
    }
  }
}

.map-container {
  width: 100%;
  min-height: 200px;
  background-color: #f1f1f1;
}

.station-info {
  .station-name {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 8px;
    color: #333;
  }
  
  .station-address {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    line-height: 1.4;
  }
  
  .station-status {
    margin-bottom: 8px;
    
    .status-label {
      font-size: 14px;
      color: #666;
    }
    
    .status-value {
      font-size: 14px;
      font-weight: bold;
      
      &.online {
        color: #67c23a;
      }
      
      &.offline {
        color: #f56c6c;
      }
    }
  }
  
  .station-distance {
    .distance-label {
      font-size: 14px;
      color: #666;
    }

    .distance-value {
      font-size: 14px;
      color: #409eff;
      font-weight: bold;
    }
  }
}

// 全屏地图样式
:deep(.fullscreen-dialog) {
  .el-dialog__body {
    padding: 0;
  }
}

.fullscreen-map-wrapper {
  height: 70vh;
  min-height: 500px;
  width: 100%;
  position: relative;
}

.fullscreen-map {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

// 标记样式
:deep(.station-marker) {
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.1);
  }
}

:deep(.cluster-marker) {
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.1);
  }
}
</style>
