<template>
  <el-dialog
    v-model="visible"
    :title="title"
    :width="config.width"
    :top="'5vh'"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    @opened="onDialogOpened"
    @closed="onDialogClosed"
    class="fullscreen-map-dialog"
  >
    <div 
      ref="fullscreenMapRef" 
      class="fullscreen-map-container"
      :style="{ height: config.height }"
    ></div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeFullscreen">关闭</el-button>
        <el-button type="primary" @click="centerToStations" v-if="stations.length > 0">
          显示所有站点
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, nextTick, watch } from 'vue'
import type { GoogleMapProperty } from './config-optimized'
import { createInfoWindow, getInfoWindowStyles, type StationInfo } from './infowindow-utils'

interface Props {
  modelValue: boolean
  title?: string
  config: GoogleMapProperty['fullscreenConfig']
  mapConfig: GoogleMapProperty['mapControls']
  center: { lat: number; lng: number }
  zoom: number
  stations: StationInfo[]
  stationIcons: GoogleMapProperty['stationIcons']
  stationIconSize: GoogleMapProperty['stationIconSize']
  stationIconAnchor: GoogleMapProperty['stationIconAnchor']
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'station-click', station: StationInfo): void
}

const props = withDefaults(defineProps<Props>(), {
  title: '地图全览',
  stations: () => []
})

const emit = defineEmits<Emits>()

// 响应式数据
const visible = ref(false)
const fullscreenMapRef = ref<HTMLElement | null>(null)
let fullscreenMap: google.maps.Map | null = null
const fullscreenMarkers: google.maps.Marker[] = []
let currentInfoWindow: google.maps.InfoWindow | null = null

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 关闭全屏
const closeFullscreen = () => {
  visible.value = false
}

// 对话框打开时初始化地图
const onDialogOpened = async () => {
  await nextTick()
  initFullscreenMap()
}

// 对话框关闭时清理资源
const onDialogClosed = () => {
  destroyFullscreenMap()
}

// 初始化全屏地图
const initFullscreenMap = () => {
  if (!fullscreenMapRef.value || !window.google?.maps) return

  // 创建全屏地图实例
  fullscreenMap = new window.google.maps.Map(fullscreenMapRef.value, {
    center: props.center,
    zoom: props.zoom,
    disableDefaultUI: !props.config.showControls,
    zoomControl: props.mapConfig.zoomControl,
    mapTypeControl: props.mapConfig.mapTypeControl,
    streetViewControl: props.mapConfig.streetViewControl,
    fullscreenControl: props.mapConfig.fullscreenControl,
    rotateControl: props.mapConfig.rotateControl,
    scaleControl: props.mapConfig.scaleControl,
    mapId: 'FULLSCREEN_MAP_ID'
  })

  // 添加样式到页面
  addInfoWindowStyles()

  // 渲染站点标记
  if (props.stations.length > 0) {
    renderStationMarkers()
  }
}

// 添加InfoWindow样式
const addInfoWindowStyles = () => {
  const styleId = 'google-maps-infowindow-styles'
  if (!document.getElementById(styleId)) {
    const styleElement = document.createElement('style')
    styleElement.id = styleId
    styleElement.innerHTML = getInfoWindowStyles()
    document.head.appendChild(styleElement)
  }
}

// 渲染站点标记
const renderStationMarkers = () => {
  // 清除现有标记
  fullscreenMarkers.forEach(marker => marker.setMap(null))
  fullscreenMarkers.length = 0

  if (!props.stations.length || !fullscreenMap) return

  props.stations.forEach(station => {
    // 根据状态选择图标
    const iconUrl = getStationIconUrl(station.status)
    
    const marker = new window.google.maps.Marker({
      position: { lat: station.latitude, lng: station.longitude },
      map: fullscreenMap,
      title: station.siteName,
      icon: {
        url: iconUrl,
        scaledSize: new window.google.maps.Size(
          props.stationIconSize.width, 
          props.stationIconSize.height
        ),
        anchor: new window.google.maps.Point(
          props.stationIconAnchor.x, 
          props.stationIconAnchor.y
        )
      }
    })

    // 添加点击事件 - 使用原生InfoWindow
    marker.addListener('click', () => {
      // 关闭之前的InfoWindow
      if (currentInfoWindow) {
        currentInfoWindow.close()
      }

      // 创建新的InfoWindow
      currentInfoWindow = createInfoWindow(station, {
        maxWidth: 320,
        pixelOffset: new window.google.maps.Size(0, -10)
      })

      // 打开InfoWindow
      currentInfoWindow.open(fullscreenMap, marker)

      // 触发站点点击事件
      emit('station-click', station)
    })

    fullscreenMarkers.push(marker)
  })

  // 自动调整视野以显示所有站点
  centerToStations()
}

// 根据状态获取图标URL
const getStationIconUrl = (status: number): string => {
  switch (status) {
    case 1: return props.stationIcons.normal
    case 2: return props.stationIcons.maintenance
    case 3: return props.stationIcons.fault
    default: return props.stationIcons.normal
  }
}

// 居中显示所有站点
const centerToStations = () => {
  if (!fullscreenMap || props.stations.length === 0) return

  if (props.stations.length === 1) {
    // 只有一个站点时，直接居中
    const station = props.stations[0]
    fullscreenMap.setCenter({ lat: station.latitude, lng: station.longitude })
    fullscreenMap.setZoom(16)
  } else {
    // 多个站点时，调整视野以包含所有站点
    const bounds = new window.google.maps.LatLngBounds()
    props.stations.forEach(station => {
      bounds.extend({ lat: station.latitude, lng: station.longitude })
    })
    fullscreenMap.fitBounds(bounds)
  }
}

// 销毁全屏地图
const destroyFullscreenMap = () => {
  // 关闭InfoWindow
  if (currentInfoWindow) {
    currentInfoWindow.close()
    currentInfoWindow = null
  }

  // 清除标记
  fullscreenMarkers.forEach(marker => marker.setMap(null))
  fullscreenMarkers.length = 0

  // 清除地图实例
  if (fullscreenMap) {
    fullscreenMap = null
  }
}

// 监听站点数据变化
watch(() => props.stations, () => {
  if (fullscreenMap && visible.value) {
    renderStationMarkers()
  }
}, { deep: true })

// 监听地图配置变化
watch(() => [props.center, props.zoom], () => {
  if (fullscreenMap && visible.value) {
    fullscreenMap.setCenter(props.center)
    fullscreenMap.setZoom(props.zoom)
  }
}, { deep: true })
</script>

<style scoped lang="scss">
.fullscreen-map-dialog {
  :deep(.el-dialog) {
    margin: 0;
    border-radius: 8px;
    overflow: hidden;
  }

  :deep(.el-dialog__header) {
    padding: 16px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
  }

  :deep(.el-dialog__body) {
    padding: 0;
  }

  :deep(.el-dialog__footer) {
    padding: 16px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e0e0e0;
  }
}

.fullscreen-map-container {
  width: 100%;
  border-radius: 0;
  overflow: hidden;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
