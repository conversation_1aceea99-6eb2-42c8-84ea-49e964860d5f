<template>
  <div class="station-list-property">
    <!-- 基础设置 -->
    <el-card class="property-card" shadow="never">
      <template #header>
        <div class="card-header">
          <Icon icon="ep:setting" />
          <span class="ml-6px">基础设置</span>
        </div>
      </template>
      
      <el-form label-width="80px" label-position="left">
        <!-- 显示数量 -->
        <el-form-item label="显示数量">
          <el-input-number 
            v-model="formData.limit" 
            :min="1" 
            :max="50" 
            controls-position="right"
            class="w-full"
          />
          <div class="form-tip">设置换电站列表显示的最大数量</div>
        </el-form-item>
        
        <!-- 列表项间距 -->
        <el-form-item label="项目间距">
          <el-input-number 
            v-model="formData.itemSpacing" 
            :min="0" 
            :max="50" 
            controls-position="right"
            class="w-full"
          />
          <div class="form-tip">设置列表项之间的间距（像素）</div>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 显示设置 -->
    <el-card class="property-card" shadow="never">
      <template #header>
        <div class="card-header">
          <Icon icon="ep:view" />
          <span class="ml-6px">显示设置</span>
        </div>
      </template>
      
      <el-form label-width="80px" label-position="left">
        <!-- 显示距离 -->
        <el-form-item label="显示距离">
          <el-switch v-model="formData.showDistance" />
          <div class="form-tip">是否显示距离和导航按钮</div>
        </el-form-item>
        
        <!-- 显示价格 -->
        <el-form-item label="显示价格">
          <el-switch v-model="formData.showPrice" />
          <div class="form-tip">是否显示换电价格</div>
        </el-form-item>
        
        <!-- 显示电池数量 -->
        <el-form-item label="电池数量">
          <el-switch v-model="formData.showBatteryCount" />
          <div class="form-tip">是否显示可用电池数量</div>
        </el-form-item>
        
        <!-- 显示可换车辆数 -->
        <el-form-item label="可换车辆">
          <el-switch v-model="formData.showReplaceCarCount" />
          <div class="form-tip">是否显示可换车辆数量</div>
        </el-form-item>
        
        <!-- 显示营业时间 -->
        <el-form-item label="营业时间">
          <el-switch v-model="formData.showBusinessTime" />
          <div class="form-tip">是否显示营业时间</div>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 样式设置 -->
    <el-card class="property-card" shadow="never">
      <template #header>
        <div class="card-header">
          <Icon icon="ep:brush" />
          <span class="ml-6px">样式设置</span>
        </div>
      </template>
      
      <el-form label-width="80px" label-position="left">
        <!-- 背景设置 -->
        <el-form-item label="背景类型">
          <el-radio-group v-model="formData.style.bgType">
            <el-radio value="color">纯色</el-radio>
            <el-radio value="img">图片</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <!-- 背景颜色 -->
        <el-form-item v-if="formData.style.bgType === 'color'" label="背景颜色">
          <el-color-picker v-model="formData.style.bgColor" />
        </el-form-item>
        
        <!-- 背景图片 -->
        <el-form-item v-if="formData.style.bgType === 'img'" label="背景图片">
          <UploadImg v-model="formData.style.bgImg" height="80px" width="120px" />
        </el-form-item>
        
        <!-- 外边距 -->
        <el-form-item label="外边距">
          <div class="margin-controls">
            <div class="margin-item">
              <label>左</label>
              <el-input-number 
                v-model="formData.style.marginLeft" 
                :min="0" 
                size="small" 
                controls-position="right"
              />
            </div>
            <div class="margin-item">
              <label>右</label>
              <el-input-number 
                v-model="formData.style.marginRight" 
                :min="0" 
                size="small" 
                controls-position="right"
              />
            </div>
            <div class="margin-item">
              <label>下</label>
              <el-input-number 
                v-model="formData.style.marginBottom" 
                :min="0" 
                size="small" 
                controls-position="right"
              />
            </div>
          </div>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch } from 'vue'
import { StationListProperty } from './config'
import UploadImg from '@/components/UploadFile/src/UploadImg.vue'

/** 换电站列表属性配置 */
defineOptions({ name: 'StationListProperty' })

// 定义属性
const props = defineProps<{ modelValue: StationListProperty }>()
const emit = defineEmits<{ 'update:modelValue': [StationListProperty] }>()

// 表单数据
const formData = reactive<StationListProperty>({ ...props.modelValue })

// 监听表单数据变化
watch(
  formData,
  () => {
    emit('update:modelValue', formData)
  },
  { deep: true }
)

// 监听外部数据变化
watch(
  () => props.modelValue,
  (newVal) => {
    Object.assign(formData, newVal)
  },
  { deep: true }
)
</script>

<style scoped>
.station-list-property {
  padding: 16px;
}

.property-card {
  margin-bottom: 16px;
  border: 1px solid #e4e7ed;
}

.property-card:last-child {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #303133;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.margin-controls {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.margin-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.margin-item label {
  font-size: 12px;
  color: #606266;
}

.margin-item .el-input-number {
  width: 80px;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-form-item__label) {
  color: #606266;
  font-weight: 500;
}

:deep(.el-card__header) {
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-card__body) {
  padding: 16px;
}
</style>
