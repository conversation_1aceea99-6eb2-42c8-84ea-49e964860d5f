import { ComponentStyle, DiyComponent } from '@/components/DiyEditor/util'

/** 换电站列表属性 */
export interface StationListProperty {
  // 显示数量限制
  limit: number
  // 是否显示距离
  showDistance: boolean
  // 是否显示价格
  showPrice: boolean
  // 是否显示电池数量
  showBatteryCount: boolean
  // 是否显示可换车辆数
  showReplaceCarCount: boolean
  // 是否显示营业时间
  showBusinessTime: boolean
  // 列表项间距
  itemSpacing: number
  // 组件样式
  style: ComponentStyle
}

// 定义组件
export const component = {
  id: 'StationList',
  name: '换电站列表',
  icon: 'mdi:ev-station',
  property: {
    limit: 10,
    showDistance: true,
    showPrice: true,
    showBatteryCount: true,
    showReplaceCarCount: true,
    showBusinessTime: true,
    itemSpacing: 16,
    style: {
      bgType: 'color',
      bgColor: '#fff',
      marginLeft: 8,
      marginRight: 8,
      marginBottom: 8
    } as ComponentStyle
  }
} as DiyComponent<StationListProperty>
