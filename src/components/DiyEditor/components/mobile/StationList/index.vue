<template>
  <div class="station-list">
    <!-- 标题栏 -->
    <div class="station-title flex items-center justify-between p-x-16px p-y-12px">
      <span class="text-18px font-bold">附近换电站</span>
      <div class="flex items-center text-14px text-gray-500">
        <Icon icon="mdi:map" class="mr-4px" />
        <span>全图模式</span>
      </div>
    </div>

    <!-- 换电站列表 -->
    <div class="station-items">
      <div
        v-for="item in displayStations"
        :key="item.siteId"
        class="station-item flex p-16px border-b border-gray-100"
        :style="{ marginBottom: `${property.itemSpacing}px` }"
      >
        <!-- 站点图片 -->
        <div class="station-img flex-shrink-0 w-80px h-80px rounded-8px overflow-hidden mr-16px">
          <el-image
            :src="item.siteImg || '/static/img/default-station.png'"
            fit="cover"
            class="w-full h-full"
          >
            <template #error>
              <div class="w-full h-full flex items-center justify-center bg-gray-100">
                <Icon icon="mdi:ev-station" class="text-24px text-gray-400" />
              </div>
            </template>
          </el-image>
        </div>

        <!-- 站点信息 -->
        <div class="station-info flex-1 min-w-0">
          <!-- 地址 -->
          <div class="station-address text-16px font-medium text-gray-900 mb-8px truncate">
            {{ item.siteAdress || item.siteName }}
          </div>

          <!-- 电池和车辆信息 -->
          <div class="station-stats flex items-center mb-8px text-14px text-gray-600">
            <span v-if="property.showBatteryCount" class="mr-20px">
              电池数量：{{ item.batteryTotal || 0 }}
            </span>
            <span v-if="property.showReplaceCarCount">
              可换车辆：{{ item.carTotal || 0 }}
            </span>
          </div>

          <!-- 价格和状态 -->
          <div class="station-bottom flex items-center justify-between">
            <div class="flex items-center">
              <!-- 价格 -->
              <span v-if="property.showPrice" class="price text-18px font-bold text-red-500 mr-16px">
                <span class="text-12px">￥</span>{{ item.price || 0 }}元/度
              </span>

              <!-- 状态标签 -->
              <div class="status-tags flex items-center">
                <span
                  v-if="item.siteStatus === 0"
                  class="status-tag bg-red-50 text-red-500 px-8px py-4px rounded-4px text-12px mr-8px"
                >
                  维护中
                </span>
                <span
                  v-else-if="item.siteStatus === 1"
                  class="status-tag bg-green-50 text-green-500 px-8px py-4px rounded-4px text-12px mr-8px"
                >
                  空闲
                </span>
                <span
                  v-else-if="item.siteStatus === 2"
                  class="status-tag bg-blue-50 text-blue-500 px-8px py-4px rounded-4px text-12px mr-8px"
                >
                  使用中
                </span>

                <!-- 营业时间 -->
                <span
                  v-if="property.showBusinessTime && getBusinessHours(item)"
                  class="business-time bg-gray-50 text-gray-500 px-8px py-4px rounded-4px text-12px"
                >
                  {{ getBusinessHours(item) }}
                </span>
              </div>
            </div>

            <!-- 距离和导航 -->
            <div v-if="property.showDistance" class="distance-nav flex items-center">
              <div
                v-if="item.siteStatus === 0"
                class="pause-notice text-12px text-red-500 text-center"
              >
                站点维护<br />暂停使用
              </div>
              <div
                v-else
                class="distance-btn flex items-center bg-blue-50 text-blue-500 px-12px py-6px rounded-20px text-12px cursor-pointer hover:bg-blue-100"
              >
                <Icon icon="mdi:navigation" class="mr-4px" />
                <span v-if="item.distance !== undefined">{{ item.distance }}km</span>
                <span v-else>计算中...</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && displayStations.length === 0" class="empty-state text-center p-40px">
      <Icon icon="mdi:ev-station" class="text-48px text-gray-300 mb-16px" />
      <div class="text-gray-500">暂无换电站数据</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue'
import { StationListProperty } from './config'
import { BusSiteApi, BusSiteRespVO } from '@/api/site/bussite'

/** 换电站列表组件 */
defineOptions({ name: 'StationList' })

// 定义属性
const props = defineProps<{ property: StationListProperty }>()

// 扩展后端VO类型，以包含前端计算的距离
interface Station extends BusSiteRespVO {
  distance?: number
}

// 换电站数据
const stationData = ref<Station[]>([])
const loading = ref(false)
const userLocation = ref<{ latitude: number; longitude: number } | null>(null)

// 计算显示的换电站列表
const displayStations = computed(() => {
  return stationData.value.slice(0, props.property.limit)
})

// 计算两点之间的距离（Haversine公式）
const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
  const R = 6371 // 地球半径，单位km
  const dLat = (lat2 - lat1) * (Math.PI / 180)
  const dLon = (lon2 - lon1) * (Math.PI / 180)
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * (Math.PI / 180)) *
      Math.cos(lat2 * (Math.PI / 180)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  return parseFloat((R * c).toFixed(1))
}

// 获取用户地理位置
const getUserLocation = (): Promise<any> => {
  return new Promise((resolve, reject) => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          userLocation.value = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude
          }
          resolve(userLocation.value)
        },
        (error) => {
          console.error('获取地理位置失败:', error)
          // 注意：在生产环境中，应提供一个备用方案，例如让用户手动输入位置
          reject(error)
        }
      )
    } else {
      const error = new Error('浏览器不支持地理位置。')
      console.error(error.message)
      reject(error)
    }
  })
}

// 获取换电站数据
const fetchStationData = async () => {
  loading.value = true
  try {
    // 1. 获取用户位置
    await getUserLocation()

    // 2. 获取换电站列表
    const response = await BusSiteApi.getSimpleBusSiteList()

    // 3. 处理数据：计算距离并排序
    if (response && response.length > 0) {
      const processedStations = response.map((item) => {
        const distance = userLocation.value
          ? calculateDistance(
              userLocation.value.latitude,
              userLocation.value.longitude,
              item.latitude,
              item.longitude
            )
          : undefined
        return { ...item, distance }
      })

      // 按距离从近到远排序
      stationData.value = processedStations.sort(
        (a, b) => (a.distance ?? Infinity) - (b.distance ?? Infinity)
      )
    }
  } catch (error) {
    console.error('获取换电站数据失败:', error)
    stationData.value = [] // 出错时清空列表
  } finally {
    loading.value = false
  }
}

// 格式化营业时间
const getBusinessHours = (item: Station): string => {
  if (item.siteStatus === 0) {
    return '维护中'
  }
  if (item.startTime && item.endTime) {
    // 假设格式为 'HH:mm:ss'，截取为 'HH:mm'
    return `${item.startTime.slice(0, 5)}-${item.endTime.slice(0, 5)}`
  }
  return '24小时营业' // 提供一个默认值
}

onMounted(() => {
  fetchStationData()
})
</script>

<style scoped>
.station-list {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.station-item:last-child {
  border-bottom: none;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.status-tag {
  font-weight: 500;
}

.distance-btn {
  transition: background-color 0.2s;
}

.pause-notice {
  line-height: 1.2;
}
</style>
