<template>
  <div class="time-slot-selector">
    <!-- 操作提示 -->
    <div class="operation-tips">
      <el-icon class="tip-icon"><InfoFilled /></el-icon>
      <span class="tip-text">拖拽鼠标选择时间段，点击预设按钮快速选择，支持选择多个不连续时间段</span>
    </div>

    <!-- 快捷预设按钮组 -->
    <div class="preset-section">
      <div class="preset-label">快捷预设：</div>
      <div class="preset-buttons">
        <el-button
          v-for="preset in presetOptions"
          :key="preset.key"
          :type="selectedPreset === preset.key ? 'primary' : 'default'"
          size="default"
          @click="handlePresetClick(preset)"
          class="preset-button"
        >
          <el-icon class="preset-icon"><Clock /></el-icon>
          {{ preset.label }}
        </el-button>
      </div>
      <el-button
        size="default"
        @click="handleClearAll"
        class="clear-button"
      >
        <el-icon><Delete /></el-icon>
        清空
      </el-button>
    </div>

    <!-- 24小时时间轴 -->
    <div class="time-axis-container">
      <div class="time-axis-header">
        <h4 class="axis-title">
          <el-icon><Clock /></el-icon>
          24小时时间轴选择
        </h4>
        <div class="axis-legend">
          <span class="legend-item">
            <span class="legend-color selected"></span>
            已选择
          </span>
          <span class="legend-item">
            <span class="legend-color unselected"></span>
            未选择
          </span>
        </div>
      </div>

      <div class="time-axis">
        <!-- 时间刻度标签 -->
        <div class="time-labels">
          <span
            v-for="hour in 25"
            :key="hour - 1"
            class="time-label"
            :class="{ 'major-label': (hour - 1) % 6 === 0 }"
            :style="{ left: `${((hour - 1) / 24) * 100}%` }"
          >
            {{ String(hour - 1).padStart(2, '0') }}:00
          </span>
        </div>

        <!-- 时间轴主体 -->
        <div
          class="time-track"
          @mousedown="handleMouseDown"
          @mousemove="handleMouseMove"
          @mouseup="handleMouseUp"
          @mouseleave="handleMouseUp"
        >
          <!-- 时间段背景 -->
          <div
            v-for="(slot, index) in timeSlots"
            :key="index"
            class="time-slot"
            :style="getSlotStyle(slot)"
          >
            <div class="slot-label">{{ formatTimeSlot(slot) }}</div>
          </div>

          <!-- 拖拽预览 -->
          <div
            v-if="isDragging && dragPreview"
            class="time-slot-preview"
            :style="getSlotStyle(dragPreview)"
          >
            <div class="preview-label">{{ formatTimeSlot(dragPreview) }}</div>
          </div>

          <!-- 时间刻度线 -->
          <div class="time-ticks">
            <div
              v-for="hour in 25"
              :key="hour - 1"
              class="tick"
              :class="{ 'major-tick': (hour - 1) % 6 === 0, 'minor-tick': (hour - 1) % 6 !== 0 }"
              :style="{ left: `${((hour - 1) / 24) * 100}%` }"
            ></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 当前选择显示 -->
    <div v-if="currentSelection" class="current-selection">
      <el-icon class="selection-icon"><Select /></el-icon>
      <span class="selection-text">当前选择：{{ currentSelection }}</span>
    </div>

    <!-- 选中时段标签展示 -->
    <div class="selected-slots-section">
      <div class="slots-header">
        <h4 class="slots-title">
          <el-icon><List /></el-icon>
          已选择的时间段 ({{ timeSlots.length }})
        </h4>
        <!-- 统计信息 -->
        <div v-if="timeSlots.length > 0" class="slots-stats">
          <span class="stat-item">
            <el-icon><Clock /></el-icon>
            总时长：{{ formatDuration(totalDuration) }}
          </span>
          <span class="stat-item">
            <el-icon><DataLine /></el-icon>
            覆盖率：{{ coverageRate }}%
          </span>
        </div>
      </div>
      <div class="selected-slots">
        <transition-group name="slot-tag" tag="div" class="slots-container">
          <el-tag
            v-for="(slot, index) in timeSlots"
            :key="`${slot.startTime}-${slot.endTime}-${index}`"
            closable
            type="primary"
            size="large"
            class="slot-tag"
            @close="removeSlot(index)"
          >
            <el-icon class="tag-icon"><Clock /></el-icon>
            {{ formatTimeSlot(slot) }}
          </el-tag>
        </transition-group>
        <span v-if="timeSlots.length === 0" class="empty-text">
          请选择可用时段
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { InfoFilled, Clock, Delete, Select, List, Close, DataLine } from '@element-plus/icons-vue'

/** 时间段接口 */
interface TimeSlot {
  startTime: string
  endTime: string
}

/** 预设选项接口 */
interface PresetOption {
  key: string
  label: string
  slots: TimeSlot[]
}

/** 组件属性 */
interface Props {
  modelValue?: TimeSlot[]
}

/** 组件事件 */
interface Emits {
  (e: 'update:modelValue', value: TimeSlot[]): void
  (e: 'change', value: TimeSlot[]): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => []
})

const emits = defineEmits<Emits>()

// 响应式数据
const timeSlots = ref<TimeSlot[]>([...props.modelValue])
const selectedPreset = ref<string>('')
const isDragging = ref(false)
const dragStart = ref<number | null>(null)
const dragEnd = ref<number | null>(null)
const currentSelection = ref<string>('')

// 泰国电价时段预设
const presetOptions: PresetOption[] = [
  {
    key: 'peak',
    label: '峰时段',
    slots: [{ startTime: '09:00', endTime: '22:00' }]
  },
  {
    key: 'normal',
    label: '平时段', 
    slots: [
      { startTime: '06:00', endTime: '09:00' },
      { startTime: '22:00', endTime: '24:00' }
    ]
  },
  {
    key: 'valley',
    label: '谷时段',
    slots: [{ startTime: '00:00', endTime: '06:00' }]
  },
  {
    key: 'all',
    label: '全天',
    slots: [{ startTime: '00:00', endTime: '24:00' }]
  }
]

// 拖拽预览
const dragPreview = computed(() => {
  if (!isDragging.value || dragStart.value === null || dragEnd.value === null) {
    return null
  }

  const start = Math.min(dragStart.value, dragEnd.value)
  const end = Math.max(dragStart.value, dragEnd.value)

  return {
    startTime: formatHour(start),
    endTime: formatHour(end)
  }
})

// 计算总时长（小时）
const totalDuration = computed(() => {
  return timeSlots.value.reduce((total, slot) => {
    const startHour = parseTimeToHour(slot.startTime)
    const endHour = parseTimeToHour(slot.endTime)
    return total + (endHour - startHour)
  }, 0)
})

// 计算覆盖率（百分比）
const coverageRate = computed(() => {
  if (totalDuration.value === 0) return 0
  const rate = (totalDuration.value / 24) * 100
  return Math.round(rate * 10) / 10 // 保留一位小数
})

// 格式化时长显示
const formatDuration = (hours: number): string => {
  if (hours === 0) return '0小时'
  if (hours === Math.floor(hours)) {
    return `${hours}小时`
  }
  const h = Math.floor(hours)
  const m = Math.round((hours - h) * 60)
  if (h === 0) return `${m}分钟`
  if (m === 0) return `${h}小时`
  return `${h}小时${m}分钟`
}

/** 组件挂载时设置默认示例 */
onMounted(() => {
  // 只有在独立使用且没有传入任何初始值时才设置默认示例
  // 在表单中使用时不设置默认值，避免干扰v-model
  if (props.modelValue.length === 0 && timeSlots.value.length === 0) {
    // 可以通过props控制是否显示默认示例
    // 这里暂时注释掉，让组件在表单中正常工作
    // const defaultSlots = [
    //   { startTime: '08:00', endTime: '12:00' },
    //   { startTime: '14:00', endTime: '18:00' }
    // ]
    // timeSlots.value = defaultSlots
    // selectedPreset.value = 'example'
  }
})

/** 监听modelValue变化 */
watch(() => props.modelValue, (newValue) => {
  // 确保newValue是数组且不与当前值相同，避免无限循环
  if (Array.isArray(newValue) && JSON.stringify(newValue) !== JSON.stringify(timeSlots.value)) {
    timeSlots.value = [...newValue]
  }
}, { deep: true, immediate: true })

/** 监听timeSlots变化，同步到父组件 */
watch(timeSlots, (newValue) => {
  console.log('TimeSlotSelector - timeSlots changed:', newValue)
  emits('update:modelValue', newValue)
  emits('change', newValue)
}, { deep: true })

/** 格式化小时 */
const formatHour = (hour: number): string => {
  return `${String(Math.floor(hour)).padStart(2, '0')}:00`
}

/** 格式化时间段显示 */
const formatTimeSlot = (slot: TimeSlot): string => {
  return `${slot.startTime} ~ ${slot.endTime}`
}

/** 获取时间段样式 */
const getSlotStyle = (slot: TimeSlot) => {
  const startHour = parseTimeToHour(slot.startTime)
  const endHour = parseTimeToHour(slot.endTime)
  
  const left = (startHour / 24) * 100
  const width = ((endHour - startHour) / 24) * 100
  
  return {
    left: `${left}%`,
    width: `${width}%`
  }
}

/** 解析时间为小时数 */
const parseTimeToHour = (time: string): number => {
  const [hour, minute] = time.split(':').map(Number)
  return hour + (minute || 0) / 60
}

/** 根据鼠标位置计算小时 */
const getHourFromMouseEvent = (event: MouseEvent): number => {
  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
  const x = event.clientX - rect.left
  const percentage = x / rect.width
  return Math.max(0, Math.min(24, percentage * 24))
}

/** 鼠标按下事件 */
const handleMouseDown = (event: MouseEvent) => {
  isDragging.value = true
  const hour = getHourFromMouseEvent(event)
  dragStart.value = hour
  dragEnd.value = hour
  updateCurrentSelection()
}

/** 鼠标移动事件 */
const handleMouseMove = (event: MouseEvent) => {
  if (!isDragging.value) return
  
  const hour = getHourFromMouseEvent(event)
  dragEnd.value = hour
  updateCurrentSelection()
}

/** 鼠标释放事件 */
const handleMouseUp = () => {
  if (!isDragging.value || dragStart.value === null || dragEnd.value === null) {
    isDragging.value = false
    return
  }
  
  const start = Math.min(dragStart.value, dragEnd.value)
  const end = Math.max(dragStart.value, dragEnd.value)
  
  // 只有拖拽距离大于0.5小时才添加时间段
  if (end - start >= 0.5) {
    const newSlot: TimeSlot = {
      startTime: formatHour(Math.floor(start)),
      endTime: formatHour(Math.ceil(end))
    }
    
    addTimeSlot(newSlot)
  }
  
  // 重置拖拽状态
  isDragging.value = false
  dragStart.value = null
  dragEnd.value = null
  currentSelection.value = ''
}

/** 更新当前选择显示 */
const updateCurrentSelection = () => {
  if (dragStart.value !== null && dragEnd.value !== null) {
    const start = Math.min(dragStart.value, dragEnd.value)
    const end = Math.max(dragStart.value, dragEnd.value)
    currentSelection.value = `${formatHour(start)} - ${formatHour(end)}`
  }
}

/** 添加时间段 */
const addTimeSlot = (newSlot: TimeSlot) => {
  // 合并重叠的时间段
  const mergedSlots = mergeTimeSlots([...timeSlots.value, newSlot])
  timeSlots.value = mergedSlots
  selectedPreset.value = '' // 清除预设选择
}

/** 合并重叠的时间段 */
const mergeTimeSlots = (slots: TimeSlot[]): TimeSlot[] => {
  if (slots.length === 0) return []
  
  // 按开始时间排序
  const sorted = slots.sort((a, b) => 
    parseTimeToHour(a.startTime) - parseTimeToHour(b.startTime)
  )
  
  const merged: TimeSlot[] = [sorted[0]]
  
  for (let i = 1; i < sorted.length; i++) {
    const current = sorted[i]
    const last = merged[merged.length - 1]
    
    // 检查是否重叠或相邻
    if (parseTimeToHour(current.startTime) <= parseTimeToHour(last.endTime)) {
      // 合并时间段
      last.endTime = parseTimeToHour(current.endTime) > parseTimeToHour(last.endTime) 
        ? current.endTime 
        : last.endTime
    } else {
      merged.push(current)
    }
  }
  
  return merged
}

/** 移除时间段 */
const removeSlot = (index: number) => {
  timeSlots.value.splice(index, 1)
  selectedPreset.value = '' // 清除预设选择
}

/** 处理预设点击 */
const handlePresetClick = (preset: PresetOption) => {
  selectedPreset.value = preset.key
  timeSlots.value = [...preset.slots]
}

/** 清空所有选择 */
const handleClearAll = () => {
  timeSlots.value = []
  selectedPreset.value = ''
}

/** 暴露方法 */
defineExpose({
  getTimeSlots: () => timeSlots.value,
  setTimeSlots: (slots: TimeSlot[]) => {
    timeSlots.value = [...slots]
  },
  clearAll: handleClearAll
})
</script>

<style scoped>
.time-slot-selector {
  width: 100%;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

/* 操作提示 */
.operation-tips {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: #e8f4fd;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.tip-icon {
  color: #409eff;
  margin-right: 6px;
  font-size: 14px;
}

.tip-text {
  color: #606266;
  font-size: 12px;
  line-height: 1.3;
}

/* 预设按钮区域 */
.preset-section {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
  padding: 10px 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.preset-label {
  font-weight: 600;
  color: #303133;
  margin-right: 6px;
  font-size: 13px;
}

.preset-buttons {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.preset-button {
  /* 与清空按钮保持一致的样式 */
}

.preset-icon {
  margin-right: 4px;
}

.clear-button {
  margin-left: 6px;
}

/* 时间轴容器 */
.time-axis-container {
  margin-bottom: 12px;
  background: #fafbfc;
  border-radius: 6px;
  padding: 12px;
  border: 1px solid #e4e7ed;
}

.time-axis-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  flex-wrap: wrap;
  gap: 8px;
}

.axis-title {
  display: flex;
  align-items: center;
  margin: 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.axis-title .el-icon {
  margin-right: 6px;
  color: #409eff;
}

.axis-legend {
  display: flex;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #606266;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 6px;
}

.legend-color.selected {
  background: #409eff;
}

.legend-color.unselected {
  background: #f5f7fa;
  border: 1px solid #dcdfe6;
}

/* 时间轴主体 */
.time-axis {
  position: relative;
  height: 76px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fafafa;
  overflow: hidden;
}

.time-labels {
  position: relative;
  height: 30px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.time-label {
  position: absolute;
  font-size: 10px;
  color: #909399;
  transform: translateX(-50%);
  top: 8px;
  white-space: nowrap;
}

.time-label.major-label {
  font-weight: 600;
  color: #606266;
  font-size: 11px;
}

.time-track {
  position: relative;
  height: 45px;
  background: linear-gradient(to right, #f5f7fa 0%, #f8f9fa 50%, #f5f7fa 100%);
  cursor: crosshair;
  overflow: hidden;
}

.time-ticks {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.tick {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 1px;
  background: #e4e7ed;
}

.tick.major-tick {
  background: #c0c4cc;
  width: 2px;
}

.tick.minor-tick {
  background: #f0f0f0;
}

.time-slot {
  position: absolute;
  height: 100%;
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  border-radius: 4px;
  opacity: 0.9;
  border: 2px solid #409eff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.time-slot:hover {
  opacity: 1;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.slot-label {
  color: white;
  font-size: 11px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  white-space: nowrap;
}

.time-slot-preview {
  position: absolute;
  height: 100%;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.6) 0%, rgba(103, 194, 58, 0.6) 100%);
  border-radius: 4px;
  border: 2px dashed #409eff;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 1s infinite;
}

.preview-label {
  color: #409eff;
  font-size: 11px;
  font-weight: 600;
  white-space: nowrap;
}

@keyframes pulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 0.8; }
}

/* 当前选择显示 */
.current-selection {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: #e8f4fd;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.selection-icon {
  color: #409eff;
  margin-right: 6px;
}

.selection-text {
  font-size: 12px;
  color: #303133;
  font-weight: 500;
}

/* 选中时段展示区域 */
.selected-slots-section {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
  border: 1px solid #e4e7ed;
}

.slots-header {
  margin-bottom: 10px;
}

.slots-title {
  display: flex;
  align-items: center;
  margin: 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.slots-title .el-icon {
  margin-right: 6px;
  color: #409eff;
}

/* 统计信息样式 */
.slots-stats {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-top: 8px;
  padding: 6px 0;
}

.stat-item {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #606266;
  font-weight: 500;
}

.stat-item .el-icon {
  margin-right: 4px;
  color: #67c23a;
  font-size: 14px;
}

.slots-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  min-height: 28px;
}

.slot-tag {
  display: flex;
  align-items: center;
  font-size: 12px;
  padding: 6px 10px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.slot-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.tag-icon {
  margin-right: 4px;
}

.empty-text {
  color: #909399;
  font-size: 14px;
}

/* 动画效果 */
.slot-tag-enter-active,
.slot-tag-leave-active {
  transition: all 0.3s ease;
}

.slot-tag-enter-from {
  opacity: 0;
  transform: translateY(-10px) scale(0.8);
}

.slot-tag-leave-to {
  opacity: 0;
  transform: translateY(10px) scale(0.8);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .time-slot-selector {
    padding: 10px;
  }

  .preset-section {
    flex-direction: column;
    align-items: flex-start;
    padding: 8px 10px;
  }

  .preset-buttons {
    width: 100%;
  }

  .time-axis-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .time-axis {
    height: 66px;
  }

  .time-labels {
    height: 26px;
  }

  .time-track {
    height: 39px;
  }

  .time-label {
    font-size: 9px;
    top: 6px;
  }

  .time-label.major-label {
    font-size: 10px;
  }
}
</style>
