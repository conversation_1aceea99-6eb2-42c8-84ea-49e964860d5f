#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import os

def fix_java_file_encoding(file_path):
    """Fix encoding issues in Java file by replacing Chinese comments with English"""
    
    # Read the file with UTF-8 encoding
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        # Try with GBK encoding if UTF-8 fails
        with open(file_path, 'r', encoding='gbk') as f:
            content = f.read()
    
    # Define replacement patterns for common Chinese comments
    replacements = [
        # Method comments
        (r'\/\*\*\s*\n\s*\*\s*检查.*?\*\/', '/**\n     * Check and process method\n     */'),
        (r'\/\*\*\s*\n\s*\*\s*判断.*?\*\/', '/**\n     * Determine method\n     */'),
        (r'\/\*\*\s*\n\s*\*\s*恢复.*?\*\/', '/**\n     * Recovery method\n     */'),
        (r'\/\*\*\s*\n\s*\*\s*预加载.*?\*\/', '/**\n     * Preload method\n     */'),
        
        # Single line comments
        (r'//\s*检查.*', '// Check logic'),
        (r'//\s*判断.*', '// Determine logic'),
        (r'//\s*恢复.*', '// Recovery logic'),
        (r'//\s*预加载.*', '// Preload logic'),
        (r'//\s*查询.*', '// Query logic'),
        (r'//\s*删除.*', '// Delete logic'),
        (r'//\s*更新.*', '// Update logic'),
        (r'//\s*处理.*', '// Process logic'),
        (r'//\s*执行.*', '// Execute logic'),
        (r'//\s*调度.*', '// Schedule logic'),
        (r'//\s*取消.*', '// Cancel logic'),
        (r'//\s*验证.*', '// Validate logic'),
        (r'//\s*生成.*', '// Generate logic'),
        (r'//\s*设置.*', '// Set logic'),
        (r'//\s*获取.*', '// Get logic'),
        (r'//\s*发送.*', '// Send logic'),
        (r'//\s*记录.*', '// Record logic'),
        
        # Log messages - keep the structure but simplify
        (r'log\.info\("\[([^\]]+)\]\[([^\]]*)[^"]*"', r'log.info("[\1][\2] Operation completed"'),
        (r'log\.warn\("\[([^\]]+)\]\[([^\]]*)[^"]*"', r'log.warn("[\1][\2] Warning occurred"'),
        (r'log\.error\("\[([^\]]+)\]\[([^\]]*)[^"]*"', r'log.error("[\1][\2] Error occurred"'),
        (r'log\.debug\("\[([^\]]+)\]\[([^\]]*)[^"]*"', r'log.debug("[\1][\2] Debug info"'),
    ]
    
    # Apply replacements
    for pattern, replacement in replacements:
        content = re.sub(pattern, replacement, content, flags=re.DOTALL | re.MULTILINE)
    
    # Remove any remaining Chinese characters in comments
    content = re.sub(r'//[^"\n]*[\u4e00-\u9fff][^"\n]*', '// Chinese comment removed', content)
    content = re.sub(r'/\*[^"]*[\u4e00-\u9fff][^"]*\*/', '/* Chinese comment removed */', content, flags=re.DOTALL)
    
    # Write back with UTF-8 encoding
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Fixed encoding for {file_path}")

if __name__ == "__main__":
    file_path = "yudao-module-member/yudao-module-member-server/src/main/java/cn/iocoder/yudao/module/member/service/vehicle/VehiclePackageScheduleServiceImpl.java"
    if os.path.exists(file_path):
        fix_java_file_encoding(file_path)
    else:
        print(f"File not found: {file_path}")
