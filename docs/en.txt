// en-US.ts (English Configuration)
export default {
  system_management: 'System Management',
  infrastructure: 'Infrastructure',
  oa_example: 'OA Example',
  user_management: 'User Management',
  role_management: 'Role Management',
  menu_management: 'Menu Management',
  department_management: 'Department Management',
  position_management: 'Position Management',
  dictionary_management: 'Dictionary Management',
  configuration_management: 'Configuration Management',
  notification_announcement: 'Notification & Announcement',
  audit_log: 'Audit Log',
  token_management: 'Token Management',
  scheduled_task: 'Scheduled Task',
  mysql_monitoring: 'MySQL Monitoring',
  java_monitoring: 'Java Monitoring',
  redis_monitoring: 'Redis Monitoring',
  form_builder: 'Form Builder',
  code_generation: 'Code Generation',
  api_interface: 'API Interface',
  operation_log: 'Operation Log',
  login_log: 'Login Log',
  user_query: 'User Query',
  user_create: 'User Create',
  user_update: 'User Update',
  user_delete: 'User Delete',
  user_export: 'User Export',
  user_import: 'User Import',
  reset_password: 'Reset Password',
  role_query: 'Role Query',
  role_create: 'Role Create',
  role_update: 'Role Update',
  role_delete: 'Role Delete',
  role_export: 'Role Export',
  menu_query: 'Menu Query',
  menu_create: 'Menu Create',
  menu_update: 'Menu Update',
  menu_delete: 'Menu Delete',
  department_query: 'Department Query',
  department_create: 'Department Create',
  department_update: 'Department Update',
  department_delete: 'Department Delete',
  position_query: 'Position Query',
  position_create: 'Position Create',
  position_update: 'Position Update',
  position_delete: 'Position Delete',
  position_export: 'Position Export',
  dictionary_query: 'Dictionary Query',
  dictionary_create: 'Dictionary Create',
  dictionary_update: 'Dictionary Update',
  dictionary_delete: 'Dictionary Delete',
  dictionary_export: 'Dictionary Export',
  configuration_query: 'Configuration Query',
  configuration_create: 'Configuration Create',
  configuration_update: 'Configuration Update',
  configuration_delete: 'Configuration Delete',
  configuration_export: 'Configuration Export',
  announcement_query: 'Announcement Query',
  announcement_create: 'Announcement Create',
  announcement_update: 'Announcement Update',
  announcement_delete: 'Announcement Delete',
  operation_query: 'Operation Query',
  log_export: 'Log Export',
  login_query: 'Login Query',
  token_list: 'Token List',
  token_delete: 'Token Delete',
  task_create: 'Task Create',
  task_update: 'Task Update',
  task_delete: 'Task Delete',
  status_update: 'Status Update',
  task_export: 'Task Export',
  generation_update: 'Generation Update',
  generation_delete: 'Generation Delete',
  import_code: 'Import Code',
  preview_code: 'Preview Code',
  generate_code: 'Generate Code',
  set_role_menu_permission: 'Set Role Menu Permission',
  set_role_data_permission: 'Set Role Data Permission',
  set_user_role: 'Set User Role',
  get_redis_monitoring_info: 'Get Redis Monitoring Info',
  get_redis_key_list: 'Get Redis Key List',
  code_generation_example: 'Code Generation Example',
  task_trigger: 'Task Trigger',
  trace_link: 'Trace Link',
  access_log: 'Access Log',
  api_log: 'API Log',
  error_log: 'Error Log',
  log_processing: 'Log Processing',
  task_query: 'Task Query',
  log_query: 'Log Query',
  file_list: 'File List',
  file_query: 'File Query',
  file_delete: 'File Delete',
  sms_management: 'SMS Management',
  sms_channel: 'SMS Channel',
  sms_channel_query: 'SMS Channel Query',
  sms_channel_create: 'SMS Channel Create',
  sms_channel_update: 'SMS Channel Update',
  sms_channel_delete: 'SMS Channel Delete',
  sms_template: 'SMS Template',
  sms_template_query: 'SMS Template Query',
  sms_template_create: 'SMS Template Create',
  sms_template_update: 'SMS Template Update',
  sms_template_delete: 'SMS Template Delete',
  sms_template_export: 'SMS Template Export',
  send_test_sms: 'Send Test SMS',
  sms_log: 'SMS Log',
  sms_log_query: 'SMS Log Query',
  sms_log_export: 'SMS Log Export',
  payment_management: 'Payment Management',
  leave_query: 'Leave Query',
  leave_application_query: 'Leave Application Query',
  leave_application_create: 'Leave Application Create',
  application_information: 'Application Information',
  payment_application_info_query: 'Payment Application Info Query',
  payment_application_info_create: 'Payment Application Info Create',
  payment_application_info_update: 'Payment Application Info Update',
  payment_application_info_delete: 'Payment Application Info Delete',
  secret_key_parsing: 'Secret Key Parsing',
  payment_merchant_info_query: 'Payment Merchant Info Query',
  payment_merchant_info_create: 'Payment Merchant Info Create',
  payment_merchant_info_update: 'Payment Merchant Info Update',
  payment_merchant_info_delete: 'Payment Merchant Info Delete',
  payment_merchant_info_export: 'Payment Merchant Info Export',
  tenant_list: 'Tenant List',
  tenant_query: 'Tenant Query',
  tenant_create: 'Tenant Create',
  tenant_update: 'Tenant Update',
  tenant_delete: 'Tenant Delete',
  tenant_export: 'Tenant Export',
  refund_order: 'Refund Order',
  refund_order_query: 'Refund Order Query',
  refund_order_create: 'Refund Order Create',
  refund_order_update: 'Refund Order Update',
  refund_order_delete: 'Refund Order Delete',
  refund_order_export: 'Refund Order Export',
  payment_order: 'Payment Order',
  payment_order_query: 'Payment Order Query',
  payment_order_create: 'Payment Order Create',
  payment_order_update: 'Payment Order Update',
  payment_order_delete: 'Payment Order Delete',
  payment_order_export: 'Payment Order Export',
  workflow_management: 'Workflow Management',
  process_management: 'Process Management',
  process_form: 'Process Form',
  form_query: 'Form Query',
  form_create: 'Form Create',
  form_update: 'Form Update',
  form_delete: 'Form Delete',
  form_export: 'Form Export',
  process_model: 'Process Model',
  model_query: 'Model Query',
  model_create: 'Model Create',
  model_update: 'Model Update',
  model_delete: 'Model Delete',
  model_publish: 'Model Publish',
  approval_center: 'Approval Center',
  my_processes: 'My Processes',
  process_instance_query: 'Process Instance Query',
  pending_task: 'Pending Task',
  completed_task: 'Completed Task',
  user_group: 'User Group',
  user_group_query: 'User Group Query',
  user_group_create: 'User Group Create',
  user_group_update: 'User Group Update',
  user_group_delete: 'User Group Delete',
  process_definition_query: 'Process Definition Query',
  process_task_assignment_rule_query: 'Process Task Assignment Rule Query',
  process_task_assignment_rule_create: 'Process Task Assignment Rule Create',
  process_task_assignment_rule_update: 'Process Task Assignment Rule Update',
  process_instance_create: 'Process Instance Create',
  process_instance_cancel: 'Process Instance Cancel',
  process_task_query: 'Process Task Query',
  process_task_update: 'Process Task Update',
  tenant_management: 'Tenant Management',
  tenant_package: 'Tenant Package',
  tenant_package_query: 'Tenant Package Query',
  tenant_package_create: 'Tenant Package Create',
  tenant_package_update: 'Tenant Package Update',
  tenant_package_delete: 'Tenant Package Delete',
  file_configuration: 'File Configuration',
  file_configuration_query: 'File Configuration Query',
  file_configuration_create: 'File Configuration Create',
  file_configuration_update: 'File Configuration Update',
  file_configuration_delete: 'File Configuration Delete',
  file_configuration_export: 'File Configuration Export',
  file_management: 'File Management',
  author_dynamics: 'Author Dynamics',
  datasource_configuration: 'Datasource Configuration',
  datasource_configuration_query: 'Datasource Configuration Query',
  datasource_configuration_create: 'Datasource Configuration Create',
  datasource_configuration_update: 'Datasource Configuration Update',
  datasource_configuration_delete: 'Datasource Configuration Delete',
  datasource_configuration_export: 'Datasource Configuration Export',
  oauth_2_0: 'OAuth 2.0',
  application_management: 'Application Management',
  client_query: 'Client Query',
  client_create: 'Client Create',
  client_update: 'Client Update',
  client_delete: 'Client Delete',
  report_management: 'Report Management',
  report_designer: 'Report Designer',
  product_center: 'Product Center',
  product_category: 'Product Category',
  category_query: 'Category Query',
  category_create: 'Category Create',
  category_update: 'Category Update',
  category_delete: 'Category Delete',
  product_brand: 'Product Brand',
  brand_query: 'Brand Query',
  brand_create: 'Brand Create',
  brand_update: 'Brand Update',
  brand_delete: 'Brand Delete',
  product_list: 'Product List',
  product_query: 'Product Query',
  product_create: 'Product Create',
  product_update: 'Product Update',
  product_delete: 'Product Delete',
  product_attribute: 'Product Attribute',
  specification_query: 'Specification Query',
  specification_create: 'Specification Create',
  specification_update: 'Specification Update',
  specification_delete: 'Specification Delete',
  banner: 'Banner',
  banner_query: 'Banner Query',
  banner_create: 'Banner Create',
  banner_update: 'Banner Update',
  banner_delete: 'Banner Delete',
  marketing_center: 'Marketing Center',
  coupon_list: 'Coupon List',
  coupon_template_query: 'Coupon Template Query',
  coupon_template_create: 'Coupon Template Create',
  coupon_template_update: 'Coupon Template Update',
  coupon_template_delete: 'Coupon Template Delete',
  claim_record: 'Claim Record',
  coupon_query: 'Coupon Query',
  coupon_delete: 'Coupon Delete',
  full_reduction_promotion: 'Full Reduction Promotion',
  full_reduction_activity_query: 'Full Reduction Activity Query',
  full_reduction_activity_create: 'Full Reduction Activity Create',
  full_reduction_activity_update: 'Full Reduction Activity Update',
  full_reduction_activity_delete: 'Full Reduction Activity Delete',
  full_reduction_activity_close: 'Full Reduction Activity Close',
  limited_time_discount: 'Limited Time Discount',
  limited_time_discount_activity_query: 'Limited Time Discount Activity Query',
  limited_time_discount_activity_create: 'Limited Time Discount Activity Create',
  limited_time_discount_activity_update: 'Limited Time Discount Activity Update',
  limited_time_discount_activity_delete: 'Limited Time Discount Activity Delete',
  limited_time_discount_activity_close: 'Limited Time Discount Activity Close',
  flash_sale_product: 'Flash Sale Product',
  flash_sale_activity_query: 'Flash Sale Activity Query',
  flash_sale_activity_create: 'Flash Sale Activity Create',
  flash_sale_activity_update: 'Flash Sale Activity Update',
  flash_sale_activity_delete: 'Flash Sale Activity Delete',
  flash_sale_timeslot: 'Flash Sale Timeslot',
  flash_sale_timeslot_query: 'Flash Sale Timeslot Query',
  flash_sale_timeslot_create: 'Flash Sale Timeslot Create',
  flash_sale_timeslot_update: 'Flash Sale Timeslot Update',
  flash_sale_timeslot_delete: 'Flash Sale Timeslot Delete',
  order_center: 'Order Center',
  after_sales_refund: 'After-sales Refund',
  after_sales_query: 'After-sales Query',
  flash_sale_activity_close: 'Flash Sale Activity Close',
  order_list: 'Order List',
  region_management: 'Region Management',
  official_account_management: 'Official Account Management',
  account_management: 'Account Management',
  add_account: 'Add Account',
  update_account: 'Update Account',
  query_account: 'Query Account',
  delete_account: 'Delete Account',
  generate_qrcode: 'Generate QRCode',
  clear_api_quota: 'Clear API Quota',
  data_statistics: 'Data Statistics',
  tag_management: 'Tag Management',
  query_tag: 'Query Tag',
  add_tag: 'Add Tag',
  update_tag: 'Update Tag',
  delete_tag: 'Delete Tag',
  sync_tag: 'Sync Tag',
  fan_management: 'Fan Management',
  query_fan: 'Query Fan',
  update_fan: 'Update Fan',
  sync_fan: 'Sync Fan',
  message_management: 'Message Management',
  article_publish_record: 'Article Publish Record',
  query_publish_list: 'Query Publish List',
  publish_draft: 'Publish Draft',
  delete_publish_record: 'Delete Publish Record',
  article_draft_box: 'Article Draft Box',
  create_draft: 'Create Draft',
  update_draft: 'Update Draft',
  query_draft: 'Query Draft',
  delete_draft: 'Delete Draft',
  material_management: 'Material Management',
  upload_temporary_material: 'Upload Temporary Material',
  upload_permanent_material: 'Upload Permanent Material',
  delete_material: 'Delete Material',
  upload_article_image: 'Upload Article Image',
  query_material: 'Query Material',
  menu_management_official_account: 'Menu Management', // Differentiated
  auto_reply: 'Auto Reply',
  query_reply: 'Query Reply',
  add_reply: 'Add Reply',
  update_reply: 'Update Reply',
  delete_reply: 'Delete Reply',
  query_menu: 'Query Menu',
  save_menu: 'Save Menu',
  delete_menu: 'Delete Menu',
  query_message: 'Query Message',
  send_message: 'Send Message',
  email_management: 'Email Management',
  email_account: 'Email Account',
  account_query: 'Account Query',
  account_create: 'Account Create',
  account_update: 'Account Update',
  account_delete: 'Account Delete',
  email_template: 'Email Template',
  template_query: 'Template Query',
  template_create: 'Template Create',
  template_update: 'Template Update',
  template_delete: 'Template Delete',
  email_record: 'Email Record',
  log_query_email: 'Log Query', // Differentiated
  send_test_email: 'Send Test Email',
  internal_message_management: 'Internal Message Management',
  template_management_internal_message: 'Template Management', // Differentiated
  internal_message_template_query: 'Internal Message Template Query',
  internal_message_template_create: 'Internal Message Template Create',
  internal_message_template_update: 'Internal Message Template Update',
  internal_message_template_delete: 'Internal Message Template Delete',
  send_test_internal_message: 'Send Test Internal Message',
  message_record: 'Message Record',
  internal_message_query: 'Internal Message Query',
  large_screen_designer: 'Large Screen Designer',
  create_project: 'Create Project',
  update_project: 'Update Project',
  query_project: 'Query Project',
  query_data_sql: 'Query Data using SQL',
  query_data_http: 'Query Data using HTTP',
  boot_development_document: 'Boot Development Document',
  cloud_development_document: 'Cloud Development Document',
  access_example: 'Access Example',
  product_export: 'Product Export',
  delivery_management: 'Delivery Management',
  express_delivery_shipment: 'Express Delivery Shipment',
  store_self_pickup: 'Store Self-pickup',
  express_company: 'Express Company',
  express_company_query: 'Express Company Query',
  express_company_create: 'Express Company Create',
  express_company_update: 'Express Company Update',
  express_company_delete: 'Express Company Delete',
  express_company_export: 'Express Company Export',
  shipping_template: 'Shipping Template',
  shipping_template_query: 'Shipping Template Query',
  shipping_template_create: 'Shipping Template Create',
  shipping_template_update: 'Shipping Template Update',
  shipping_template_delete: 'Shipping Template Delete',
  shipping_template_export: 'Shipping Template Export',
  store_management: 'Store Management',
  self_pickup_store_query: 'Self-pickup Store Query',
  self_pickup_store_create: 'Self-pickup Store Create',
  self_pickup_store_update: 'Self-pickup Store Update',
  self_pickup_store_delete: 'Self-pickup Store Delete',
  self_pickup_store_export: 'Self-pickup Store Export',
  flash_sale_activity: 'Flash Sale Activity',
  member_center: 'Member Center',
  member_configuration: 'Member Configuration',
  member_configuration_query: 'Member Configuration Query',
  member_configuration_save: 'Member Configuration Save',
  check_in_configuration: 'Check-in Configuration',
  points_check_in_rule_query: 'Points Check-in Rule Query',
  points_check_in_rule_create: 'Points Check-in Rule Create',
  points_check_in_rule_update: 'Points Check-in Rule Update',
  points_check_in_rule_delete: 'Points Check-in Rule Delete',
  member_points: 'Member Points',
  user_points_record_query: 'User Points Record Query',
  check_in_record: 'Check-in Record',
  user_check_in_points_query: 'User Check-in Points Query',
  user_check_in_points_delete: 'User Check-in Points Delete',
  member_check_in: 'Member Check-in',
  callback_notification: 'Callback Notification',
  payment_notification_query: 'Payment Notification Query',
  group_buying_activity: 'Group Buying Activity',
  group_buying_product: 'Group Buying Product',
  group_buying_activity_query: 'Group Buying Activity Query',
  group_buying_activity_create: 'Group Buying Activity Create',
  group_buying_activity_update: 'Group Buying Activity Update',
  group_buying_activity_delete: 'Group Buying Activity Delete',
  group_buying_activity_close: 'Group Buying Activity Close',
  bargain_activity: 'Bargain Activity',
  bargain_product: 'Bargain Product',
  bargain_activity_query: 'Bargain Activity Query',
  bargain_activity_create: 'Bargain Activity Create',
  bargain_activity_update: 'Bargain Activity Update',
  bargain_activity_delete: 'Bargain Activity Delete',
  bargain_activity_close: 'Bargain Activity Close',
  member_management: 'Member Management',
  member_user_query: 'Member User Query',
  member_user_update: 'Member User Update',
  member_tag: 'Member Tag',
  member_tag_query: 'Member Tag Query',
  member_tag_create: 'Member Tag Create',
  member_tag_update: 'Member Tag Update',
  member_tag_delete: 'Member Tag Delete',
  member_level: 'Member Level',
  member_level_query: 'Member Level Query',
  member_level_create: 'Member Level Create',
  member_level_update: 'Member Level Update',
  member_level_delete: 'Member Level Delete',
  member_group: 'Member Group',
  user_group_query_member: 'User Group Query', // Differentiated
  user_group_create_member: 'User Group Create', // Differentiated
  user_group_update_member: 'User Group Update', // Differentiated
  user_group_delete_member: 'User Group Delete', // Differentiated
  user_level_update: 'User Level Update',
  product_comment: 'Product Comment',
  comment_query: 'Comment Query',
  add_self_comment: 'Add Self Comment',
  merchant_reply: 'Merchant Reply',
  show_hide_comment: 'Show/Hide Comment',
  coupon_send: 'Coupon Send',
  transaction_configuration: 'Transaction Configuration',
  transaction_center_configuration_query: 'Transaction Center Configuration Query',
  transaction_center_configuration_save: 'Transaction Center Configuration Save',
  distribution_management: 'Distribution Management',
  distribution_user: 'Distribution User',
  distribution_user_query: 'Distribution User Query',
  distribution_user_promoter_query: 'Distribution User Promoter Query',
  distribution_user_promotion_order_query: 'Distribution User Promotion Order Query',
  distribution_user_update_promotion_qualification: 'Distribution User Update Promotion Qualification',
  update_promoter: 'Update Promoter',
  clear_promoter: 'Clear Promoter',
  commission_record: 'Commission Record',
  commission_record_query: 'Commission Record Query',
  commission_withdrawal: 'Commission Withdrawal',
  commission_withdrawal_query: 'Commission Withdrawal Query',
  commission_withdrawal_approve: 'Commission Withdrawal Approve',
  statistics_center: 'Statistics Center',
  transaction_statistics: 'Transaction Statistics',
  transaction_statistics_query: 'Transaction Statistics Query',
  transaction_statistics_export: 'Transaction Statistics Export',
  mall_system: 'Mall System',
  user_points_update: 'User Points Update',
  user_balance_update: 'User Balance Update',
  coupon: 'Coupon',
  bargain_record: 'Bargain Record',
  bargain_record_query: 'Bargain Record Query',
  assistance_record_query: 'Assistance Record Query',
  group_buying_record: 'Group Buying Record',
  member_statistics: 'Member Statistics',
  member_statistics_query: 'Member Statistics Query',
  order_verification: 'Order Verification',
  article_category: 'Article Category',
  category_query_article: 'Category Query', // Differentiated
  category_create_article: 'Category Create', // Differentiated
  category_update_article: 'Category Update', // Differentiated
  category_delete_article: 'Category Delete', // Differentiated
  article_list: 'Article List',
  article_management_query: 'Article Management Query',
  article_management_create: 'Article Management Create',
  article_management_update: 'Article Management Update',
  article_management_delete: 'Article Management Delete',
  content_management: 'Content Management',
  mall_home_page: 'Mall Home Page',
  verify_order: 'Verify Order',
  promotional_activities: 'Promotional Activities',
  customer_management: 'Customer Management',
  customer_query: 'Customer Query',
  customer_create: 'Customer Create',
  customer_update: 'Customer Update',
  customer_delete: 'Customer Delete',
  customer_export: 'Customer Export',
  crm_system: 'CRM System',
  contract_management: 'Contract Management',
  contract_query: 'Contract Query',
  contract_create: 'Contract Create',
  contract_update: 'Contract Update',
  contract_delete: 'Contract Delete',
  contract_export: 'Contract Export',
  lead_management: 'Lead Management',
  lead_query: 'Lead Query',
  lead_create: 'Lead Create',
  lead_update: 'Lead Update',
  lead_delete: 'Lead Delete',
  lead_export: 'Lead Export',
  opportunity_management: 'Opportunity Management',
  opportunity_query: 'Opportunity Query',
  opportunity_create: 'Opportunity Create',
  opportunity_update: 'Opportunity Update',
  opportunity_delete: 'Opportunity Delete',
  opportunity_export: 'Opportunity Export',
  contact_management: 'Contact Management',
  contact_query: 'Contact Query',
  contact_create: 'Contact Create',
  contact_update: 'Contact Update',
  contact_delete: 'Contact Delete',
  contact_export: 'Contact Export',
  payment_collection_management: 'Payment Collection Management',
  payment_collection_management_query: 'Payment Collection Management Query',
  payment_collection_management_create: 'Payment Collection Management Create',
  payment_collection_management_update: 'Payment Collection Management Update',
  payment_collection_management_delete: 'Payment Collection Management Delete',
  payment_collection_management_export: 'Payment Collection Management Export',
  payment_plan: 'Payment Plan',
  payment_plan_query: 'Payment Plan Query',
  payment_plan_create: 'Payment Plan Create',
  payment_plan_update: 'Payment Plan Update',
  payment_plan_delete: 'Payment Plan Delete',
  payment_plan_export: 'Payment Plan Export',
  store_decoration: 'Store Decoration',
  decoration_template: 'Decoration Template',
  decoration_template_query: 'Decoration Template Query',
  decoration_template_create: 'Decoration Template Create',
  decoration_template_update: 'Decoration Template Update',
  decoration_template_delete: 'Decoration Template Delete',
  decoration_template_use: 'Use Decoration Template',
  decoration_page: 'Decoration Page',
  decoration_page_query: 'Decoration Page Query',
  decoration_page_create: 'Decoration Page Create',
  decoration_page_update: 'Decoration Page Update',
  decoration_page_delete: 'Decoration Page Delete',
  third_party_login: 'Third-party Login',
  third_party_application: 'Third-party Application',
  third_party_application_query: 'Third-party Application Query',
  third_party_application_create: 'Third-party Application Create',
  third_party_application_update: 'Third-party Application Update',
  third_party_application_delete: 'Third-party Application Delete',
  third_party_user: 'Third-party User',
  main_sub_table_inline: 'Main-Sub Table (Inline)',
  single_table_crud: 'Single Table (CRUD)',
  example_contact_query: 'Example Contact Query',
  example_contact_create: 'Example Contact Create',
  example_contact_update: 'Example Contact Update',
  example_contact_delete: 'Example Contact Delete',
  example_contact_export: 'Example Contact Export',
  tree_table_crud: 'Tree Table (CRUD)',
  example_category_query: 'Example Category Query',
  example_category_create: 'Example Category Create',
  example_category_update: 'Example Category Update',
  example_category_delete: 'Example Category Delete',
  example_category_export: 'Example Category Export',
  main_sub_table_standard: 'Main-Sub Table (Standard)',
  student_query: 'Student Query',
  student_create: 'Student Create',
  student_update: 'Student Update',
  student_delete: 'Student Delete',
  student_export: 'Student Export',
  main_sub_table_erp: 'Main-Sub Table (ERP)',
  customer_common_pool_config: 'Customer Common Pool Config',
  customer_common_pool_config_save: 'Customer Common Pool Config Save',
  customer_limit_config: 'Customer Limit Config',
  customer_limit_config_query: 'Customer Limit Config Query',
  customer_limit_config_create: 'Customer Limit Config Create',
  customer_limit_config_update: 'Customer Limit Config Update',
  customer_limit_config_delete: 'Customer Limit Config Delete',
  customer_limit_config_export: 'Customer Limit Config Export',
  system_configuration: 'System Configuration',
  websocket: 'WebSocket',
  product_management: 'Product Management',
  product_query_generic: 'Product Query', // Differentiated
  product_create_generic: 'Product Create', // Differentiated
  product_update_generic: 'Product Update', // Differentiated
  product_delete_generic: 'Product Delete', // Differentiated
  product_export_generic: 'Product Export', // Differentiated
  product_category_config: 'Product Category Config',
  product_category_query: 'Product Category Query',
  product_category_create: 'Product Category Create',
  product_category_update: 'Product Category Update',
  product_category_delete: 'Product Category Delete',
  associate_opportunity: 'Associate Opportunity',
  unfollow_opportunity: 'Unfollow Opportunity',
  product_statistics: 'Product Statistics',
  customer_common_pool: 'Customer Common Pool',
  order_query: 'Order Query',
  order_update: 'Order Update',
  payment_refund_example: 'Payment & Refund Example',
  transfer_example: 'Transfer Example',
  wallet_management: 'Wallet Management',
  recharge_package: 'Recharge Package',
  wallet_recharge_package_query: 'Wallet Recharge Package Query',
  wallet_recharge_package_create: 'Wallet Recharge Package Create',
  wallet_recharge_package_update: 'Wallet Recharge Package Update',
  wallet_recharge_package_delete: 'Wallet Recharge Package Delete',
  wallet_balance: 'Wallet Balance',
  wallet_balance_query: 'Wallet Balance Query',
  transfer_order: 'Transfer Order',
  data_statistics_generic: 'Data Statistics', // Differentiated
  leaderboard: 'Leaderboard',
  customer_import: 'Customer Import',
  erp_system: 'ERP System',
  product_management_erp: 'Product Management', // Differentiated
  product_information: 'Product Information',
  product_query_erp: 'Product Query', // Differentiated
  product_create_erp: 'Product Create', // Differentiated
  product_update_erp: 'Product Update', // Differentiated
  product_delete_erp: 'Product Delete', // Differentiated
  product_export_erp: 'Product Export', // Differentiated
  product_category_erp: 'Product Category', // Differentiated
  category_query_erp: 'Category Query', // Differentiated
  category_create_erp: 'Category Create', // Differentiated
  category_update_erp: 'Category Update', // Differentiated
  category_delete_erp: 'Category Delete', // Differentiated
  category_export_erp: 'Category Export', // Differentiated
  product_unit: 'Product Unit',
  unit_query: 'Unit Query',
  unit_create: 'Unit Create',
  unit_update: 'Unit Update',
  unit_delete: 'Unit Delete',
  unit_export: 'Unit Export',
  inventory_management: 'Inventory Management',
  warehouse_information: 'Warehouse Information',
  warehouse_query: 'Warehouse Query',
  warehouse_create: 'Warehouse Create',
  warehouse_update: 'Warehouse Update',
  warehouse_delete: 'Warehouse Delete',
  warehouse_export: 'Warehouse Export',
  product_inventory: 'Product Inventory',
  inventory_query: 'Inventory Query',
  inventory_export: 'Inventory Export',
  stock_in_out_details: 'Stock In/Out Details',
  inventory_details_query: 'Inventory Details Query',
  inventory_details_export: 'Inventory Details Export',
  other_inbound: 'Other Inbound',
  other_inbound_order_query: 'Other Inbound Order Query',
  other_inbound_order_create: 'Other Inbound Order Create',
  other_inbound_order_update: 'Other Inbound Order Update',
  other_inbound_order_delete: 'Other Inbound Order Delete',
  other_inbound_order_export: 'Other Inbound Order Export',
  purchase_management: 'Purchase Management',
  supplier_information: 'Supplier Information',
  supplier_query: 'Supplier Query',
  supplier_create: 'Supplier Create',
  supplier_update: 'Supplier Update',
  supplier_delete: 'Supplier Delete',
  supplier_export: 'Supplier Export',
  other_inbound_order_approval: 'Other Inbound Order Approval',
  other_outbound: 'Other Outbound',
  other_outbound_order_query: 'Other Outbound Order Query',
  other_outbound_order_create: 'Other Outbound Order Create',
  other_outbound_order_update: 'Other Outbound Order Update',
  other_outbound_order_delete: 'Other Outbound Order Delete',
  other_outbound_order_export: 'Other Outbound Order Export',
  other_outbound_order_approval: 'Other Outbound Order Approval',
  sales_management: 'Sales Management',
  customer_information: 'Customer Information',
  customer_query_sales: 'Customer Query', // Differentiated
  customer_create_sales: 'Customer Create', // Differentiated
  customer_update_sales: 'Customer Update', // Differentiated
  customer_delete_sales: 'Customer Delete', // Differentiated
  customer_export_sales: 'Customer Export', // Differentiated
  inventory_transfer: 'Inventory Transfer',
  inventory_transfer_order_query: 'Inventory Transfer Order Query',
  inventory_transfer_order_create: 'Inventory Transfer Order Create',
  inventory_transfer_order_update: 'Inventory Transfer Order Update',
  inventory_transfer_order_delete: 'Inventory Transfer Order Delete',
  inventory_transfer_order_export: 'Inventory Transfer Order Export',
  inventory_transfer_order_approval: 'Inventory Transfer Order Approval',
  inventory_count: 'Inventory Count',
  inventory_count_order_query: 'Inventory Count Order Query',
  inventory_count_order_create: 'Inventory Count Order Create',
  inventory_count_order_update: 'Inventory Count Order Update',
  inventory_count_order_delete: 'Inventory Count Order Delete',
  inventory_count_order_export: 'Inventory Count Order Export',
  inventory_count_order_approval: 'Inventory Count Order Approval',
  sales_order: 'Sales Order',
  sales_order_query: 'Sales Order Query',
  sales_order_create: 'Sales Order Create',
  sales_order_update: 'Sales Order Update',
  sales_order_delete: 'Sales Order Delete',
  sales_order_export: 'Sales Order Export',
  sales_order_approval: 'Sales Order Approval',
  financial_management: 'Financial Management',
  settlement_account: 'Settlement Account',
  settlement_account_query: 'Settlement Account Query',
  settlement_account_create: 'Settlement Account Create',
  settlement_account_update: 'Settlement Account Update',
  settlement_account_delete: 'Settlement Account Delete',
  settlement_account_export: 'Settlement Account Export',
  sales_outbound: 'Sales Outbound',
  sales_outbound_query: 'Sales Outbound Query',
  sales_outbound_create: 'Sales Outbound Create',
  sales_outbound_update: 'Sales Outbound Update',
  sales_outbound_delete: 'Sales Outbound Delete',
  sales_outbound_export: 'Sales Outbound Export',
  sales_outbound_approval: 'Sales Outbound Approval',
  sales_return: 'Sales Return',
  sales_return_query: 'Sales Return Query',
  sales_return_create: 'Sales Return Create',
  sales_return_update: 'Sales Return Update',
  sales_return_delete: 'Sales Return Delete',
  sales_return_export: 'Sales Return Export',
  sales_return_approval: 'Sales Return Approval',
  purchase_order: 'Purchase Order',
  purchase_order_query: 'Purchase Order Query',
  purchase_order_create: 'Purchase Order Create',
  purchase_order_update: 'Purchase Order Update',
  purchase_order_delete: 'Purchase Order Delete',
  purchase_order_export: 'Purchase Order Export',
  purchase_order_approval: 'Purchase Order Approval',
  purchase_inbound: 'Purchase Inbound',
  purchase_inbound_query: 'Purchase Inbound Query',
  purchase_inbound_create: 'Purchase Inbound Create',
  purchase_inbound_update: 'Purchase Inbound Update',
  purchase_inbound_delete: 'Purchase Inbound Delete',
  purchase_inbound_export: 'Purchase Inbound Export',
  purchase_inbound_approval: 'Purchase Inbound Approval',
  purchase_return: 'Purchase Return',
  purchase_return_query: 'Purchase Return Query',
  purchase_return_create: 'Purchase Return Create',
  purchase_return_update: 'Purchase Return Update',
  purchase_return_delete: 'Purchase Return Delete',
  purchase_return_export: 'Purchase Return Export',
  purchase_return_approval: 'Purchase Return Approval',
  payment_slip: 'Payment Slip',
  payment_slip_query: 'Payment Slip Query',
  payment_slip_create: 'Payment Slip Create',
  payment_slip_update: 'Payment Slip Update',
  payment_slip_delete: 'Payment Slip Delete',
  payment_slip_export: 'Payment Slip Export',
  payment_slip_approval: 'Payment Slip Approval',
  receipt_slip: 'Receipt Slip',
  receipt_slip_query: 'Receipt Slip Query',
  receipt_slip_create: 'Receipt Slip Create',
  receipt_slip_update: 'Receipt Slip Update',
  receipt_slip_delete: 'Receipt Slip Delete',
  receipt_slip_export: 'Receipt Slip Export',
  receipt_slip_approval: 'Receipt Slip Approval',
  todo_items: 'Todo Items',
  erp_home_page: 'ERP Home Page',
  opportunity_status_config: 'Opportunity Status Config',
  opportunity_status_query: 'Opportunity Status Query',
  opportunity_status_create: 'Opportunity Status Create',
  opportunity_status_update: 'Opportunity Status Update',
  opportunity_status_delete: 'Opportunity Status Delete',
  contract_configuration: 'Contract Configuration',
  customer_common_pool_config_query: 'Customer Common Pool Config Query',
  contract_configuration_update: 'Contract Configuration Update',
  contract_configuration_query: 'Contract Configuration Query',
  customer_analysis: 'Customer Analysis',
  cc_to_me: 'CC to Me',
  process_category: 'Process Category',
  category_query_process: 'Category Query', // Differentiated
  category_create_process: 'Category Create', // Differentiated
  category_update_process: 'Category Update', // Differentiated
  category_delete_process: 'Category Delete', // Differentiated
  initiate_process: 'Initiate Process',
  process_instance: 'Process Instance',
  process_instance_query_admin: 'Process Instance Query (Admin)',
  process_instance_cancel_admin: 'Process Instance Cancel (Admin)',
  process_task: 'Process Task',
  process_task_query_admin: 'Process Task Query (Admin)',
  process_listener: 'Process Listener',
  process_listener_query: 'Process Listener Query',
  process_listener_create: 'Process Listener Create',
  process_listener_update: 'Process Listener Update',
  process_listener_delete: 'Process Listener Delete',
  process_expression: 'Process Expression',
  process_expression_query: 'Process Expression Query',
  process_expression_create: 'Process Expression Create',
  process_expression_update: 'Process Expression Update',
  process_expression_delete: 'Process Expression Delete',
  employee_performance: 'Employee Performance',
  customer_profile: 'Customer Profile',
  sales_funnel: 'Sales Funnel',
  message_center: 'Message Center',
  monitoring_center: 'Monitoring Center',
  claim_common_pool_customer: 'Claim Common Pool Customer',
  assign_common_pool_customer: 'Assign Common Pool Customer',
  product_statistics_query: 'Product Statistics Query',
  product_statistics_export: 'Product Statistics Export',
  payment_channel_query: 'Payment Channel Query',
  payment_channel_create: 'Payment Channel Create',
  payment_channel_update: 'Payment Channel Update',
  payment_channel_delete: 'Payment Channel Delete',
  product_collection_query: 'Product Collection Query',
  product_browsing_query: 'Product Browsing Query',
  after_sales_approve: 'After-sales Approve',
  after_sales_reject: 'After-sales Reject',
  after_sales_confirm_return: 'After-sales Confirm Return',
  after_sales_confirm_refund: 'After-sales Confirm Refund',
  delete_project: 'Delete Project',
  member_level_record_query: 'Member Level Record Query',
  member_experience_record_query: 'Member Experience Record Query',
  ai_large_model: 'AI Large Model',
  ai_chat: 'AI Chat',
  console: 'Console',
  api_key: 'API Key',
  api_key_query: 'API Key Query',
  api_key_create: 'API Key Create',
  api_key_update: 'API Key Update',
  api_key_delete: 'API Key Delete',
  model_configuration: 'Model Configuration',
  chat_model_query: 'Chat Model Query',
  chat_model_create: 'Chat Model Create',
  chat_model_update: 'Chat Model Update',
  chat_model_delete: 'Chat Model Delete',
  chat_role: 'Chat Role',
  chat_role_query: 'Chat Role Query',
  chat_role_create: 'Chat Role Create',
  chat_role_update: 'Chat Role Update',
  chat_role_delete: 'Chat Role Delete',
  chat_management: 'Chat Management',
  session_query: 'Session Query',
  session_delete: 'Session Delete',
  message_query_chat: 'Message Query', // Differentiated
  message_delete_chat: 'Message Delete', // Differentiated
  ai_drawing: 'AI Drawing',
  drawing_management: 'Drawing Management',
  drawing_query: 'Drawing Query',
  drawing_delete: 'Drawing Delete',
  drawing_update: 'Drawing Update',
  music_management: 'Music Management',
  music_query: 'Music Query',
  music_update: 'Music Update',
  music_delete: 'Music Delete',
  ai_writing: 'AI Writing',
  writing_management: 'Writing Management',
  ai_writing_query: 'AI Writing Query',
  ai_writing_delete: 'AI Writing Delete',
  ai_music: 'AI Music',
  customer_service_center: 'Customer Service Center',
  ai_mind_map: 'AI Mind Map',
  mind_map_management: 'Mind Map Management',
  mind_map_query: 'Mind Map Query',
  mind_map_delete: 'Mind Map Delete',
  session_query_mindmap: 'Session Query', // Differentiated
  session_update_mindmap: 'Session Update', // Differentiated
  message_query_mindmap: 'Message Query', // Differentiated
  session_delete_mindmap: 'Session Delete', // Differentiated
  message_send_mindmap: 'Message Send', // Differentiated
  message_update_mindmap: 'Message Update', // Differentiated
  points_mall: 'Points Mall',
  points_mall_activity_query: 'Points Mall Activity Query',
  points_mall_activity_create: 'Points Mall Activity Create',
  points_mall_activity_update: 'Points Mall Activity Update',
  points_mall_activity_delete: 'Points Mall Activity Delete',
  points_mall_activity_export: 'Points Mall Activity Export',
  create_promoter: 'Create Promoter',
  process_cleanup: 'Process Cleanup',
  points_mall_activity_close: 'Points Mall Activity Close',
  ai_knowledge_base: 'AI Knowledge Base',
  ai_knowledge_base_query: 'AI Knowledge Base Query',
  ai_knowledge_base_create: 'AI Knowledge Base Create',
  ai_knowledge_base_update: 'AI Knowledge Base Update',
  ai_knowledge_base_delete: 'AI Knowledge Base Delete',
  tool_management: 'Tool Management',
  tool_query: 'Tool Query',
  tool_create: 'Tool Create',
  tool_update: 'Tool Update',
  tool_delete: 'Tool Delete',
  iot_internet_of_things: 'IoT (Internet of Things)',
  device_access: 'Device Access',
  product_management_iot: 'Product Management', // Differentiated
  product_query_iot: 'Product Query', // Differentiated
  product_create_iot: 'Product Create', // Differentiated
  product_update_iot: 'Product Update', // Differentiated
  product_delete_iot: 'Product Delete', // Differentiated
  product_export_iot: 'Product Export', // Differentiated
  device_management: 'Device Management',
  device_query: 'Device Query',
  device_create: 'Device Create',
  device_update: 'Device Update',
  device_delete: 'Device Delete',
  device_export: 'Device Export',
  product_category_iot: 'Product Category', // Differentiated
  product_category_query_iot: 'Product Category Query', // Differentiated
  product_category_create_iot: 'Product Category Create', // Differentiated
  product_category_update_iot: 'Product Category Update', // Differentiated
  product_category_delete_iot: 'Product Category Delete', // Differentiated
  plugin_management: 'Plugin Management',
  plugin_query: 'Plugin Query',
  plugin_create: 'Plugin Create',
  plugin_update: 'Plugin Update',
  plugin_delete: 'Plugin Delete',
  plugin_export: 'Plugin Export',
  device_group: 'Device Group',
  device_group_query: 'Device Group Query',
  device_group_create: 'Device Group Create',
  device_group_update: 'Device Group Update',
  device_group_delete: 'Device Group Delete',
  device_import: 'Device Import',
  product_thing_model: 'Product Thing Model',
  product_thing_model_function_query: 'Product Thing Model Function Query',
  product_thing_model_function_create: 'Product Thing Model Function Create',
  product_thing_model_function_update: 'Product Thing Model Function Update',
  product_thing_model_function_delete: 'Product Thing Model Function Delete',
  product_thing_model_function_export: 'Product Thing Model Function Export',
  device_uplink: 'Device Uplink',
  device_attribute_query: 'Device Attribute Query',
  device_log_query: 'Device Log Query',
  device_downlink: 'Device Downlink',
  ops_management: 'Ops Management',
  rule_engine: 'Rule Engine',
  scene_linkage: 'Scene Linkage',
  iot_home_page: 'IoT Home Page',
  data_bridge: 'Data Bridge',
  iot_data_bridge_query: 'IoT Data Bridge Query',
  iot_data_bridge_create: 'IoT Data Bridge Create',
  iot_data_bridge_update: 'IoT Data Bridge Update',
  iot_data_bridge_delete: 'IoT Data Bridge Delete',
  iot_data_bridge_export: 'IoT Data Bridge Export',
  ai_workflow: 'AI Workflow',
  ai_workflow_query: 'AI Workflow Query',
  ai_workflow_create: 'AI Workflow Create',
  ai_workflow_update: 'AI Workflow Update',
  ai_workflow_delete: 'AI Workflow Delete',
  ai_workflow_test: 'AI Workflow Test',
  workbench: 'Workbench',
  vehicle_management: 'Vehicle Management',
  site_management: 'Site Management',
  order_management: 'Order Management',
  vehicle_delete: 'Vehicle Delete',
  vehicle_add: 'Vehicle Add',
  vehicle_export: 'Vehicle Export',
  vehicle_update: 'Vehicle Update',
  vehicle_unbind: 'Vehicle Unbind',
  edit: 'Edit',
  enable_disable: 'Enable/Disable',
  show_hide: 'Show/Hide',
  details: 'Details',
  import: 'Import',
};

