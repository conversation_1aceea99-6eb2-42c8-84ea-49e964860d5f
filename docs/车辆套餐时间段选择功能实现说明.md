# 车辆套餐时间段选择功能实现说明

## 功能概述

本功能为车辆套餐配置表单添加了可视化的时间段选择器，支持24小时时间轴拖拽选择、预设时段快捷选择、以及完整的前后端数据验证。

## 核心文件

### 前端文件
- `src/components/TimeSlotSelector/index.vue` - 时间段选择器组件
- `src/views/pay/vehicle/package/VehiclePackageForm.vue` - 车辆套餐表单（已集成）
- `src/views/pay/vehicle/package/TimeSlotDemo.vue` - 演示页面
- `src/api/pay/vehicle/package/index.ts` - API类型定义（已更新）

### 后端文件
- `yudao-module-pay-server/src/main/java/cn/iocoder/yudao/module/pay/util/TimeSlotUtils.java` - 时间段工具类
- `yudao-module-pay-server/src/main/java/cn/iocoder/yudao/module/pay/service/vehicle/PayVehiclePackageServiceImpl.java` - 服务层（已集成验证）
- `yudao-module-pay-api/src/main/java/cn/iocoder/yudao/module/pay/enums/ErrorCodeConstants.java` - 错误码（已添加）

### 测试文件
- `yudao-module-pay-server/src/test/java/cn/iocoder/yudao/module/pay/util/TimeSlotUtilsTest.java` - 单元测试
- `yudao-module-pay-server/src/test/java/cn/iocoder/yudao/module/pay/util/SimpleTimeSlotTest.java` - 简单测试

## 功能特性

### 1. 时间段选择器组件 (TimeSlotSelector)

#### 核心功能
- ✅ 24小时时间轴可视化界面
- ✅ 鼠标拖拽选择时间段
- ✅ 多个不连续时间段支持
- ✅ 自动合并重叠时间段
- ✅ 选中时段标签展示
- ✅ 单独删除时间段功能

#### 预设时段
- **峰时段**: 09:00-22:00 (泰国电价峰时段)
- **平时段**: 06:00-09:00, 22:00-24:00 (泰国电价平时段)
- **谷时段**: 00:00-06:00 (泰国电价谷时段)
- **全天**: 00:00-24:00 (全天可用)

#### 使用方法
```vue
<template>
  <TimeSlotSelector 
    v-model="timeSlots"
    @change="handleTimeSlotsChange"
  />
</template>

<script setup>
import TimeSlotSelector from '@/components/TimeSlotSelector/index.vue'

const timeSlots = ref([])

const handleTimeSlotsChange = (slots) => {
  console.log('选中的时间段:', slots)
}
</script>
```

### 2. 数据格式

#### JSON存储格式
```json
{
  "timeSlots": [
    {
      "startTime": "08:00",
      "endTime": "12:00"
    },
    {
      "startTime": "14:00",
      "endTime": "18:00"
    }
  ]
}
```

#### TypeScript类型定义
```typescript
interface TimeSlot {
  startTime: string  // 格式: HH:mm
  endTime: string    // 格式: HH:mm
}

interface TimeSlotCollection {
  timeSlots: TimeSlot[]
}
```

### 3. 后端工具类 (TimeSlotUtils)

#### 主要方法
```java
// JSON解析
List<TimeSlot> parseTimeSlots(String jsonStr)

// JSON生成
String toJsonString(List<TimeSlot> timeSlots)

// 验证时间段
String validateTimeSlots(List<TimeSlot> timeSlots)

// 判断时间是否在可用时段内
boolean isTimeInAvailableSlots(String currentTime, String timeSlotsJson)

// 合并重叠时间段
List<TimeSlot> mergeOverlappingSlots(List<TimeSlot> timeSlots)
```

#### 验证规则
- 时间格式必须为 HH:mm
- 开始时间必须小于结束时间
- 时间段不能重叠
- 支持24:00格式（表示一天结束）

### 4. 集成到车辆套餐表单

#### 表单字段更新
```vue
<!-- 原来的textarea输入 -->
<el-form-item label="可用时段" prop="availableTimes">
  <el-input
    v-model="formData.availableTimes"
    type="textarea"
    placeholder="请输入可用时段，格式：08:00-18:00,19:00-22:00"
    :rows="3"
  />
</el-form-item>

<!-- 新的时间段选择器 -->
<el-form-item label="可用时段" prop="availableTimes">
  <TimeSlotSelector 
    v-model="timeSlots"
    @change="handleTimeSlotsChange"
  />
</el-form-item>
```

#### 数据处理
```javascript
// 时间段变化处理
const handleTimeSlotsChange = (slots) => {
  timeSlots.value = slots
  if (slots.length > 0) {
    formData.value.availableTimes = JSON.stringify({ timeSlots: slots })
  } else {
    formData.value.availableTimes = ''
  }
}

// 解析已有数据
const parseTimeSlots = (timeSlotsStr) => {
  if (!timeSlotsStr || timeSlotsStr.trim() === '') {
    return []
  }
  try {
    const parsed = JSON.parse(timeSlotsStr)
    return parsed.timeSlots || []
  } catch (error) {
    console.error('解析时间段JSON失败:', error)
    return []
  }
}
```

## 验证和错误处理

### 前端验证
- 实时格式检查
- 重叠检测
- 用户友好的错误提示

### 后端验证
- Service层集成验证
- 专用错误码: `VEHICLE_PACKAGE_TIME_SLOTS_INVALID`
- 详细的错误信息返回

### 错误码定义
```java
ErrorCode VEHICLE_PACKAGE_TIME_SLOTS_INVALID = new ErrorCode(1_007_010_003, "车辆套餐可用时段格式错误：{}");
```

## 测试

### 单元测试
运行 `TimeSlotUtilsTest.java` 进行完整的单元测试，包括：
- JSON解析和生成
- 时间段验证
- 重叠检测
- 24:00格式处理
- 时间段合并

### 手动测试
运行 `SimpleTimeSlotTest.java` 进行基本功能验证。

### 前端演示
访问 `TimeSlotDemo.vue` 页面进行可视化测试。

## 部署说明

1. 确保前端依赖已安装（dayjs已在package.json中）
2. 后端无需额外依赖，使用现有的hutool和jackson
3. 数据库字段 `available_times` 保持String类型，存储JSON格式数据

## 兼容性

- ✅ 与现有车辆套餐功能完全兼容
- ✅ 支持数据回显和编辑
- ✅ 保持原有表单验证机制
- ✅ 向后兼容旧的数据格式

## 技术栈

- **前端**: Vue 3 + Composition API + Element Plus + dayjs
- **后端**: Spring Boot + hutool + jackson
- **数据存储**: JSON格式字符串
- **测试**: JUnit 5 + Mockito
