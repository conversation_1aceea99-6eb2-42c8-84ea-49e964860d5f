# GoogleMap标题与全屏按钮同行布局优化

## 优化目标

将GoogleMap组件的标题（property.title）与全屏按钮放在同一行显示，提高空间利用率和视觉效果。

## 实现方案

### 1. 布局结构调整

#### 优化前的结构
```vue
<div v-if="property.showTitle" class="map-title">
  {{ property.title }}
</div>
<div class="map-toolbar">
  <button class="fullscreen-btn">全屏</button>
</div>
```

#### 优化后的结构
```vue
<!-- 标题和工具栏同行 -->
<div v-if="property.showTitle" class="map-header">
  <div class="map-title">
    {{ property.title }}
  </div>
  <button class="fullscreen-btn">
    <el-icon size="16"><FullScreen /></el-icon>
    <span class="btn-text">全屏</span>
  </button>
</div>

<!-- 如果没有标题但需要显示全屏按钮 -->
<div v-if="!property.showTitle" class="map-toolbar">
  <button class="fullscreen-btn">
    <el-icon size="16"><FullScreen /></el-icon>
    <span class="btn-text">全屏</span>
  </button>
</div>
```

### 2. CSS样式实现

#### 新增的同行布局样式
```scss
// 标题和按钮同行布局
.map-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  
  .map-title {
    font-size: 16px;
    font-weight: bold;
    margin: 0;
    flex: 1;
  }
  
  .fullscreen-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    color: #495057;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
    margin-left: 12px;
    
    &:hover {
      background: #e9ecef;
      border-color: #dee2e6;
      color: #212529;
    }
    
    &:active {
      background: #dee2e6;
      transform: translateY(1px);
    }
    
    .btn-text {
      font-size: 12px;
      font-weight: 500;
    }
  }
}
```

#### 保留的独立样式
```scss
// 独立的工具栏样式（当没有标题时）
.map-toolbar {
  margin-bottom: 8px;
  display: flex;
  justify-content: flex-end;
  
  .fullscreen-btn {
    // 相同的按钮样式
  }
}
```

## 设计特点

### 1. 响应式布局
- **Flex布局**: 使用`display: flex`实现水平排列
- **空间分配**: 标题使用`flex: 1`占据剩余空间
- **按钮固定**: 按钮使用`flex-shrink: 0`保持固定大小

### 2. 视觉对齐
- **垂直居中**: 使用`align-items: center`确保标题和按钮垂直居中
- **水平分布**: 使用`justify-content: space-between`实现两端对齐
- **间距控制**: 按钮添加`margin-left: 12px`确保与标题的间距

### 3. 兼容性处理
- **条件渲染**: 根据是否显示标题选择不同的布局结构
- **样式复用**: 按钮样式在两种布局中保持一致
- **向下兼容**: 保留原有的独立工具栏样式

## 布局效果

### 有标题时的布局
```
┌─────────────────────────────────────────┐
│ 换电站地图                    [📍 全屏] │
├─────────────────────────────────────────┤
│ 地图描述信息（如果有）                   │
├─────────────────────────────────────────┤
│                                         │
│           地图内容区域                   │
│                                         │
└─────────────────────────────────────────┘
```

### 无标题时的布局
```
┌─────────────────────────────────────────┐
│                           [📍 全屏] │
├─────────────────────────────────────────┤
│ 地图描述信息（如果有）                   │
├─────────────────────────────────────────┤
│                                         │
│           地图内容区域                   │
│                                         │
└─────────────────────────────────────────┘
```

## 优势分析

### 1. 空间利用率提升
- **垂直空间节省**: 减少了一行的垂直空间占用
- **水平空间优化**: 充分利用标题行的水平空间
- **整体紧凑**: 组件整体更加紧凑美观

### 2. 用户体验改善
- **视觉关联**: 标题和功能按钮在同一行，关联性更强
- **操作便捷**: 全屏按钮位置更加显眼，易于发现
- **界面整洁**: 减少了界面元素的分散，更加整洁

### 3. 设计一致性
- **对齐规范**: 遵循左对齐标题、右对齐操作的设计规范
- **视觉平衡**: 标题和按钮形成良好的视觉平衡
- **层次清晰**: 保持了信息层次的清晰性

## 技术实现要点

### 1. 条件渲染逻辑
```vue
<!-- 有标题时使用同行布局 -->
<div v-if="property.showTitle" class="map-header">
  <!-- 标题和按钮 -->
</div>

<!-- 无标题时使用独立工具栏 -->
<div v-if="!property.showTitle" class="map-toolbar">
  <!-- 只有按钮 -->
</div>
```

### 2. Flex布局关键属性
- `justify-content: space-between`: 两端对齐
- `align-items: center`: 垂直居中
- `flex: 1`: 标题占据剩余空间
- `flex-shrink: 0`: 按钮保持固定大小

### 3. 样式继承和复用
- 按钮样式在两种布局中保持一致
- 通过CSS嵌套确保样式的正确应用
- 保持原有的交互效果和视觉反馈

## 测试要点

### 1. 布局测试
- 验证有标题时的同行显示效果
- 确认无标题时的独立工具栏显示
- 测试不同长度标题的显示效果

### 2. 响应式测试
- 测试在不同屏幕宽度下的表现
- 验证标题过长时的处理方式
- 确认按钮在各种情况下的可点击性

### 3. 兼容性测试
- 验证在不同浏览器中的显示效果
- 测试与现有配置的兼容性
- 确认不影响其他功能的正常使用

## 总结

通过将标题与全屏按钮放在同一行，GoogleMap组件实现了更好的空间利用率和用户体验。这个改进既保持了功能的完整性，又提升了界面的美观性和实用性。同时，通过条件渲染确保了在各种配置下都能正确显示，体现了良好的设计考虑。
