# GoogleMap 站点图标配置说明

## 功能概述

GoogleMap 组件现在支持通过文件上传来配置站点图标，用户可以根据站点状态（营业中、暂停营业、繁忙）上传不同的图标，并控制图标在地图上的显示大小和位置。

## 配置选项

### 1. 站点图标上传

在换电站设置中，新增了以下图标配置选项：

- **营业中图标**：用于显示状态为"营业中"的站点
- **暂停营业图标**：用于显示状态为"暂停营业"的站点  
- **繁忙状态图标**：用于显示状态为"繁忙"的站点

### 2. 图标大小控制

- **宽度**：图标宽度，范围 16-64 像素
- **高度**：图标高度，范围 16-64 像素

### 3. 图标锚点位置

- **X偏移**：图标水平偏移量，通常设置为图标宽度的一半
- **Y偏移**：图标垂直偏移量，通常设置为图标高度

## 使用方法

### 1. 上传图标

1. 在装修编辑器中添加 GoogleMap 组件
2. 启用"显示换电站"选项
3. 在"站点图标设置"部分，点击对应状态的上传按钮
4. 选择图片文件（支持 PNG、JPG、GIF 等格式，大小不超过 2MB）
5. 上传成功后，图标预览会显示在输入框下方

### 2. 调整图标大小

1. 在"图标大小"设置中调整宽度和高度
2. 建议使用正方形图标，如 32x32 或 48x48 像素
3. 图标大小会影响在地图上的显示效果

### 3. 设置锚点位置

1. 锚点决定了图标相对于地图坐标点的位置
2. X偏移：正值向右偏移，负值向左偏移
3. Y偏移：正值向下偏移，负值向上偏移
4. 通常设置：
   - X = 图标宽度的一半（图标中心对齐）
   - Y = 图标高度（图标底部对齐到坐标点）

## 图标设计建议

### 1. 尺寸规格

- 推荐尺寸：32x32 或 48x48 像素
- 格式：PNG（支持透明背景）
- 文件大小：不超过 2MB

### 2. 颜色区分

- **营业中**：绿色系图标
- **暂停营业**：红色系图标
- **繁忙**：黄色或橙色系图标

### 3. 设计原则

- 简洁明了，易于识别
- 保持三种状态图标风格一致
- 考虑在不同背景下的可见性
- 支持透明背景，适应地图样式

## 技术实现

### 1. 数据结构

```typescript
interface GoogleMapProperty {
  // ... 其他配置
  stationIcons: {
    online: string    // 营业中图标URL
    offline: string   // 暂停营业图标URL
    busy: string      // 繁忙状态图标URL
  }
  stationIconSize: {
    width: number     // 图标宽度
    height: number    // 图标高度
  }
  stationIconAnchor: {
    x: number         // X轴锚点偏移
    y: number         // Y轴锚点偏移
  }
}
```

### 2. 图标选择逻辑

```javascript
// 根据站点状态选择对应的图标
let iconUrl = props.data.stationIcons?.online || props.data.stationIcon
if (station.status === 0) {
  iconUrl = props.data.stationIcons?.offline || props.data.stationIcon
} else if (station.status === 2) {
  iconUrl = props.data.stationIcons?.busy || props.data.stationIcon
}
```

### 3. 图标渲染

```javascript
const icon = {
  url: iconUrl,
  scaledSize: new google.maps.Size(width, height),
  anchor: new google.maps.Point(anchorX, anchorY)
}

const marker = new google.maps.Marker({
  position: { lat, lng },
  map: map,
  icon: icon
})
```

## 注意事项

1. **兼容性**：新功能向后兼容，未配置自定义图标时使用默认图标
2. **性能**：图标文件过大可能影响加载速度，建议优化图片大小
3. **缓存**：上传的图标会创建本地URL，页面刷新后需要重新上传
4. **响应式**：图标大小设置会影响在不同设备上的显示效果

## 故障排除

### 1. 图标不显示

- 检查图标URL是否有效
- 确认图标文件格式支持
- 验证图标大小设置是否合理

### 2. 图标位置偏移

- 调整锚点设置
- 检查图标尺寸是否与锚点匹配
- 确认坐标点位置是否正确

### 3. 图标大小异常

- 检查图标尺寸设置
- 确认图标文件本身尺寸
- 验证地图缩放级别设置 