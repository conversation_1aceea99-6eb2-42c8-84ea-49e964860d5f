<template>
  <div
    class="google-map-container"
    :style="{
      backgroundColor: property.style.bgColor,
      borderRadius: property.style.borderRadius + 'px'
    }"
  >
    <!-- 标题和工具栏同行 -->
    <div v-if="property.showTitle" class="map-header">
      <div class="map-title" :style="{ color: property.style.titleColor }">
        {{ property.title }}
      </div>
      <button class="fullscreen-btn" @click="openFullscreen">
        <el-icon size="16"><FullScreen /></el-icon>
        <span class="btn-text">全屏</span>
      </button>
    </div>

    <!-- 如果没有标题但需要显示全屏按钮 -->
    <div v-if="!property.showTitle" class="map-toolbar">
      <button class="fullscreen-btn" @click="openFullscreen">
        <el-icon size="16"><FullScreen /></el-icon>
        <span class="btn-text">全屏</span>
      </button>
    </div>

    <div v-if="property.showLocationDesc" class="map-desc" :style="{ color: property.style.descColor }">
      {{ property.locationDesc }}
    </div>

    <!-- 地图容器 -->
    <div ref="mapRef" class="map-container" :style="{ height: property.height + 'px' }"></div>

    <!-- 站点信息弹窗 -->
    <el-dialog v-model="stationInfoVisible" title="站点详情" width="400px">
      <div v-if="selectedStation" class="station-detail">
        <p><strong>站点名称：</strong>{{ selectedStation.siteName }}</p>
        <p><strong>详细地址：</strong>{{ selectedStation.address }}</p>
        <p><strong>运营状态：</strong>{{ getStatusText(selectedStation.status) }}</p>
        <p><strong>可用电池：</strong>{{ selectedStation.replaceCarCtn }}个</p>
        <p><strong>当前电价：</strong>¥{{ selectedStation.nowPrice }}/kWh</p>
        <p><strong>距离：</strong>{{ selectedStation.distance }}米</p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="stationInfoVisible = false">关闭</el-button>
          <el-button type="primary" @click="navigateToStation">导航</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 全屏地图对话框 -->
    <el-dialog v-model="fullscreenVisible" title="地图全览" width="90%" top="5vh" @opened="initFullscreenMap" @closed="destroyFullscreenMap">
      <div ref="fullscreenMapRef" class="fullscreen-map"></div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { GoogleMapProperty } from './config'
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import { FullScreen } from '@element-plus/icons-vue'
import { MarkerClusterer } from '@googlemaps/markerclusterer'

/** 谷歌地图组件 */
defineOptions({ name: 'GoogleMap' })

const props = defineProps<{ property: GoogleMapProperty }>()
const mapRef = ref<HTMLElement | null>(null)
const fullscreenMapRef = ref<HTMLElement | null>(null)
let map: any = null
let googleMapScript: HTMLScriptElement | null = null

// 换电站相关状态
const stationInfoVisible = ref(false)
const selectedStation = ref<any>(null)
const stationMarkers: any[] = []
let markerClusterer: MarkerClusterer | null = null

// 全屏相关状态
const fullscreenVisible = ref(false)
let fullscreenMap: any = null
const fullscreenStationMarkers: any[] = []
let fullscreenMarkerClusterer: MarkerClusterer | null = null

// 创建专业的换电站图标
const createStationIcon = (station: any) => {
  const getIconColor = (status: number) => {
    switch (status) {
      case 1: return '#4CAF50' // 正常运营
      case 2: return '#FF9800' // 维护中
      case 3: return '#F44336' // 故障
      default: return '#9E9E9E' // 未知状态
    }
  }

  const iconElement = document.createElement('div')
  iconElement.className = 'station-marker'
  iconElement.innerHTML = `
    <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
      <circle cx="16" cy="16" r="14" fill="${getIconColor(station.status)}" stroke="#fff" stroke-width="2"/>
      <path d="M12 10h8v4h-2v6h-4v-6h-2z" fill="#fff"/>
      <circle cx="16" cy="22" r="2" fill="#fff"/>
    </svg>
  `
  iconElement.style.cursor = 'pointer'
  iconElement.title = station.siteName
  return iconElement
}

// 创建聚合图标
const createClusterIcon = (count: number) => {
  const clusterElement = document.createElement('div')
  clusterElement.className = 'cluster-marker'
  clusterElement.innerHTML = `
    <div style="
      background: #1976D2;
      color: white;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 14px;
      border: 2px solid white;
      box-shadow: 0 2px 6px rgba(0,0,0,0.3);
      cursor: pointer;
    ">${count}</div>
  `
  return clusterElement
}

// 获取状态文本
const getStatusText = (status: number) => {
  switch (status) {
    case 1: return '正常运营'
    case 2: return '维护中'
    case 3: return '故障'
    default: return '未知状态'
  }
}

// 显示站点信息
const showStationInfo = (station: any) => {
  selectedStation.value = station
  stationInfoVisible.value = true
}

// 获取站点列表
const getStationList = async () => {
  try {
    // 在管理后台使用模拟数据展示效果
    return [
      {
        siteNo: 'DEMO001',
        siteName: '示例换电站1',
        address: '上海市浦东新区张江高科技园区',
        latitude: 31.23,
        longitude: 121.5,
        status: 1,
        distance: 1200,
        replaceCarCtn: 15,
        nowPrice: 1.2
      },
      {
        siteNo: 'DEMO002', 
        siteName: '示例换电站2',
        address: '上海市徐汇区漕河泾开发区',
        latitude: 31.18,
        longitude: 121.43,
        status: 2,
        distance: 2500,
        replaceCarCtn: 8,
        nowPrice: 1.3
      },
      {
        siteNo: 'DEMO003',
        siteName: '示例换电站3', 
        address: '上海市杨浦区五角场',
        latitude: 31.30,
        longitude: 121.52,
        status: 1,
        distance: 3200,
        replaceCarCtn: 12,
        nowPrice: 1.1
      }
    ]
  } catch (error) {
    console.error('获取站点列表失败:', error)
    return []
  }
}

// 加载谷歌地图API
const loadGoogleMapAPI = () => {
  return new Promise<void>((resolve, reject) => {
    try {
      // 检查是否已经加载过
      if (window.google && window.google.maps) {
        resolve()
        return
      }

      // 创建script标签
      googleMapScript = document.createElement('script')
      googleMapScript.src = `https://maps.googleapis.com/maps/api/js?key=AIzaSyArla_C7JZ6kpEFq9ojEK4YRFg6h8uhoQA&libraries=marker,geometry&callback=initGoogleMap`
      googleMapScript.async = true
      googleMapScript.defer = true

      // 在window上定义回调函数
      window.initGoogleMap = () => {
        resolve()
      }

      // 处理加载失败
      googleMapScript.onerror = () => {
        reject(new Error('Google Maps API加载失败'))
      }

      // 添加到文档中
      document.head.appendChild(googleMapScript)
    } catch (error) {
      reject(error)
    }
  })
}

// 初始化地图
const initMap = () => {
  if (!mapRef.value || !window.google?.maps) return

  // 创建地图
  map = new window.google.maps.Map(mapRef.value, {
    center: { lat: props.property.lat, lng: props.property.lng },
    zoom: props.property.zoom,
    disableDefaultUI: false,
    zoomControl: true,
    mapTypeControl: true,
    streetViewControl: false,
    fullscreenControl: false,
    rotateControl: true,
    scaleControl: true,
    mapId: 'DEMO_MAP_ID'
  })

  // 如果启用换电站显示，加载站点数据
  if (props.property.showStations) {
    loadStationMarkers()
  }
}

// 加载站点标记
const loadStationMarkers = async () => {
  try {
    const stations = await getStationList()
    renderStationMarkers(stations)
  } catch (error) {
    console.error('加载站点数据失败:', error)
  }
}

// 渲染站点标记（优化版本）
const renderStationMarkers = (stations: any[]) => {
  // 清除现有标记和聚合器
  if (markerClusterer) {
    markerClusterer.clearMarkers()
    markerClusterer = null
  }
  stationMarkers.forEach(marker => marker.map = null)
  stationMarkers.length = 0

  if (!stations.length) return

  // 创建新标记
  stations.forEach(station => {
    const marker = new window.google.maps.marker.AdvancedMarkerElement({
      map: map,
      position: { lat: station.latitude, lng: station.longitude },
      title: station.siteName,
      content: createStationIcon(station)
    })

    // 添加点击事件
    marker.addListener('click', () => {
      showStationInfo(station)
    })

    stationMarkers.push(marker)
  })

  // 创建标记聚合器
  if (stationMarkers.length > 1) {
    markerClusterer = new MarkerClusterer({
      map,
      markers: stationMarkers,
      algorithm: new SuperClusterAlgorithm({
        radius: 100,
        maxZoom: 15
      }),
      renderer: {
        render: ({ count, position }) => {
          return new window.google.maps.marker.AdvancedMarkerElement({
            position,
            content: createClusterIcon(count),
            zIndex: 1000 + count
          })
        }
      }
    })
  }

  // 自动调整视野
  if (props.property.autoCenter && stations.length > 0) {
    const bounds = new window.google.maps.LatLngBounds()
    stations.forEach(station => {
      bounds.extend({ lat: station.latitude, lng: station.longitude })
    })
    map.fitBounds(bounds)
  }
}

// 导航到站点
const navigateToStation = () => {
  if (!selectedStation.value) return

  const { latitude, longitude } = selectedStation.value
  const url = `https://www.google.com/maps/dir/?api=1&destination=${latitude},${longitude}`
  window.open(url, '_blank')
}

// 打开全屏模式
const openFullscreen = () => {
  fullscreenVisible.value = true
}

// 初始化全屏地图
const initFullscreenMap = async () => {
  await nextTick()
  if (!fullscreenMapRef.value || !window.google?.maps) return

  // 创建全屏地图实例
  fullscreenMap = new window.google.maps.Map(fullscreenMapRef.value, {
    center: map ? map.getCenter() : { lat: props.property.lat, lng: props.property.lng },
    zoom: map ? map.getZoom() : props.property.zoom,
    disableDefaultUI: false,
    zoomControl: true,
    fullscreenControl: true,
    streetViewControl: true,
    mapTypeControl: true,
    mapId: 'DEMO_MAP_ID'
  })

  // 复制站点标记到全屏地图
  if (props.property.showStations) {
    await loadFullscreenStationMarkers()
  }
}

// 加载全屏地图站点标记
const loadFullscreenStationMarkers = async () => {
  try {
    const stations = await getStationList()
    renderFullscreenStationMarkers(stations)
  } catch (error) {
    console.error('加载全屏地图站点数据失败:', error)
  }
}

// 渲染全屏地图站点标记
const renderFullscreenStationMarkers = (stations: any[]) => {
  // 清除现有标记和聚合器
  if (fullscreenMarkerClusterer) {
    fullscreenMarkerClusterer.clearMarkers()
    fullscreenMarkerClusterer = null
  }
  fullscreenStationMarkers.forEach(marker => marker.map = null)
  fullscreenStationMarkers.length = 0

  if (!stations.length) return

  // 创建新标记
  stations.forEach(station => {
    const marker = new window.google.maps.marker.AdvancedMarkerElement({
      map: fullscreenMap,
      position: { lat: station.latitude, lng: station.longitude },
      title: station.siteName,
      content: createStationIcon(station)
    })

    // 添加点击事件
    marker.addListener('click', () => {
      showStationInfo(station)
    })

    fullscreenStationMarkers.push(marker)
  })

  // 创建标记聚合器
  if (fullscreenStationMarkers.length > 1) {
    fullscreenMarkerClusterer = new MarkerClusterer({
      map: fullscreenMap,
      markers: fullscreenStationMarkers,
      algorithm: new SuperClusterAlgorithm({
        radius: 100,
        maxZoom: 15
      }),
      renderer: {
        render: ({ count, position }) => {
          return new window.google.maps.marker.AdvancedMarkerElement({
            position,
            content: createClusterIcon(count),
            zIndex: 1000 + count
          })
        }
      }
    })
  }

  // 自动调整视野
  if (stations.length > 0) {
    const bounds = new window.google.maps.LatLngBounds()
    stations.forEach(station => {
      bounds.extend({ lat: station.latitude, lng: station.longitude })
    })
    fullscreenMap.fitBounds(bounds)
  }
}

// 销毁全屏地图
const destroyFullscreenMap = () => {
  if (fullscreenMarkerClusterer) {
    fullscreenMarkerClusterer.clearMarkers()
    fullscreenMarkerClusterer = null
  }
  if (fullscreenMap) {
    fullscreenStationMarkers.forEach(marker => marker.map = null)
    fullscreenStationMarkers.length = 0
    fullscreenMap = null
  }
}

// 更新地图
const updateMap = () => {
  if (!map) return
  
  const latLng = { lat: props.property.lat, lng: props.property.lng }
  map.setCenter(latLng)
  map.setZoom(props.property.zoom)
}

// 监听属性变化
watch(
  () => [props.property.lat, props.property.lng, props.property.zoom],
  () => {
    updateMap()
  }
)

// 监听换电站配置变化
watch(
  () => props.property.showStations,
  (newVal) => {
    if (newVal && map) {
      loadStationMarkers()
    } else {
      // 清除站点标记
      if (markerClusterer) {
        markerClusterer.clearMarkers()
        markerClusterer = null
      }
      stationMarkers.forEach(marker => marker.map = null)
      stationMarkers.length = 0
    }
  }
)

// 监听站点图标配置变化
watch(
  () => [props.property.stationIcons, props.property.stationIconSize, props.property.stationIconAnchor],
  () => {
    if (props.property.showStations && map && stationMarkers.length > 0) {
      // 重新加载站点标记以应用新的图标配置
      loadStationMarkers()
    }
  },
  { deep: true }
)

onMounted(async () => {
  try {
    await loadGoogleMapAPI()
    initMap()
  } catch (e) {
    console.error('加载谷歌地图失败:', e)
  }
})

onBeforeUnmount(() => {
  // 清理资源
  if (markerClusterer) {
    markerClusterer.clearMarkers()
  }
  if (fullscreenMarkerClusterer) {
    fullscreenMarkerClusterer.clearMarkers()
  }
  if (googleMapScript) {
    document.head.removeChild(googleMapScript)
  }
})
</script>

<script lang="ts">
// 为TypeScript声明全局Google Maps变量
declare global {
  interface Window {
    google: any;
    initGoogleMap: () => void;
  }
}
</script>

<style scoped lang="scss">
.google-map-container {
  width: 100%;
  padding: 12px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 10px;
}

.map-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.map-toolbar {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 8px;
}

.map-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.fullscreen-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: #1976D2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;

  &:hover {
    background: #1565C0;
  }

  .btn-text {
    font-size: 12px;
  }
}

.map-desc {
  font-size: 14px;
  margin-bottom: 8px;
  line-height: 1.4;
}

.map-container {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e0e0e0;
}

.fullscreen-map {
  width: 100%;
  height: 70vh;
  border-radius: 8px;
}

.station-detail {
  p {
    margin: 8px 0;
    line-height: 1.5;
  }
}

// 标记样式
:deep(.station-marker) {
  transition: transform 0.2s ease;
  
  &:hover {
    transform: scale(1.1);
  }
}

:deep(.cluster-marker) {
  transition: transform 0.2s ease;
  
  &:hover {
    transform: scale(1.1);
  }
}
</style>
