# GoogleMap组件全屏模式实现说明

## 概述

为GoogleMap组件增加了全屏查看功能，用户可以点击全屏按钮在更大的视野中查看地图和站点标记，并且在全屏模式下点击站点标记同样可以查看站点详情。

## 实现方案

### 技术选型
- **管理端**: 使用Element Plus的el-dialog组件实现全屏模态弹窗
- **移动端**: 使用uni-app的su-popup组件实现全屏弹窗
- **地图实例**: 在全屏模式下重新创建Google Maps实例以确保正确渲染
- **状态同步**: 保持原地图的中心点、缩放级别等状态

### 核心功能

#### 1. 全屏入口
- **工具栏按钮**: 在地图上方提供"全屏查看"按钮
- **浮动按钮**: 在地图右上角提供全屏图标按钮
- **双重入口**: 提供两种方式方便用户操作

#### 2. 全屏地图
- **大尺寸显示**: 管理端90vw×70vh，移动端95vw×85vh
- **完整功能**: 包含缩放、地图类型切换等控件
- **站点标记**: 完整复制原地图的所有站点标记
- **交互一致**: 点击站点标记显示相同的详情弹窗

#### 3. 状态管理
- **地图状态同步**: 全屏地图继承原地图的中心点和缩放级别
- **标记状态同步**: 根据站点状态显示对应的图标
- **自动适配**: 如果启用自动居中，全屏地图自动调整视野包含所有站点

## 文件修改清单

### 管理端组件
**文件**: `src/components/DiyEditor/components/mobile/GoogleMap/index.vue`

**主要修改**:
1. 模板部分：
   - 添加地图工具栏和全屏按钮
   - 添加全屏地图对话框

2. 脚本部分：
   - 导入FullScreen图标
   - 添加全屏相关状态变量
   - 实现全屏地图初始化和销毁函数
   - 实现站点标记复制和渲染

3. 样式部分：
   - 工具栏和全屏按钮样式
   - 全屏对话框样式

### 移动端组件
**文件**: `sheep/components/s-google-map/s-google-map.vue`

**主要修改**:
1. 模板部分：
   - 添加地图工具栏和全屏按钮
   - 添加全屏地图弹窗

2. 脚本部分：
   - 添加全屏相关状态变量
   - 实现全屏地图初始化和销毁函数
   - 实现站点标记复制和渲染
   - 适配移动端交互特性

3. 样式部分：
   - 移动端适配的工具栏样式
   - 全屏容器和地图样式

## 核心函数说明

### 全屏控制函数
```javascript
// 打开全屏模式
const openFullscreen = () => {
  fullscreenVisible.value = true
}

// 初始化全屏地图
const initFullscreenMap = async () => {
  // 创建全屏地图实例
  // 复制站点标记
  // 设置事件监听
}

// 销毁全屏地图
const destroyFullscreenMap = () => {
  // 清理标记
  // 释放地图实例
}
```

### 站点标记复制
```javascript
// 渲染全屏地图站点标记
const renderFullscreenStationMarkers = (stations) => {
  // 清除现有标记
  // 计算图标大小
  // 创建新标记
  // 添加点击事件
  // 自动调整视野
}
```

## 用户体验优化

### 1. 响应式设计
- 管理端和移动端使用不同的弹窗组件
- 适配不同屏幕尺寸的显示效果
- 保持一致的交互体验

### 2. 性能优化
- 全屏地图在关闭时完全销毁，避免内存泄漏
- 按需创建地图实例，不影响原地图性能
- 标记复制采用高效的渲染策略

### 3. 交互优化
- 提供多种全屏入口方式
- 保持原有的站点详情查看功能
- 支持地图控件的完整功能

## 兼容性说明

### 浏览器兼容性
- 支持现代浏览器的Google Maps API
- 兼容移动端浏览器环境
- 适配uni-app框架特性

### 功能兼容性
- 完全兼容现有的站点标记功能
- 保持原有的配置项和样式设置
- 不影响现有的地图交互功能

## 使用说明

### 用户操作
1. 在地图组件中点击"全屏查看"按钮或右上角全屏图标
2. 在全屏模式下可以进行地图缩放、拖拽等操作
3. 点击站点标记查看站点详情
4. 点击关闭按钮或遮罩层退出全屏模式

### 开发配置
- 无需额外配置，功能自动启用
- 继承原有的站点显示和图标配置
- 支持所有现有的地图属性设置

## 测试建议

### 功能测试
1. 测试全屏按钮的点击响应
2. 验证全屏地图的正确显示
3. 测试站点标记的点击交互
4. 验证地图控件的功能完整性

### 兼容性测试
1. 测试不同浏览器的显示效果
2. 验证移动端的触摸交互
3. 测试不同屏幕尺寸的适配

### 性能测试
1. 验证内存使用情况
2. 测试多次开关全屏的稳定性
3. 检查地图加载性能

## 后续优化方向

1. **动画效果**: 添加全屏切换的过渡动画
2. **快捷键支持**: 支持ESC键退出全屏
3. **手势支持**: 移动端支持双指缩放等手势
4. **离线支持**: 考虑离线地图的全屏显示
5. **自定义控件**: 在全屏模式下添加更多自定义控件
