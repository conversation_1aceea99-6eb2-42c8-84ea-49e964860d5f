# GoogleMap 站点图标使用示例

## 示例配置

以下是一个完整的 GoogleMap 组件配置示例，展示了如何设置站点图标：

```json
{
  "id": "GoogleMap",
  "name": "谷歌地图",
  "property": {
    "title": "换电站地图",
    "locationDesc": "上海市浦东新区陆家嘴",
    "lng": 121.5,
    "lat": 31.23,
    "zoom": 15,
    "height": 400,
    "showTitle": true,
    "showLocationDesc": true,
    "style": {
      "bgColor": "#ffffff",
      "titleColor": "#333333",
      "descColor": "#666666",
      "borderRadius": 8
    },
    "showStations": true,
    "stationIcon": "https://maps.google.com/mapfiles/ms/icons/red-dot.png",
    "autoCenter": true,
    "showStationInfo": true,
    "stationRadius": 10,
    "currentLocation": {
      "lat": 31.23,
      "lng": 121.5
    },
    "stationIcons": {
      "online": "https://example.com/icons/station-online.png",
      "offline": "https://example.com/icons/station-offline.png",
      "busy": "https://example.com/icons/station-busy.png"
    },
    "stationIconSize": {
      "width": 32,
      "height": 32
    },
    "stationIconAnchor": {
      "x": 16,
      "y": 32
    }
  }
}
```

## 图标设计示例

### 1. 营业中图标 (online)
- 颜色：绿色 (#67C23A)
- 设计：圆形图标，中心有"✓"符号
- 尺寸：32x32 像素
- 背景：透明

### 2. 暂停营业图标 (offline)
- 颜色：红色 (#F56C6C)
- 设计：圆形图标，中心有"✗"符号
- 尺寸：32x32 像素
- 背景：透明

### 3. 繁忙状态图标 (busy)
- 颜色：橙色 (#E6A23C)
- 设计：圆形图标，中心有"!"符号
- 尺寸：32x32 像素
- 背景：透明

## 使用步骤

### 步骤 1：准备图标文件

1. 设计三种状态的图标
2. 确保图标尺寸一致（推荐 32x32 像素）
3. 保存为 PNG 格式，支持透明背景
4. 文件大小控制在 2MB 以内

### 步骤 2：在管理后台配置

1. 进入装修编辑器
2. 添加 GoogleMap 组件
3. 启用"显示换电站"选项
4. 在"站点图标设置"中上传图标：
   - 点击"营业中图标"上传按钮，选择绿色图标
   - 点击"暂停营业图标"上传按钮，选择红色图标
   - 点击"繁忙状态图标"上传按钮，选择橙色图标

### 步骤 3：调整图标大小和位置

1. 设置图标大小：
   - 宽度：32 像素
   - 高度：32 像素

2. 设置锚点位置：
   - X偏移：16（图标宽度的一半）
   - Y偏移：32（图标高度）

### 步骤 4：测试效果

1. 保存配置
2. 预览地图效果
3. 检查不同状态站点的图标显示
4. 调整图标大小和位置直到满意

## 常见配置组合

### 小图标配置
```json
{
  "stationIconSize": {
    "width": 24,
    "height": 24
  },
  "stationIconAnchor": {
    "x": 12,
    "y": 24
  }
}
```

### 大图标配置
```json
{
  "stationIconSize": {
    "width": 48,
    "height": 48
  },
  "stationIconAnchor": {
    "x": 24,
    "y": 48
  }
}
```

### 矩形图标配置
```json
{
  "stationIconSize": {
    "width": 40,
    "height": 32
  },
  "stationIconAnchor": {
    "x": 20,
    "y": 32
  }
}
```

## 注意事项

1. **图标一致性**：确保三种状态的图标风格一致，便于用户识别
2. **颜色对比**：选择在地图背景上有良好对比度的颜色
3. **尺寸适中**：图标不宜过大或过小，影响地图整体美观
4. **锚点设置**：正确的锚点设置确保图标准确指向站点位置
5. **性能考虑**：图标文件过大可能影响地图加载速度

## 故障排除

### 图标不显示
- 检查图标URL是否有效
- 确认图标文件格式支持
- 验证图标大小设置

### 图标位置偏移
- 调整锚点设置
- 检查图标尺寸与锚点匹配
- 确认坐标点位置

### 图标大小异常
- 检查图标尺寸设置
- 确认图标文件本身尺寸
- 验证地图缩放级别 