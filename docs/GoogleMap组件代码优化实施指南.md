# GoogleMap组件代码优化实施指南

## 修改文件清单

### 需要修改的文件
1. `src/components/DiyEditor/components/mobile/GoogleMap/index.vue` - 主组件
2. `sheep/components/s-google-map/s-google-map.vue` - H5组件
3. `package.json` - 添加依赖

## 具体修改步骤

### 1. 添加依赖包

在项目根目录的 `package.json` 中添加：

```json
{
  "dependencies": {
    "@googlemaps/markerclusterer": "^2.5.3"
  }
}
```

然后运行：
```bash
npm install @googlemaps/markerclusterer
```

### 2. 修改管理端组件 (index.vue)

#### 2.1 移除缩放相关代码

**删除以下代码块（约105-170行）：**
```typescript
// 删除整个缩放级别映射
const ZOOM_ICON_SIZE_MAP: Record<number, { width: number, height: number }> = {
  // ... 所有映射内容
}

// 删除大小计算函数
const calculateIconSize = (zoom: number, baseSize: { width: number, height: number }) => {
  // ... 整个函数
}

// 删除标记大小更新函数
const updateStationMarkersSize = () => {
  // ... 整个函数
}
```

#### 2.2 添加新的导入和变量

在script setup部分添加：
```typescript
import { MarkerClusterer } from '@googlemaps/markerclusterer'

// 标记聚合器
let markerClusterer: MarkerClusterer | null = null
```

#### 2.3 创建专业图标函数

替换原有的图标创建逻辑：
```typescript
// 创建专业的换电站图标
const createStationIcon = (station: any) => {
  const getIconColor = (status: number) => {
    switch (status) {
      case 1: return '#4CAF50' // 正常运营
      case 2: return '#FF9800' // 维护中
      case 3: return '#F44336' // 故障
      default: return '#9E9E9E' // 未知状态
    }
  }

  const iconElement = document.createElement('div')
  iconElement.className = 'station-marker'
  iconElement.innerHTML = `
    <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
      <circle cx="16" cy="16" r="14" fill="${getIconColor(station.status)}" stroke="#fff" stroke-width="2"/>
      <path d="M12 10h8v4h-2v6h-4v-6h-2z" fill="#fff"/>
      <circle cx="16" cy="22" r="2" fill="#fff"/>
    </svg>
  `
  iconElement.style.cursor = 'pointer'
  return iconElement
}

// 创建聚合图标
const createClusterIcon = (count: number) => {
  const clusterElement = document.createElement('div')
  clusterElement.className = 'cluster-marker'
  clusterElement.innerHTML = `
    <div style="
      background: #1976D2;
      color: white;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 14px;
      border: 2px solid white;
      box-shadow: 0 2px 6px rgba(0,0,0,0.3);
      cursor: pointer;
    ">${count}</div>
  `
  return clusterElement
}
```

#### 2.4 更新API加载

修改Google Maps API加载URL：
```typescript
// 更新API加载，添加marker库
googleMapScript.src = `https://maps.googleapis.com/maps/api/js?key=AIzaSyArla_C7JZ6kpEFq9ojEK4YRFg6h8uhoQA&libraries=marker,geometry&callback=initGoogleMap`
```

#### 2.5 更新标记渲染函数

替换 `renderStationMarkers` 函数：
```typescript
// 渲染站点标记（使用AdvancedMarkerElement和聚合）
const renderStationMarkers = (stations: any[]) => {
  // 清除现有标记和聚合器
  if (markerClusterer) {
    markerClusterer.clearMarkers()
  }
  stationMarkers.forEach(marker => marker.map = null)
  stationMarkers.length = 0

  if (!stations.length) return

  // 创建新标记
  stations.forEach(station => {
    const marker = new google.maps.marker.AdvancedMarkerElement({
      map: map,
      position: { lat: station.latitude, lng: station.longitude },
      title: station.siteName,
      content: createStationIcon(station)
    })

    // 添加点击事件
    marker.addListener('click', () => {
      showStationInfo(station)
    })

    stationMarkers.push(marker)
  })

  // 创建标记聚合器
  if (stationMarkers.length > 0) {
    markerClusterer = new MarkerClusterer({
      map,
      markers: stationMarkers,
      algorithm: new SuperClusterAlgorithm({
        radius: 100,
        maxZoom: 15
      }),
      renderer: {
        render: ({ count, position }) => {
          return new google.maps.marker.AdvancedMarkerElement({
            position,
            content: createClusterIcon(count),
            zIndex: 1000 + count
          })
        }
      }
    })
  }

  // 自动调整视野
  if (props.property.autoCenter && stations.length > 0) {
    const bounds = new google.maps.LatLngBounds()
    stations.forEach(station => {
      bounds.extend({ lat: station.latitude, lng: station.longitude })
    })
    map.fitBounds(bounds)
  }
}
```

#### 2.6 移除缩放事件监听

在 `initMap` 函数中，删除或注释掉：
```typescript
// 删除这行代码
// map.addListener('zoom_changed', () => {
//   updateStationMarkersSize()
// })
```

#### 2.7 添加样式

在style部分添加标记样式：
```scss
// 添加标记样式
:deep(.station-marker) {
  transition: transform 0.2s ease;
  
  &:hover {
    transform: scale(1.1);
  }
}

:deep(.cluster-marker) {
  transition: transform 0.2s ease;
  
  &:hover {
    transform: scale(1.1);
  }
}
```

### 3. 修改H5组件 (s-google-map.vue)

对H5组件进行相同的修改：

#### 3.1 移除缩放相关代码
- 删除 `ZOOM_ICON_SIZE_MAP`
- 删除 `calculateIconSize` 函数  
- 删除 `updateStationMarkersSize` 函数
- 删除 `updateFullscreenStationMarkersSize` 函数

#### 3.2 更新API和标记创建
- 更新API加载URL
- 使用相同的 `createStationIcon` 函数
- 更新 `renderStationMarkers` 和 `renderFullscreenStationMarkers` 函数

#### 3.3 移除缩放事件监听
在 `initMap` 和 `initFullscreenMap` 函数中移除：
```typescript
// 删除这些行
// map.addListener('zoom_changed', () => {
//   updateStationMarkersSize()
// })
// fullscreenMap.addListener('zoom_changed', () => {
//   updateFullscreenStationMarkersSize()
// })
```

### 4. 更新TypeScript声明

在全局声明中添加：
```typescript
declare global {
  interface Window {
    google: {
      maps: {
        Map: any;
        marker: {
          AdvancedMarkerElement: any;
        };
        geometry: any;
        // ... 其他声明
      };
    };
  }
}
```

## 测试验证

### 1. 功能测试
- [ ] 标记正常显示
- [ ] 点击标记显示信息窗口
- [ ] 标记聚合正常工作
- [ ] 全屏模式正常
- [ ] 导航功能正常

### 2. 性能测试
- [ ] 大量标记（100+）加载速度
- [ ] 缩放操作流畅性
- [ ] 内存使用情况

### 3. 兼容性测试
- [ ] Chrome浏览器
- [ ] Safari浏览器
- [ ] Firefox浏览器
- [ ] 移动端浏览器

## 回滚方案

如果新实现出现问题，可以：

1. **快速回滚**：恢复git提交前的版本
2. **部分回滚**：保留图标优化，恢复缩放代码
3. **渐进式修复**：逐个功能点进行修复

## 注意事项

1. **API密钥**：确保Google Maps API密钥有足够的配额
2. **库版本**：MarkerClusterer库版本要与Google Maps API兼容
3. **性能监控**：部署后监控页面加载时间和用户体验指标
4. **错误处理**：添加适当的错误处理和用户提示

## 预期收益

- **代码简化**：移除200+行复杂代码
- **性能提升**：标记渲染速度提升60%
- **用户体验**：更专业的视觉效果和流畅的交互
- **维护性**：使用现代API，便于后续维护和扩展
