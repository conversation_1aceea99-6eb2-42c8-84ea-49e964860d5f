router.system_management
router.infrastructure
router.oa_example
router.user_management
router.role_management
router.menu_management
router.department_management
router.position_management
router.dictionary_management
router.configuration_management
router.notification_announcement
router.audit_log
router.token_management
router.scheduled_task
router.mysql_monitoring
router.java_monitoring
router.redis_monitoring
router.form_builder
router.code_generation
router.api_interface
router.operation_log
router.login_log
router.user_query
router.user_create
router.user_update
router.user_delete
router.user_export
router.user_import
router.reset_password
router.role_query
router.role_create
router.role_update
router.role_delete
router.role_export
router.menu_query
router.menu_create
router.menu_update
router.menu_delete
router.department_query
router.department_create
router.department_update
router.department_delete
router.position_query
router.position_create
router.position_update
router.position_delete
router.position_export
router.dictionary_query
router.dictionary_create
router.dictionary_update
router.dictionary_delete
router.dictionary_export
router.configuration_query
router.configuration_create
router.configuration_update
router.configuration_delete
router.configuration_export
router.announcement_query
router.announcement_create
router.announcement_update
router.announcement_delete
router.operation_query
router.log_export
router.login_query
router.log_export
router.token_list
router.token_delete
router.task_create
router.task_update
router.task_delete
router.status_update
router.task_export
router.generation_update
router.generation_delete
router.import_code
router.preview_code
router.generate_code
router.set_role_menu_permission
router.set_role_data_permission
router.set_user_role
router.get_redis_monitoring_info
router.get_redis_key_list
router.code_generation_example
router.task_trigger
router.trace_link
router.access_log
router.log_export
router.api_log
router.error_log
router.log_processing
router.log_export
router.task_query
router.log_query
router.log_query
router.file_list
router.file_query
router.file_delete
router.sms_management
router.sms_channel
router.sms_channel_query
router.sms_channel_create
router.sms_channel_update
router.sms_channel_delete
router.sms_template
router.sms_template_query
router.sms_template_create
router.sms_template_update
router.sms_template_delete
router.sms_template_export
router.send_test_sms
router.sms_log
router.sms_log_query
router.sms_log_export
router.payment_management
router.leave_query
router.leave_application_query
router.leave_application_create
router.application_information
router.payment_application_info_query
router.payment_application_info_create
router.payment_application_info_update
router.payment_application_info_delete
router.secret_key_parsing
router.payment_merchant_info_query
router.payment_merchant_info_create
router.payment_merchant_info_update
router.payment_merchant_info_delete
router.payment_merchant_info_export
router.tenant_list
router.tenant_query
router.tenant_create
router.tenant_update
router.tenant_delete
router.tenant_export
router.secret_key_parsing
router.refund_order
router.refund_order_query
router.refund_order_create
router.refund_order_update
router.refund_order_delete
router.refund_order_export
router.payment_order
router.payment_order_query
router.payment_order_create
router.payment_order_update
router.payment_order_delete
router.payment_order_export
router.workflow_management
router.process_management
router.process_form
router.form_query
router.form_create
router.form_update
router.form_delete
router.form_export
router.process_model
router.model_query
router.model_create
router.model_update
router.model_delete
router.model_publish
router.approval_center
router.my_processes
router.process_instance_query
router.pending_task
router.completed_task
router.user_group
router.user_group_query
router.user_group_create
router.user_group_update
router.user_group_delete
router.process_definition_query
router.process_task_assignment_rule_query
router.process_task_assignment_rule_create
router.process_task_assignment_rule_update
router.process_instance_create
router.process_instance_cancel
router.process_task_query
router.process_task_update
router.tenant_management
router.tenant_package
router.tenant_package_query
router.tenant_package_create
router.tenant_package_update
router.tenant_package_delete
router.file_configuration
router.file_configuration_query
router.file_configuration_create
router.file_configuration_update
router.file_configuration_delete
router.file_configuration_export
router.file_management
router.author_dynamics
router.datasource_configuration
router.datasource_configuration_query
router.datasource_configuration_create
router.datasource_configuration_update
router.datasource_configuration_delete
router.datasource_configuration_export
router.oauth_2_0
router.application_management
router.client_query
router.client_create
router.client_update
router.client_delete
router.report_management
router.report_designer
router.product_center
router.product_category
router.category_query
router.category_create
router.category_update
router.category_delete
router.product_brand
router.brand_query
router.brand_create
router.brand_update
router.brand_delete
router.product_list
router.product_query
router.product_create
router.product_update
router.product_delete
router.product_attribute
router.specification_query
router.specification_create
router.specification_update
router.specification_delete
router.banner
router.banner_query
router.banner_create
router.banner_update
router.banner_delete
router.marketing_center
router.coupon_list
router.coupon_template_query
router.coupon_template_create
router.coupon_template_update
router.coupon_template_delete
router.claim_record
router.coupon_query
router.coupon_delete
router.full_reduction_promotion
router.full_reduction_activity_query
router.full_reduction_activity_create
router.full_reduction_activity_update
router.full_reduction_activity_delete
router.full_reduction_activity_close
router.limited_time_discount
router.limited_time_discount_activity_query
router.limited_time_discount_activity_create
router.limited_time_discount_activity_update
router.limited_time_discount_activity_delete
router.limited_time_discount_activity_close
router.flash_sale_product
router.flash_sale_activity_query
router.flash_sale_activity_create
router.flash_sale_activity_update
router.flash_sale_activity_delete
router.flash_sale_timeslot
router.flash_sale_timeslot_query
router.flash_sale_timeslot_create
router.flash_sale_timeslot_update
router.flash_sale_timeslot_delete
router.order_center
router.after_sales_refund
router.after_sales_query
router.flash_sale_activity_close
router.order_list
router.region_management
router.official_account_management
router.account_management
router.add_account
router.update_account
router.query_account
router.delete_account
router.generate_qrcode
router.clear_api_quota
router.data_statistics
router.tag_management
router.query_tag
router.add_tag
router.update_tag
router.delete_tag
router.sync_tag
router.fan_management
router.query_fan
router.update_fan
router.sync_fan
router.message_management
router.article_publish_record
router.query_publish_list
router.publish_draft
router.delete_publish_record
router.article_draft_box
router.create_draft
router.update_draft
router.query_draft
router.delete_draft
router.material_management
router.upload_temporary_material
router.upload_permanent_material
router.delete_material
router.upload_article_image
router.query_material
router.menu_management_official_account  // Differentiating from general menu_management
router.auto_reply
router.query_reply
router.add_reply
router.update_reply
router.delete_reply
router.query_menu
router.save_menu
router.delete_menu
router.query_message
router.send_message
router.email_management
router.email_account
router.account_query
router.account_create
router.account_update
router.account_delete
router.email_template
router.template_query
router.template_create
router.template_update
router.template_delete
router.email_record
router.log_query_email // Differentiating from general log_query
router.send_test_email
router.internal_message_management
router.template_management_internal_message // Differentiating
router.internal_message_template_query
router.internal_message_template_create
router.internal_message_template_update
router.internal_message_template_delete
router.send_test_internal_message
router.message_record
router.internal_message_query
router.large_screen_designer
router.create_project
router.update_project
router.query_project
router.query_data_sql
router.query_data_http
router.boot_development_document
router.cloud_development_document
router.access_example
router.product_export
router.delivery_management
router.express_delivery_shipment
router.store_self_pickup
router.express_company
router.express_company_query
router.express_company_create
router.express_company_update
router.express_company_delete
router.express_company_export
router.shipping_template
router.shipping_template_query
router.shipping_template_create
router.shipping_template_update
router.shipping_template_delete
router.shipping_template_export
router.store_management
router.self_pickup_store_query
router.self_pickup_store_create
router.self_pickup_store_update
router.self_pickup_store_delete
router.self_pickup_store_export
router.flash_sale_activity
router.member_center
router.member_configuration
router.member_configuration_query
router.member_configuration_save
router.check_in_configuration
router.points_check_in_rule_query
router.points_check_in_rule_create
router.points_check_in_rule_update
router.points_check_in_rule_delete
router.member_points
router.user_points_record_query
router.check_in_record
router.user_check_in_points_query
router.user_check_in_points_delete
router.member_check_in
router.callback_notification
router.payment_notification_query
router.group_buying_activity
router.group_buying_product
router.group_buying_activity_query
router.group_buying_activity_create
router.group_buying_activity_update
router.group_buying_activity_delete
router.group_buying_activity_close
router.bargain_activity
router.bargain_product
router.bargain_activity_query
router.bargain_activity_create
router.bargain_activity_update
router.bargain_activity_delete
router.bargain_activity_close
router.member_management
router.member_user_query
router.member_user_update
router.member_tag
router.member_tag_query
router.member_tag_create
router.member_tag_update
router.member_tag_delete
router.member_level
router.member_level_query
router.member_level_create
router.member_level_update
router.member_level_delete
router.member_group
router.user_group_query_member // Differentiating
router.user_group_create_member // Differentiating
router.user_group_update_member // Differentiating
router.user_group_delete_member // Differentiating
router.user_level_update
router.product_comment
router.comment_query
router.add_self_comment
router.merchant_reply
router.show_hide_comment
router.coupon_send
router.transaction_configuration
router.transaction_center_configuration_query
router.transaction_center_configuration_save
router.distribution_management
router.distribution_user
router.distribution_user_query
router.distribution_user_promoter_query
router.distribution_user_promotion_order_query
router.distribution_user_update_promotion_qualification
router.update_promoter
router.clear_promoter
router.commission_record
router.commission_record_query
router.commission_withdrawal
router.commission_withdrawal_query
router.commission_withdrawal_approve
router.statistics_center
router.transaction_statistics
router.transaction_statistics_query
router.transaction_statistics_export
router.mall_system
router.user_points_update
router.user_balance_update
router.coupon
router.bargain_record
router.bargain_record_query
router.assistance_record_query
router.group_buying_record
router.member_statistics
router.member_statistics_query
router.order_verification
router.article_category
router.category_query_article // Differentiating
router.category_create_article // Differentiating
router.category_update_article // Differentiating
router.category_delete_article // Differentiating
router.article_list
router.article_management_query
router.article_management_create
router.article_management_update
router.article_management_delete
router.content_management
router.mall_home_page
router.verify_order
router.promotional_activities
router.customer_management
router.customer_query
router.customer_create
router.customer_update
router.customer_delete
router.customer_export
router.crm_system
router.contract_management
router.contract_query
router.contract_create
router.contract_update
router.contract_delete
router.contract_export
router.lead_management
router.lead_query
router.lead_create
router.lead_update
router.lead_delete
router.lead_export
router.opportunity_management
router.opportunity_query
router.opportunity_create
router.opportunity_update
router.opportunity_delete
router.opportunity_export
router.contact_management
router.contact_query
router.contact_create
router.contact_update
router.contact_delete
router.contact_export
router.payment_collection_management
router.payment_collection_management_query
router.payment_collection_management_create
router.payment_collection_management_update
router.payment_collection_management_delete
router.payment_collection_management_export
router.payment_plan
router.payment_plan_query
router.payment_plan_create
router.payment_plan_update
router.payment_plan_delete
router.payment_plan_export
router.store_decoration
router.decoration_template
router.decoration_template_query
router.decoration_template_create
router.decoration_template_update
router.decoration_template_delete
router.decoration_template_use
router.decoration_page
router.decoration_page_query
router.decoration_page_create
router.decoration_page_update
router.decoration_page_delete
router.third_party_login
router.third_party_application
router.third_party_application_query
router.third_party_application_create
router.third_party_application_update
router.third_party_application_delete
router.third_party_user
router.main_sub_table_inline
router.single_table_crud
router.example_contact_query
router.example_contact_create
router.example_contact_update
router.example_contact_delete
router.example_contact_export
router.tree_table_crud
router.example_category_query
router.example_category_create
router.example_category_update
router.example_category_delete
router.example_category_export
router.main_sub_table_standard
router.student_query
router.student_create
router.student_update
router.student_delete
router.student_export
router.main_sub_table_erp
router.customer_common_pool_config
router.customer_common_pool_config_save
router.customer_limit_config
router.customer_limit_config_query
router.customer_limit_config_create
router.customer_limit_config_update
router.customer_limit_config_delete
router.customer_limit_config_export
router.system_configuration
router.websocket
router.product_management
router.product_query_generic // Differentiating
router.product_create_generic // Differentiating
router.product_update_generic // Differentiating
router.product_delete_generic // Differentiating
router.product_export_generic // Differentiating
router.product_category_config
router.product_category_query
router.product_category_create
router.product_category_update
router.product_category_delete
router.associate_opportunity
router.unfollow_opportunity
router.product_statistics
router.customer_common_pool
router.order_query
router.order_update
router.payment_refund_example
router.transfer_example
router.wallet_management
router.recharge_package
router.wallet_recharge_package_query
router.wallet_recharge_package_create
router.wallet_recharge_package_update
router.wallet_recharge_package_delete
router.wallet_balance
router.wallet_balance_query
router.transfer_order
router.data_statistics_generic // Differentiating
router.leaderboard
router.customer_import
router.erp_system
router.product_management_erp // Differentiating
router.product_information
router.product_query_erp // Differentiating
router.product_create_erp // Differentiating
router.product_update_erp // Differentiating
router.product_delete_erp // Differentiating
router.product_export_erp // Differentiating
router.product_category_erp // Differentiating
router.category_query_erp // Differentiating
router.category_create_erp // Differentiating
router.category_update_erp // Differentiating
router.category_delete_erp // Differentiating
router.category_export_erp // Differentiating
router.product_unit
router.unit_query
router.unit_create
router.unit_update
router.unit_delete
router.unit_export
router.inventory_management
router.warehouse_information
router.warehouse_query
router.warehouse_create
router.warehouse_update
router.warehouse_delete
router.warehouse_export
router.product_inventory
router.inventory_query
router.inventory_export
router.stock_in_out_details
router.inventory_details_query
router.inventory_details_export
router.other_inbound
router.other_inbound_order_query
router.other_inbound_order_create
router.other_inbound_order_update
router.other_inbound_order_delete
router.other_inbound_order_export
router.purchase_management
router.supplier_information
router.supplier_query
router.supplier_create
router.supplier_update
router.supplier_delete
router.supplier_export
router.other_inbound_order_approval
router.other_outbound
router.other_outbound_order_query
router.other_outbound_order_create
router.other_outbound_order_update
router.other_outbound_order_delete
router.other_outbound_order_export
router.other_outbound_order_approval
router.sales_management
router.customer_information
router.customer_query_sales // Differentiating
router.customer_create_sales // Differentiating
router.customer_update_sales // Differentiating
router.customer_delete_sales // Differentiating
router.customer_export_sales // Differentiating
router.inventory_transfer
router.inventory_transfer_order_query
router.inventory_transfer_order_create
router.inventory_transfer_order_update
router.inventory_transfer_order_delete
router.inventory_transfer_order_export
router.inventory_transfer_order_approval
router.inventory_count
router.inventory_count_order_query
router.inventory_count_order_create
router.inventory_count_order_update
router.inventory_count_order_delete
router.inventory_count_order_export
router.inventory_count_order_approval
router.sales_order
router.sales_order_query
router.sales_order_create
router.sales_order_update
router.sales_order_delete
router.sales_order_export
router.sales_order_approval
router.financial_management
router.settlement_account
router.settlement_account_query
router.settlement_account_create
router.settlement_account_update
router.settlement_account_delete
router.settlement_account_export
router.sales_outbound
router.sales_outbound_query
router.sales_outbound_create
router.sales_outbound_update
router.sales_outbound_delete
router.sales_outbound_export
router.sales_outbound_approval
router.sales_return
router.sales_return_query
router.sales_return_create
router.sales_return_update
router.sales_return_delete
router.sales_return_export
router.sales_return_approval
router.purchase_order
router.purchase_order_query
router.purchase_order_create
router.purchase_order_update
router.purchase_order_delete
router.purchase_order_export
router.purchase_order_approval
router.purchase_inbound
router.purchase_inbound_query
router.purchase_inbound_create
router.purchase_inbound_update
router.purchase_inbound_delete
router.purchase_inbound_export
router.purchase_inbound_approval
router.purchase_return
router.purchase_return_query
router.purchase_return_create
router.purchase_return_update
router.purchase_return_delete
router.purchase_return_export
router.purchase_return_approval
router.payment_slip
router.payment_slip_query
router.payment_slip_create
router.payment_slip_update
router.payment_slip_delete
router.payment_slip_export
router.payment_slip_approval
router.receipt_slip
router.receipt_slip_query
router.receipt_slip_create
router.receipt_slip_update
router.receipt_slip_delete
router.receipt_slip_export
router.receipt_slip_approval
router.todo_items
router.erp_home_page
router.opportunity_status_config
router.opportunity_status_query
router.opportunity_status_create
router.opportunity_status_update
router.opportunity_status_delete
router.contract_configuration
router.customer_common_pool_config_query
router.contract_configuration_update
router.contract_configuration_query
router.customer_analysis
router.cc_to_me
router.process_category
router.category_query_process // Differentiating
router.category_create_process // Differentiating
router.category_update_process // Differentiating
router.category_delete_process // Differentiating
router.initiate_process
router.process_instance
router.process_instance_query_admin
router.process_instance_cancel_admin
router.process_task
router.process_task_query_admin
router.process_listener
router.process_listener_query
router.process_listener_create
router.process_listener_update
router.process_listener_delete
router.process_expression
router.process_expression_query
router.process_expression_create
router.process_expression_update
router.process_expression_delete
router.employee_performance
router.customer_profile
router.sales_funnel
router.message_center
router.monitoring_center
router.claim_common_pool_customer
router.assign_common_pool_customer
router.product_statistics_query
router.product_statistics_export
router.payment_channel_query
router.payment_channel_create
router.payment_channel_update
router.payment_channel_delete
router.product_collection_query
router.product_browsing_query
router.after_sales_approve
router.after_sales_reject
router.after_sales_confirm_return
router.after_sales_confirm_refund
router.delete_project
router.member_level_record_query
router.member_experience_record_query
router.ai_large_model
router.ai_chat
router.console
router.api_key
router.api_key_query
router.api_key_create
router.api_key_update
router.api_key_delete
router.model_configuration
router.chat_model_query
router.chat_model_create
router.chat_model_update
router.chat_model_delete
router.chat_role
router.chat_role_query
router.chat_role_create
router.chat_role_update
router.chat_role_delete
router.chat_management
router.session_query
router.session_delete
router.message_query_chat // Differentiating
router.message_delete_chat // Differentiating
router.ai_drawing
router.drawing_management
router.drawing_query
router.drawing_delete
router.drawing_update
router.music_management
router.music_query
router.music_update
router.music_delete
router.ai_writing
router.writing_management
router.ai_writing_query
router.ai_writing_delete
router.ai_music
router.customer_service_center
router.ai_mind_map
router.mind_map_management
router.mind_map_query
router.mind_map_delete
router.session_query_mindmap // Differentiating
router.session_update_mindmap // Differentiating
router.message_query_mindmap // Differentiating
router.session_delete_mindmap // Differentiating
router.message_send_mindmap // Differentiating
router.message_update_mindmap // Differentiating
router.points_mall
router.points_mall_activity_query
router.points_mall_activity_create
router.points_mall_activity_update
router.points_mall_activity_delete
router.points_mall_activity_export
router.create_promoter
router.process_cleanup
router.points_mall_activity_close
router.ai_knowledge_base
router.ai_knowledge_base_query
router.ai_knowledge_base_create
router.ai_knowledge_base_update
router.ai_knowledge_base_delete
router.tool_management
router.tool_query
router.tool_create
router.tool_update
router.tool_delete
router.iot_internet_of_things
router.device_access
router.product_management_iot // Differentiating
router.product_query_iot // Differentiating
router.product_create_iot // Differentiating
router.product_update_iot // Differentiating
router.product_delete_iot // Differentiating
router.product_export_iot // Differentiating
router.device_management
router.device_query
router.device_create
router.device_update
router.device_delete
router.device_export
router.product_category_iot // Differentiating
router.product_category_query_iot // Differentiating
router.product_category_create_iot // Differentiating
router.product_category_update_iot // Differentiating
router.product_category_delete_iot // Differentiating
router.plugin_management
router.plugin_query
router.plugin_create
router.plugin_update
router.plugin_delete
router.plugin_export
router.device_group
router.device_group_query
router.device_group_create
router.device_group_update
router.device_group_delete
router.device_import
router.product_thing_model
router.product_thing_model_function_query
router.product_thing_model_function_create
router.product_thing_model_function_update
router.product_thing_model_function_delete
router.product_thing_model_function_export
router.device_uplink
router.device_attribute_query
router.device_log_query
router.device_downlink
router.ops_management
router.rule_engine
router.scene_linkage
router.iot_home_page
router.data_bridge
router.iot_data_bridge_query
router.iot_data_bridge_create
router.iot_data_bridge_update
router.iot_data_bridge_delete
router.iot_data_bridge_export
router.ai_workflow
router.ai_workflow_query
router.ai_workflow_create
router.ai_workflow_update
router.ai_workflow_delete
router.ai_workflow_test
router.workbench
router.vehicle_management
router.site_management
router.order_management
router.vehicle_delete
router.vehicle_add
router.vehicle_export
router.vehicle_update
router.vehicle_unbind
router.edit
router.enable_disable
router.show_hide
router.details
router.import

