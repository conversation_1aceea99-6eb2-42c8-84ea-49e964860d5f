# GoogleMap 站点图标动态缩放实现方案

## 概述

本方案实现了类似谷歌地图 ViewportInfo 的功能，让换电站图标能够根据地图缩放级别动态调整大小，提供更专业的地图展示效果。

## 技术原理

### 1. 核心概念

- **ViewportInfo**：谷歌地图中的视口信息，包括当前缩放级别、中心点、边界等
- **动态缩放**：根据地图缩放级别实时调整标记图标的大小
- **响应式设计**：图标大小与地图缩放级别保持协调的比例关系

### 2. 实现机制

1. **监听缩放事件**：使用 `zoom_changed` 事件监听地图缩放级别变化
2. **计算图标大小**：根据当前缩放级别计算合适的图标尺寸
3. **实时更新标记**：在缩放时重新设置标记的 `scaledSize` 属性
4. **平滑过渡**：使用合理的缩放算法实现自然的大小变化

## 实现方案

### 方案一：线性缩放（当前采用）

#### 1. 缩放级别映射表

```typescript
const ZOOM_ICON_SIZE_MAP = {
  1: { width: 16, height: 16 },   // 最小缩放级别
  2: { width: 18, height: 18 },
  3: { width: 20, height: 20 },
  4: { width: 22, height: 22 },
  5: { width: 24, height: 24 },
  6: { width: 26, height: 26 },
  7: { width: 28, height: 28 },
  8: { width: 30, height: 30 },
  9: { width: 32, height: 32 },   // 基准大小
  10: { width: 34, height: 34 },
  11: { width: 36, height: 36 },
  12: { width: 38, height: 38 },
  13: { width: 40, height: 40 },
  14: { width: 42, height: 42 },
  15: { width: 44, height: 44 },
  16: { width: 46, height: 46 },
  17: { width: 48, height: 48 },
  18: { width: 50, height: 50 },
  19: { width: 52, height: 52 },
  20: { width: 54, height: 54 }   // 最大缩放级别
}
```

#### 2. 大小计算函数

```typescript
const calculateIconSize = (zoom: number, baseSize: { width: number, height: number }) => {
  const minZoom = 1
  const maxZoom = 20
  const clampedZoom = Math.max(minZoom, Math.min(maxZoom, zoom))
  
  // 获取缩放级别对应的尺寸
  const zoomSize = ZOOM_ICON_SIZE_MAP[clampedZoom] || ZOOM_ICON_SIZE_MAP[9]
  
  // 根据基础尺寸计算实际尺寸
  const scaleFactor = Math.min(zoomSize.width / 32, zoomSize.height / 32)
  
  return {
    width: Math.round(baseSize.width * scaleFactor),
    height: Math.round(baseSize.height * scaleFactor)
  }
}
```

#### 3. 更新函数

```typescript
const updateStationMarkersSize = () => {
  if (!map || stationMarkers.length === 0) return
  
  const currentZoom = map.getZoom()
  const baseIconSize = {
    width: props.property.stationIconSize?.width || 32,
    height: props.property.stationIconSize?.height || 32
  }
  
  const newSize = calculateIconSize(currentZoom, baseIconSize)
  
  stationMarkers.forEach(marker => {
    const currentIcon = marker.getIcon()
    if (currentIcon && typeof currentIcon === 'object') {
      // 更新图标大小
      const updatedIcon = {
        ...currentIcon,
        scaledSize: new window.google.maps.Size(newSize.width, newSize.height)
      }
      marker.setIcon(updatedIcon)
    }
  })
}
```

### 方案二：指数缩放（可选）

```typescript
const calculateIconSizeExponential = (zoom: number, baseSize: { width: number, height: number }) => {
  const minZoom = 1
  const maxZoom = 20
  const clampedZoom = Math.max(minZoom, Math.min(maxZoom, zoom))
  
  // 使用指数函数计算缩放因子
  const scaleFactor = Math.pow(1.1, clampedZoom - 9) // 以缩放级别9为基准
  
  // 限制最小和最大缩放
  const minScale = 0.5  // 最小缩放到50%
  const maxScale = 2.0  // 最大缩放到200%
  const clampedScale = Math.max(minScale, Math.min(maxScale, scaleFactor))
  
  return {
    width: Math.round(baseSize.width * clampedScale),
    height: Math.round(baseSize.height * clampedScale)
  }
}
```

## 技术实现

### 1. 事件监听

```typescript
// 在地图初始化时添加缩放事件监听
map.addListener('zoom_changed', () => {
  updateStationMarkersSize()
})
```

### 2. 标记创建

```typescript
// 在创建标记时使用动态计算的大小
const iconSize = calculateIconSize(currentZoom, baseIconSize)

const icon = {
  url: iconUrl,
  scaledSize: new window.google.maps.Size(iconSize.width, iconSize.height),
  anchor: new window.google.maps.Point(iconAnchor.x, iconAnchor.y)
}
```

### 3. 实时更新

```typescript
// 在缩放时实时更新所有标记的大小
const updateStationMarkersSize = () => {
  // 获取当前缩放级别
  const currentZoom = map.getZoom()
  
  // 计算新的图标大小
  const newSize = calculateIconSize(currentZoom, baseIconSize)
  
  // 更新所有标记
  stationMarkers.forEach(marker => {
    const currentIcon = marker.getIcon()
    if (currentIcon && typeof currentIcon === 'object') {
      const updatedIcon = {
        ...currentIcon,
        scaledSize: new window.google.maps.Size(newSize.width, newSize.height)
      }
      marker.setIcon(updatedIcon)
    }
  })
}
```

## 缩放效果对比

### 缩放级别与图标大小关系

| 缩放级别 | 图标大小 | 视觉效果 |
|----------|----------|----------|
| 1-5      | 16-24px  | 小图标，适合概览 |
| 6-10     | 26-34px  | 中等图标，适合浏览 |
| 11-15    | 36-44px  | 较大图标，适合详细查看 |
| 16-20    | 46-54px  | 大图标，适合精确定位 |

### 用户体验提升

1. **视觉层次**：不同缩放级别下图标大小合理，避免视觉混乱
2. **信息密度**：缩放时图标大小与地图信息密度匹配
3. **交互友好**：图标大小适中，便于点击和识别
4. **专业感**：类似谷歌地图的专业展示效果

## 性能优化

### 1. 防抖处理

```typescript
// 添加防抖处理，避免频繁更新
let updateTimeout: NodeJS.Timeout | null = null

const debouncedUpdateSize = () => {
  if (updateTimeout) {
    clearTimeout(updateTimeout)
  }
  updateTimeout = setTimeout(() => {
    updateStationMarkersSize()
  }, 100) // 100ms 防抖
}

map.addListener('zoom_changed', debouncedUpdateSize)
```

### 2. 批量更新

```typescript
// 批量更新所有标记，减少重绘次数
const updateStationMarkersSize = () => {
  if (!map || stationMarkers.length === 0) return
  
  const currentZoom = map.getZoom()
  const newSize = calculateIconSize(currentZoom, baseIconSize)
  
  // 批量更新，避免逐个更新导致的性能问题
  stationMarkers.forEach(marker => {
    const currentIcon = marker.getIcon()
    if (currentIcon && typeof currentIcon === 'object') {
      const updatedIcon = {
        ...currentIcon,
        scaledSize: new window.google.maps.Size(newSize.width, newSize.height)
      }
      marker.setIcon(updatedIcon)
    }
  })
}
```

## 配置选项

### 1. 基础配置

```typescript
// 在 config.ts 中添加配置选项
export interface GoogleMapProperty {
  // ... 现有配置
  stationIconScaling: {
    enabled: boolean        // 是否启用动态缩放
    minScale: number        // 最小缩放比例 (0.5)
    maxScale: number        // 最大缩放比例 (2.0)
    baseZoom: number        // 基准缩放级别 (9)
  }
}
```

### 2. 默认值

```typescript
// 默认配置
stationIconScaling: {
  enabled: true,
  minScale: 0.5,
  maxScale: 2.0,
  baseZoom: 9
}
```

## 测试验证

### 1. 功能测试

- [ ] 地图缩放时图标大小正确变化
- [ ] 不同缩放级别下图标大小合理
- [ ] 图标锚点位置正确
- [ ] 性能表现良好，无卡顿

### 2. 边界测试

- [ ] 最小缩放级别 (1) 下图标显示正常
- [ ] 最大缩放级别 (20) 下图标显示正常
- [ ] 快速缩放时图标更新及时
- [ ] 大量标记时性能表现

### 3. 兼容性测试

- [ ] 不同浏览器兼容性
- [ ] 移动端适配
- [ ] 不同屏幕尺寸适配

## 扩展功能

### 1. 自定义缩放算法

```typescript
// 支持自定义缩放算法
interface ScalingAlgorithm {
  name: 'linear' | 'exponential' | 'custom'
  calculate: (zoom: number, baseSize: { width: number, height: number }) => { width: number, height: number }
}
```

### 2. 分组缩放

```typescript
// 支持不同类型标记使用不同的缩放策略
const markerGroups = {
  stations: { algorithm: 'linear', baseSize: { width: 32, height: 32 } },
  poi: { algorithm: 'exponential', baseSize: { width: 24, height: 24 } }
}
```

### 3. 动画效果

```typescript
// 添加缩放动画效果
const animateIconSize = (marker, fromSize, toSize, duration = 300) => {
  // 实现平滑的大小变化动画
}
```

## 总结

通过实现动态缩放功能，GoogleMap 组件的换电站图标展示效果更加专业和用户友好：

1. **视觉效果**：图标大小与地图缩放级别协调
2. **用户体验**：提供类似谷歌地图的专业体验
3. **性能优化**：合理的更新机制，确保流畅性
4. **可扩展性**：支持多种缩放算法和配置选项

这个实现方案不仅提升了地图的视觉效果，还增强了用户与地图的交互体验，使整个系统更加专业和易用。 