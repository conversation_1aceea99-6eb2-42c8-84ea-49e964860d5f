# GoogleMap组件换电站功能修改文档

## 概述

本次修改为GoogleMap组件添加了换电站功能，使其能够在地图上展示换电站位置，并支持点击查看站点详情和导航功能。修改涉及管理后台和uni-app两个端的组件，并完成了与后端API的集成。

## 修改内容

### 1. 管理后台组件修改

#### 1.1 配置文件修改 (`config.ts`)

**文件路径**: `yudao-ui/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/GoogleMap/config.ts`

**新增配置项**:
```typescript
// 换电站相关配置
showStations: boolean           // 是否显示换电站
stationIcon: string            // 站点图标URL
autoCenter: boolean            // 是否自动居中到站点
showStationInfo: boolean       // 是否显示站点信息弹窗
stationRadius: number          // 站点搜索半径(km)
currentLocation: {             // 当前位置
  lat: number
  lng: number
}
```

**默认配置**:
```typescript
// 换电站默认配置
showStations: false,
stationIcon: 'https://maps.google.com/mapfiles/ms/icons/red-dot.png',
autoCenter: true,
showStationInfo: true,
stationRadius: 10,
currentLocation: {
  lat: 31.23,
  lng: 121.5
}
```

#### 1.2 主组件修改 (`index.vue`)

**文件路径**: `yudao-ui/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/GoogleMap/index.vue`

**新增功能**:
- 站点数据获取和渲染
- 多个站点标记显示
- 站点信息弹窗
- 点击站点查看详情
- 导航功能

**核心方法**:
```javascript
// 获取站点列表
const getStationList = async () => {
  // 在管理后台使用模拟数据展示效果
}

// 渲染站点标记
const renderStationMarkers = (stations) => {
  // 清除现有标记并创建新标记
  // 添加点击事件监听
  // 自动调整地图视野
}

// 显示站点信息
const showStationInfo = (station) => {
  // 显示站点详情弹窗
}

// 导航到站点
const navigateToStation = () => {
  // 打开Google地图导航
}
```

#### 1.3 属性面板修改 (`property.vue`)

**文件路径**: `yudao-ui/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/GoogleMap/property.vue`

**新增配置选项**:
- 换电站显示开关
- 站点图标URL输入
- 自动居中选项
- 站点信息弹窗开关
- 搜索半径设置
- 当前位置经纬度设置

### 2. uni-app端组件修改

#### 2.1 主组件修改 (`s-google-map.vue`)

**文件路径**: `yudao-ui/yudao-mall-uniapp/sheep/components/s-google-map/s-google-map.vue`

**新增功能**:
- 集成真实API调用
- 站点信息弹窗（使用uni-popup）
- 导航功能（使用uni.openLocation）
- 数据格式转换
- 状态显示优化

**API集成**:
```javascript
import SiteApi from '@/sheep/api/site/site'

// 获取站点列表
const getStationList = async () => {
  const { code, data } = await SiteApi.getSiteList({
    longitude: props.data.currentLocation?.lng || props.data.lng,
    latitude: props.data.currentLocation?.lat || props.data.lat,
    keyWord: ''
  })
  
  // 转换后端数据格式为前端期望的格式
  return data.map(station => ({
    siteNo: station.siteNo,
    siteName: station.siteName,
    address: station.siteAdress || station.address,
    latitude: station.latitude || station.lat,
    longitude: station.longitude || station.lng,
    status: station.siteStatus || station.status,
    distance: station.distance,
    // 其他字段
    city: station.city,
    enabled: station.enabled,
    replaceCarCtn: station.replaceCarCtn,
    lowPrice: station.lowPrice,
    normalPrice: station.normalPrice,
    peakPrice: station.peakPrice,
    nowPrice: station.nowPrice
  }))
}
```

**导航功能**:
```javascript
const navigateToStation = () => {
  uni.openLocation({
    latitude: latitude,
    longitude: longitude,
    name: selectedStation.value.siteName,
    address: selectedStation.value.address
  })
}
```

**状态处理**:
```javascript
// 获取状态样式类
const getStatusClass = (status) => {
  switch (status) {
    case 0: return 'offline'    // 暂停营业
    case 1: return 'online'     // 营业中
    case 2: return 'busy'       // 繁忙
    default: return 'offline'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 0: return '暂停营业'
    case 1: return '营业中'
    case 2: return '繁忙'
    default: return '未知状态'
  }
}
```

## 后端接口分析

### 1. 站点列表接口
- **接口**: `/site/app/list`
- **方法**: GET
- **参数**: 
  - `longitude`: 经度
  - `latitude`: 纬度
  - `keyWord`: 关键词
- **返回**: 站点列表数据（EntityMap格式）

### 2. 站点详情接口
- **接口**: `/site/app/info`
- **方法**: GET
- **参数**: `siteNo`: 站点编码
- **返回**: 站点详细信息

### 3. 数据字段映射

**后端返回字段** → **前端使用字段**:
- `siteNo` → `siteNo` (站点编码)
- `siteName` → `siteName` (站点名称)
- `siteAdress` → `address` (站点地址)
- `latitude` → `latitude` (纬度)
- `longitude` → `longitude` (经度)
- `siteStatus` → `status` (站点状态)
- `distance` → `distance` (距离，单位：米)
- `replaceCarCtn` → `replaceCarCtn` (可换车辆数)
- `nowPrice` → `nowPrice` (当前价格)

## 技术实现要点

### 1. 兼容性设计
- 保持原有地图功能不变
- 通过配置开关控制换电站功能
- 向后兼容现有装修配置

### 2. 数据流设计
- 管理后台：使用模拟数据展示效果
- uni-app端：调用真实API获取数据
- 配置数据通过property传递
- 数据格式转换适配

### 3. 用户体验优化
- 站点信息弹窗美观易用
- 支持自动定位和手动定位
- 导航功能集成系统地图
- 状态显示清晰（营业中/暂停营业/繁忙）
- 距离显示优化（米转千米）

### 4. 性能考虑
- 站点数据缓存机制
- 标记清理和重建优化
- 错误处理和降级方案

## 使用说明

### 1. 管理后台配置
1. 进入商城装修页面
2. 添加GoogleMap组件
3. 在属性面板中配置换电站选项：
   - 开启"显示换电站"
   - 设置站点图标URL
   - 配置搜索半径
   - 设置当前位置坐标

### 2. 前台展示
1. uni-app页面自动加载装修配置
2. 根据配置决定是否显示换电站
3. 调用后端API获取站点数据
4. 在地图上渲染站点标记

### 3. 用户交互
1. 点击站点图标查看详情
2. 在弹窗中查看站点信息：
   - 站点名称和地址
   - 营业状态（营业中/暂停营业/繁忙）
   - 距离信息
   - 可换车辆数量
   - 当前价格
3. 点击导航按钮进行导航

## 文件清单

### 修改的文件
1. `yudao-ui/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/GoogleMap/config.ts`
2. `yudao-ui/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/GoogleMap/index.vue`
3. `yudao-ui/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/GoogleMap/property.vue`
4. `yudao-ui/yudao-mall-uniapp/sheep/components/s-google-map/s-google-map.vue`

### 依赖的文件
1. `yudao-ui/yudao-mall-uniapp/sheep/api/site/site.js` (已存在)
2. `yudao-module-site/yudao-module-site-server/src/main/java/cn/iocoder/yudao/module/site/controller/app/bussite/AppBusSiteController.java` (已存在)
3. `yudao-module-site/yudao-module-site-server/src/main/java/cn/iocoder/yudao/module/site/service/impl/BusSiteServiceImpl.java` (已存在)

## 测试建议

### 1. 功能测试
- 换电站显示开关功能
- 站点标记渲染
- 点击站点查看详情
- 导航功能
- 数据格式转换

### 2. 兼容性测试
- 不同浏览器兼容性
- 移动端适配
- 小程序兼容性

### 3. 性能测试
- 大量站点数据渲染
- 地图加载性能
- 内存使用情况

## 注意事项

1. **API密钥**: 确保Google Maps API密钥有效且有足够配额
2. **跨域问题**: 注意API调用的跨域配置
3. **错误处理**: 网络异常时的降级处理
4. **权限问题**: 确保有调用站点API的权限
5. **数据格式**: 注意后端返回的EntityMap格式数据转换

## 后续优化建议

1. **缓存机制**: 实现站点数据本地缓存
2. **搜索功能**: 添加站点搜索功能
3. **筛选功能**: 按站点状态、距离等筛选
4. **实时更新**: 站点状态实时更新
5. **离线支持**: 离线地图和站点数据支持
6. **自定义图标**: 支持不同状态使用不同图标
7. **聚合显示**: 站点密集时进行聚合显示 