# GoogleMap组件示例数据说明

## 概述

为了便于测试和验证换电站地图展示功能，GoogleMap组件提供了完整的示例数据。当后端API返回空数据或调用失败时，组件会自动使用示例数据进行演示。

## 示例数据场景

### 1. 管理后台演示
- **用途**: 在商城装修管理后台中展示换电站功能
- **触发条件**: 始终使用示例数据
- **目的**: 让管理员能够预览换电站功能效果

### 2. uni-app端演示
- **用途**: 在用户端展示换电站功能
- **触发条件**: 
  - 后端API返回空数据
  - API调用失败
  - 网络连接问题
- **目的**: 确保用户始终能看到功能演示

## 示例站点数据

### 站点1: 陆家嘴换电站
```javascript
{
  siteNo: 'ST001',
  siteName: '陆家嘴换电站',
  address: '上海市浦东新区陆家嘴环路1000号',
  latitude: 31.23,
  longitude: 121.5,
  status: 1,                    // 营业中
  distance: 1200,               // 1.2km
  city: '上海',
  enabled: true,
  replaceCarCtn: 15,            // 15辆可换车辆
  lowPrice: 0.8,                // 低谷价格
  normalPrice: 1.2,             // 正常价格
  peakPrice: 1.8,               // 高峰价格
  nowPrice: 1.2                 // 当前价格
}
```

### 站点2: 外滩换电站
```javascript
{
  siteNo: 'ST002',
  siteName: '外滩换电站',
  address: '上海市黄浦区中山东一路1号',
  latitude: 31.24,
  longitude: 121.49,
  status: 2,                    // 繁忙
  distance: 2500,               // 2.5km
  city: '上海',
  enabled: true,
  replaceCarCtn: 8,             // 8辆可换车辆
  lowPrice: 0.9,
  normalPrice: 1.3,
  peakPrice: 1.9,
  nowPrice: 1.3
}
```

### 站点3: 南京路换电站
```javascript
{
  siteNo: 'ST003',
  siteName: '南京路换电站',
  address: '上海市黄浦区南京东路123号',
  latitude: 31.22,
  longitude: 121.48,
  status: 0,                    // 暂停营业
  distance: 3800,               // 3.8km
  city: '上海',
  enabled: false,
  replaceCarCtn: 0,             // 0辆可换车辆
  lowPrice: 0.7,
  normalPrice: 1.1,
  peakPrice: 1.7,
  nowPrice: 1.1
}
```

### 站点4: 静安寺换电站
```javascript
{
  siteNo: 'ST004',
  siteName: '静安寺换电站',
  address: '上海市静安区南京西路1686号',
  latitude: 31.22,
  longitude: 121.46,
  status: 1,                    // 营业中
  distance: 1800,               // 1.8km
  city: '上海',
  enabled: true,
  replaceCarCtn: 12,            // 12辆可换车辆
  lowPrice: 0.85,
  normalPrice: 1.25,
  peakPrice: 1.85,
  nowPrice: 1.25
}
```

### 站点5: 虹桥商务区换电站
```javascript
{
  siteNo: 'ST005',
  siteName: '虹桥商务区换电站',
  address: '上海市长宁区虹桥路1号',
  latitude: 31.20,
  longitude: 121.40,
  status: 1,                    // 营业中
  distance: 3200,               // 3.2km
  city: '上海',
  enabled: true,
  replaceCarCtn: 20,            // 20辆可换车辆
  lowPrice: 0.75,
  normalPrice: 1.15,
  peakPrice: 1.75,
  nowPrice: 1.15
}
```

## 数据字段说明

### 基础信息
- **siteNo**: 站点编号，唯一标识
- **siteName**: 站点名称，显示在地图和弹窗中
- **address**: 详细地址，显示在站点详情中
- **latitude/longitude**: 经纬度坐标，用于地图定位

### 状态信息
- **status**: 站点状态
  - `0`: 暂停营业（红色显示）
  - `1`: 营业中（绿色显示）
  - `2`: 繁忙（橙色显示）
- **enabled**: 是否启用
- **distance**: 距离当前位置（米）

### 业务信息
- **replaceCarCtn**: 可换车辆数量
- **lowPrice**: 低谷时段价格（元/度）
- **normalPrice**: 正常时段价格（元/度）
- **peakPrice**: 高峰时段价格（元/度）
- **nowPrice**: 当前价格（元/度）

## 地图展示效果

### 1. 标记显示
- 每个站点在地图上显示为一个标记点
- 标记点可点击，显示站点信息弹窗
- 支持自动居中功能，显示所有站点

### 2. 状态颜色
- **营业中**: 绿色标记
- **繁忙**: 橙色标记  
- **暂停营业**: 红色标记

### 3. 信息弹窗
点击站点标记后显示详细信息：
- 站点名称
- 详细地址
- 营业状态
- 距离信息
- 可换车辆数量
- 当前价格

## 测试验证

### 1. 地图功能验证
- [ ] 地图正确加载
- [ ] 站点标记正确显示
- [ ] 标记点击响应正常
- [ ] 自动居中功能正常

### 2. 数据展示验证
- [ ] 站点信息弹窗显示完整
- [ ] 状态颜色正确
- [ ] 距离单位正确（km）
- [ ] 价格信息正确

### 3. 交互功能验证
- [ ] 弹窗关闭功能
- [ ] 导航功能（uni-app端）
- [ ] 响应式布局

## 注意事项

1. **示例数据仅用于演示**: 生产环境中应使用真实的后端数据
2. **坐标范围**: 示例数据集中在上海市区，便于测试
3. **数据完整性**: 示例数据包含所有必要字段，确保功能完整展示
4. **状态多样性**: 包含不同状态的站点，便于测试状态显示逻辑

## 自定义示例数据

如需修改示例数据，可以编辑以下文件：
- 管理后台: `yudao-ui/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/GoogleMap/index.vue`
- uni-app端: `yudao-ui/yudao-mall-uniapp/sheep/components/s-google-map/s-google-map.vue`

修改时请确保：
- 坐标在合理范围内
- 数据格式保持一致
- 包含所有必要字段 