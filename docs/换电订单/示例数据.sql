-- 换电动态定价规则示例数据
-- 请先确保表已创建，然后执行以下插入语句

-- 1. 工作日峰时电价规则
INSERT INTO trade_battery_swap_pricing_rule (
    name, description, priority, status, station_codes, date_type, specific_dates, holiday_types, 
    time_ranges, price, price_description, valid_from, valid_to, match_count, last_matched_time, 
    creator, create_time, updater, update_time, deleted, tenant_id
) VALUES (
    '工作日峰时电价', 
    '工作日高峰时段电价规则，适用于用电高峰期', 
    100, 
    0, 
    NULL, 
    2, 
    NULL, 
    NULL, 
    '09:00-22:00', 
    180, 
    '峰时电价 1.8元/度', 
    '2024-01-01 00:00:00', 
    '2024-12-31 23:59:59', 
    0, 
    NULL, 
    'admin', 
    NOW(), 
    'admin', 
    NOW(), 
    0, 
    1
);

-- 2. 工作日谷时电价规则
INSERT INTO trade_battery_swap_pricing_rule (
    name, description, priority, status, station_codes, date_type, specific_dates, holiday_types, 
    time_ranges, price, price_description, valid_from, valid_to, match_count, last_matched_time, 
    creator, create_time, updater, update_time, deleted, tenant_id
) VALUES (
    '工作日谷时电价', 
    '工作日低谷时段电价规则，适用于用电低谷期', 
    90, 
    0, 
    NULL, 
    2, 
    NULL, 
    NULL, 
    '00:00-06:00', 
    100, 
    '谷时电价 1.0元/度', 
    '2024-01-01 00:00:00', 
    '2024-12-31 23:59:59', 
    0, 
    NULL, 
    'admin', 
    NOW(), 
    'admin', 
    NOW(), 
    0, 
    1
);

-- 3. 工作日平时电价规则
INSERT INTO trade_battery_swap_pricing_rule (
    name, description, priority, status, station_codes, date_type, specific_dates, holiday_types, 
    time_ranges, price, price_description, valid_from, valid_to, match_count, last_matched_time, 
    creator, create_time, updater, update_time, deleted, tenant_id
) VALUES (
    '工作日平时电价', 
    '工作日平时电价规则，适用于一般时段', 
    80, 
    0, 
    NULL, 
    2, 
    NULL, 
    NULL, 
    '06:00-09:00,22:00-24:00', 
    140, 
    '平时电价 1.4元/度', 
    '2024-01-01 00:00:00', 
    '2024-12-31 23:59:59', 
    0, 
    NULL, 
    'admin', 
    NOW(), 
    'admin', 
    NOW(), 
    0, 
    1
);

-- 4. 周末优惠电价规则
INSERT INTO trade_battery_swap_pricing_rule (
    name, description, priority, status, station_codes, date_type, specific_dates, holiday_types, 
    time_ranges, price, price_description, valid_from, valid_to, match_count, last_matched_time, 
    creator, create_time, updater, update_time, deleted, tenant_id
) VALUES (
    '周末优惠电价', 
    '周末全天优惠电价，鼓励周末换电', 
    70, 
    0, 
    NULL, 
    3, 
    NULL, 
    NULL, 
    NULL, 
    120, 
    '周末优惠价 1.2元/度', 
    '2024-01-01 00:00:00', 
    '2024-12-31 23:59:59', 
    0, 
    NULL, 
    'admin', 
    NOW(), 
    'admin', 
    NOW(), 
    0, 
    1
);

-- 5. 特定站点高价规则
INSERT INTO trade_battery_swap_pricing_rule (
    name, description, priority, status, station_codes, date_type, specific_dates, holiday_types, 
    time_ranges, price, price_description, valid_from, valid_to, match_count, last_matched_time, 
    creator, create_time, updater, update_time, deleted, tenant_id
) VALUES (
    '核心商圈站点电价', 
    '核心商圈换电站高价规则', 
    110, 
    0, 
    'PS001,PS002,PS003', 
    1, 
    NULL, 
    NULL, 
    NULL, 
    200, 
    '核心商圈价 2.0元/度', 
    '2024-01-01 00:00:00', 
    '2024-12-31 23:59:59', 
    0, 
    NULL, 
    'admin', 
    NOW(), 
    'admin', 
    NOW(), 
    0, 
    1
);

-- 6. 节假日特价规则
INSERT INTO trade_battery_swap_pricing_rule (
    name, description, priority, status, station_codes, date_type, specific_dates, holiday_types, 
    time_ranges, price, price_description, valid_from, valid_to, match_count, last_matched_time, 
    creator, create_time, updater, update_time, deleted, tenant_id
) VALUES (
    '节假日特价', 
    '节假日期间特殊定价', 
    95, 
    0, 
    NULL, 
    5, 
    NULL, 
    'NATIONAL_DAY,NEW_YEAR,SPRING_FESTIVAL', 
    NULL, 
    160, 
    '节假日价 1.6元/度', 
    '2024-01-01 00:00:00', 
    '2024-12-31 23:59:59', 
    0, 
    NULL, 
    'admin', 
    NOW(), 
    'admin', 
    NOW(), 
    0, 
    1
);

-- 7. 指定日期促销规则
INSERT INTO trade_battery_swap_pricing_rule (
    name, description, priority, status, station_codes, date_type, specific_dates, holiday_types, 
    time_ranges, price, price_description, valid_from, valid_to, match_count, last_matched_time, 
    creator, create_time, updater, update_time, deleted, tenant_id
) VALUES (
    '双十一促销价', 
    '双十一期间特殊促销价格', 
    120, 
    0, 
    NULL, 
    4, 
    '2024-11-11,2024-11-12', 
    NULL, 
    NULL, 
    80, 
    '双十一促销价 0.8元/度', 
    '2024-11-01 00:00:00', 
    '2024-11-30 23:59:59', 
    0, 
    NULL, 
    'admin', 
    NOW(), 
    'admin', 
    NOW(), 
    0, 
    1
);

-- 8. 夜间优惠规则
INSERT INTO trade_battery_swap_pricing_rule (
    name, description, priority, status, station_codes, date_type, specific_dates, holiday_types, 
    time_ranges, price, price_description, valid_from, valid_to, match_count, last_matched_time, 
    creator, create_time, updater, update_time, deleted, tenant_id
) VALUES (
    '夜间优惠电价', 
    '夜间时段优惠电价，鼓励错峰换电', 
    60, 
    0, 
    NULL, 
    1, 
    NULL, 
    NULL, 
    '23:00-06:00', 
    90, 
    '夜间优惠价 0.9元/度', 
    '2024-01-01 00:00:00', 
    '2024-12-31 23:59:59', 
    0, 
    NULL, 
    'admin', 
    NOW(), 
    'admin', 
    NOW(), 
    0, 
    1
);

-- 9. 测试用禁用规则
INSERT INTO trade_battery_swap_pricing_rule (
    name, description, priority, status, station_codes, date_type, specific_dates, holiday_types, 
    time_ranges, price, price_description, valid_from, valid_to, match_count, last_matched_time, 
    creator, create_time, updater, update_time, deleted, tenant_id
) VALUES (
    '测试禁用规则', 
    '用于测试的禁用状态规则', 
    50, 
    1, 
    'PS999', 
    1, 
    NULL, 
    NULL, 
    NULL, 
    300, 
    '测试价格 3.0元/度', 
    '2024-01-01 00:00:00', 
    '2024-12-31 23:59:59', 
    0, 
    NULL, 
    'admin', 
    NOW(), 
    'admin', 
    NOW(), 
    0, 
    1
);

-- 10. 默认兜底规则
INSERT INTO trade_battery_swap_pricing_rule (
    name, description, priority, status, station_codes, date_type, specific_dates, holiday_types, 
    time_ranges, price, price_description, valid_from, valid_to, match_count, last_matched_time, 
    creator, create_time, updater, update_time, deleted, tenant_id
) VALUES (
    '默认电价规则', 
    '默认兜底电价规则，优先级最低', 
    10, 
    0, 
    NULL, 
    1, 
    NULL, 
    NULL, 
    NULL, 
    150, 
    '默认电价 1.5元/度', 
    '2024-01-01 00:00:00', 
    '2024-12-31 23:59:59', 
    0, 
    NULL, 
    'admin', 
    NOW(), 
    'admin', 
    NOW(), 
    0, 
    1
);

-- 查询验证数据
SELECT 
    id,
    name,
    priority,
    CASE status WHEN 0 THEN '启用' WHEN 1 THEN '禁用' END as status_display,
    CASE date_type 
        WHEN 1 THEN '全部日期'
        WHEN 2 THEN '工作日'
        WHEN 3 THEN '周末'
        WHEN 4 THEN '指定日期'
        WHEN 5 THEN '节假日'
    END as date_type_display,
    time_ranges,
    CONCAT(price/100, '元/度') as price_display,
    price_description
FROM trade_battery_swap_pricing_rule 
WHERE deleted = 0 
ORDER BY priority DESC, id DESC;
