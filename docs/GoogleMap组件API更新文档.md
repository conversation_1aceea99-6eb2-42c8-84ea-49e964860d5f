# GoogleMap组件API更新文档

## 概述

根据Google Maps API的最新建议，已将组件中的 `google.maps.Marker` 更新为 `google.maps.marker.AdvancedMarkerElement`，以消除弃用警告并采用推荐的API。

## 更新内容

### 1. 管理后台组件更新

**文件**: `yudao-ui/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/GoogleMap/index.vue`

#### 1.1 默认标记更新
```javascript
// 更新前
marker = new window.google.maps.Marker({
  position: { lat: props.property.lat, lng: props.property.lng },
  map: map,
  title: props.property.locationDesc
})

// 更新后
marker = new window.google.maps.marker.AdvancedMarkerElement({
  position: { lat: props.property.lat, lng: props.property.lng },
  map: map,
  title: props.property.locationDesc
})
```

#### 1.2 站点标记更新
```javascript
// 更新前
const stationMarker = new window.google.maps.Marker({
  position: { lat: station.latitude, lng: station.longitude },
  map: map,
  icon: props.property.stationIcon,
  title: station.siteName
})

// 更新后
const stationMarker = new window.google.maps.marker.AdvancedMarkerElement({
  position: { lat: station.latitude, lng: station.longitude },
  map: map,
  title: station.siteName
})
```

#### 1.3 地图初始化更新
```javascript
// 更新前
map = new window.google.maps.Map(mapRef, {
  center: { lat: props.property.lat, lng: props.property.lng },
  zoom: props.property.zoom,
  disableDefaultUI: true,
  zoomControl: true
})

// 更新后
map = new window.google.maps.Map(mapRef, {
  center: { lat: props.property.lat, lng: props.property.lng },
  zoom: props.property.zoom,
  disableDefaultUI: true,
  zoomControl: true,
  mapId: 'DEMO_MAP_ID' // 添加地图ID以支持高级标记
})
```

#### 1.4 API加载URL更新
```javascript
// 更新前
googleMapScript.src = `https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&callback=initGoogleMap`

// 更新后
googleMapScript.src = `https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&libraries=marker&callback=initGoogleMap`
```

### 2. uni-app端组件更新

**文件**: `yudao-ui/yudao-mall-uniapp/sheep/components/s-google-map/s-google-map.vue`

#### 2.1 默认标记更新
```javascript
// 更新前
marker = new window.google.maps.Marker({
  position: { lat: props.data.lat, lng: props.data.lng },
  map: map,
  title: props.data.locationDesc,
})

// 更新后
marker = new window.google.maps.marker.AdvancedMarkerElement({
  position: { lat: props.data.lat, lng: props.data.lng },
  map: map,
  title: props.data.locationDesc,
})
```

#### 2.2 站点标记更新
```javascript
// 更新前
const stationMarker = new window.google.maps.Marker({
  position: { lat: station.latitude, lng: station.longitude },
  map: map,
  icon: props.data.stationIcon || 'https://maps.google.com/mapfiles/ms/icons/red-dot.png',
  title: station.siteName
})

// 更新后
const stationMarker = new window.google.maps.marker.AdvancedMarkerElement({
  position: { lat: station.latitude, lng: station.longitude },
  map: map,
  title: station.siteName
})
```

#### 2.3 地图初始化更新
```javascript
// 更新前
map = new window.google.maps.Map(mapRef, {
  center: { lat: props.data.lat, lng: props.data.lng },
  zoom: props.data.zoom,
  disableDefaultUI: true,
  zoomControl: true,
})

// 更新后
map = new window.google.maps.Map(mapRef, {
  center: { lat: props.data.lat, lng: props.data.lng },
  zoom: props.data.zoom,
  disableDefaultUI: true,
  zoomControl: true,
  mapId: 'DEMO_MAP_ID' // 添加地图ID以支持高级标记
})
```

#### 2.4 API加载URL更新
```javascript
// 更新前
script.src = 'https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&callback=initGoogleMap'

// 更新后
script.src = 'https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&libraries=marker&callback=initGoogleMap'
```

## 主要变化

### 1. API变更
- **旧API**: `google.maps.Marker` (已弃用)
- **新API**: `google.maps.marker.AdvancedMarkerElement` (推荐)

### 2. 功能差异
- **图标支持**: `AdvancedMarkerElement` 不支持传统的 `icon` 属性
- **自定义内容**: 新API支持更丰富的自定义内容
- **性能**: 新API提供更好的性能
- **地图ID要求**: 新API要求提供有效的 `mapId` 参数

### 3. 兼容性
- 新API向后兼容，不会影响现有功能
- 点击事件监听器保持不变
- 位置设置和地图集成方式相同

## 注意事项

### 1. 地图ID要求
Google Maps的新版本要求为地图实例提供有效的 `mapId` 才能使用 `AdvancedMarkerElement`。这是Google为了提供更好的性能和功能而引入的要求。

```javascript
// 必须添加mapId参数
map = new window.google.maps.Map(mapRef, {
  // ... 其他配置
  mapId: 'DEMO_MAP_ID' // 或使用自定义的地图ID
})
```

**重要说明**：
- `DEMO_MAP_ID` 是Google提供的演示地图ID，适用于开发和测试
- 在生产环境中，建议在Google Cloud Console中创建自定义地图ID
- 没有有效地图ID的地图将无法使用高级标记功能

### 2. 图标处理
由于 `AdvancedMarkerElement` 不支持传统的 `icon` 属性，如果需要自定义图标，需要：

```javascript
// 创建自定义标记内容
const pinElement = document.createElement('div')
pinElement.className = 'custom-marker'
pinElement.innerHTML = `<img src="${iconUrl}" alt="marker" />`

const marker = new google.maps.marker.AdvancedMarkerElement({
  position: position,
  content: pinElement,
  map: map
})
```

### 3. 样式调整
可能需要调整CSS样式以适应新的标记结构：

```css
.custom-marker {
  width: 32px;
  height: 32px;
  cursor: pointer;
}

.custom-marker img {
  width: 100%;
  height: 100%;
}
```

### 4. 浏览器兼容性
- 新API需要较新的浏览器版本
- 建议在支持的浏览器中测试功能

### 4. 地图ID要求
- `AdvancedMarkerElement` 要求地图实例提供有效的 `mapId`
- 使用 `DEMO_MAP_ID` 作为默认地图ID
- 在生产环境中建议使用自定义的地图ID

## 迁移指南

### 1. 检查现有代码
- 查找所有使用 `google.maps.Marker` 的地方
- 更新为新API

### 2. 测试功能
- 确保标记正确显示
- 验证点击事件正常工作
- 检查地图交互功能

### 3. 性能优化
- 利用新API的性能优势
- 考虑使用自定义内容增强用户体验

## 示例数据功能

### 1. 管理后台示例数据
管理后台组件使用固定的示例数据来展示换电站功能，包括：
- 5个不同状态的换电站
- 完整的站点信息（名称、地址、状态、距离、价格等）
- 不同状态：营业中(1)、繁忙(2)、暂停营业(0)

### 2. uni-app端示例数据
uni-app端组件在以下情况下会使用示例数据：
- 后端API返回空数据
- API调用失败
- 网络连接问题

### 3. 示例数据内容
```javascript
// 示例站点数据结构
{
  siteNo: 'DEMO001',           // 站点编号
  siteName: '示例换电站1',      // 站点名称
  address: '详细地址',          // 站点地址
  latitude: 31.23,             // 纬度
  longitude: 121.5,            // 经度
  status: 1,                   // 状态：0-暂停营业，1-营业中，2-繁忙
  distance: 1200,              // 距离（米）
  city: '上海',                // 城市
  enabled: true,               // 是否启用
  replaceCarCtn: 15,           // 可换车辆数量
  lowPrice: 0.8,               // 低谷价格
  normalPrice: 1.2,            // 正常价格
  peakPrice: 1.8,              // 高峰价格
  nowPrice: 1.2                // 当前价格
}
```

## 后续优化建议

1. **自定义标记内容**: 利用新API创建更丰富的标记显示
2. **性能监控**: 监控新API的性能表现
3. **用户体验**: 考虑添加动画效果和交互反馈
4. **图标系统**: 建立统一的图标管理系统
5. **示例数据管理**: 建立示例数据的配置化管理

## 相关链接

- [Google Maps API 弃用说明](https://developers.google.com/maps/deprecations)
- [高级标记迁移指南](https://developers.google.com/maps/documentation/javascript/advanced-markers/migration)
- [AdvancedMarkerElement 文档](https://developers.google.com/maps/documentation/javascript/advanced-markers) 