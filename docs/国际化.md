国际化
===

友情提示：

该章节，基于 [《vue element plus admin —— 国际化》 (opens new window)](https://element-plus-admin-doc.cn/dep/i18n.html)的内容修改。

如果你使用的 vscode 开发工具，则推荐安装 [I18n-ally (opens new window)](https://marketplace.visualstudio.com/items?itemName=Lokalise.i18n-ally)这个插件

[#](https://cloud.iocoder.cn/vue3/i18n/#_1-i18n-ally-%E6%8F%92%E4%BB%B6)1\. I18n-ally 插件
----------------------------------------------------------------------------------------

安装了该插件后，你的代码内可以实时看到对应的语言内容

![](https://element-plus-admin-doc.cn/images/i18n.png)

[#](https://cloud.iocoder.cn/vue3/i18n/#_2-%E9%85%8D%E7%BD%AE%E9%BB%98%E8%AE%A4%E8%AF%AD%E8%A8%80)2\. 配置默认语言
------------------------------------------------------------------------------------------------------------

在 [src/store/modules/locale.ts (opens new window)](https://github.com/yudaocode/yudao-ui-admin-vue3/blob/master/src/store/modules/locale.ts)内配置 `currentLocale` 为其他语言。

查看代码

[#](https://cloud.iocoder.cn/vue3/i18n/#_3-%E8%AF%AD%E8%A8%80%E6%96%87%E4%BB%B6)3\. 语言文件
----------------------------------------------------------------------------------------

在 [src/locales (opens new window)](https://github.com/yudaocode/yudao-ui-admin-vue3/blob/master/src/locales/)可以配置具体的语言。

目前项目中的语言都是没有拆分的，全部放一起，后续会考虑拆分出来，比较好维护。

[#](https://cloud.iocoder.cn/vue3/i18n/#_4-%E8%AF%AD%E8%A8%80%E5%AF%BC%E5%85%A5%E9%80%BB%E8%BE%91%E8%AF%B4%E6%98%8E)4\. 语言导入逻辑说明
--------------------------------------------------------------------------------------------------------------------------------

在 [src/plugins/vueI18n/index.ts (opens new window)](https://github.com/yudaocode/yudao-ui-admin-vue3/blob/master/src/plugins/vueI18n/index.ts#L13)内可以看到

    const defaultLocal = await import(`../../locales/${locale.lang}.ts`)


这会导入 `src/locales` 文件语言包。

[#](https://cloud.iocoder.cn/vue3/i18n/#_5-%E4%BD%BF%E7%94%A8)5\. 使用
--------------------------------------------------------------------

引入项目自带的 `useI18n`

**注意不要引入 vue-i18n 的 useI18n**

    import { useI18n } from '/@/hooks/web/useI18n'
    
    const { t } = useI18n()
    
    const title = t('common.menu')


[#](https://cloud.iocoder.cn/vue3/i18n/#_6-%E5%88%87%E6%8D%A2%E8%AF%AD%E8%A8%80)6\. 切换语言
----------------------------------------------------------------------------------------

切换语言需要使用 [src/hooks/web/useLocale.ts(opens new window)](https://github.com/yudaocode/yudao-ui-admin-vue3/blob/master/src/hooks/web/useLocale.ts#L19-L35)

    import { useLocale } from '@/hooks/web/useLocale'
    const { changeLocale } = useLocale()
    
    changeLocale('en')


[#](https://cloud.iocoder.cn/vue3/i18n/#_7-%E6%96%B0%E5%A2%9E%E6%96%B0%E8%AF%AD%E8%A8%80)7\. 新增新语言
--------------------------------------------------------------------------------------------------

### [#](https://cloud.iocoder.cn/vue3/i18n/#_7-1-%E8%AF%AD%E8%A8%80%E6%96%87%E4%BB%B6)7.1 语言文件

在 [src/locales (opens new window)](https://github.com/yudaocode/yudao-ui-admin-vue3/blob/master/src/locales/)增加对应语言的文件即可

### [#](https://cloud.iocoder.cn/vue3/i18n/#_7-2-%E6%96%B0%E5%A2%9E%E8%AF%AD%E8%A8%80)7.2 新增语言

目前项目自带的语言只有 `zh_CN` 和 `en` 两种

如果需要新增，按以下操作即可

1.  在 [src/locales (opens new window)](https://github.com/yudaocode/yudao-ui-admin-vue3/blob/master/src/locales/)下语言文件
2.  在 [types/global.d.ts (opens new window)](https://github.com/yudaocode/yudao-ui-admin-vue3/blob/master/types/global.d.ts#L15)给 `LocaleType` 添加对应的类型
3.  在 [src/store/modules/locale.ts (opens new window)](https://github.com/yudaocode/yudao-ui-admin-vue3/blob/master/src/store/modules/locale.ts#L26-L38)`localeMap` 中添加对应语言

[#](https://cloud.iocoder.cn/vue3/i18n/#_8-%E8%BF%9C%E7%A8%8B%E8%AF%BB%E5%8F%96%E8%AF%AD%E8%A8%80%E6%95%B0%E6%8D%AE)8\. 远程读取语言数据
--------------------------------------------------------------------------------------------------------------------------------

目前项目会在 `src/main.ts` 内等待 `setupI18n` 这个函数执行完之后才会渲染界面，所以只需在 setupI18n 内的 `createI18nOptions` 发送 ajax 请求，将对应的数据设置到 i18n 实例上即可。

    const createI18nOptions = async (): Promise<I18nOptions> => {
      const localeStore = useLocaleStoreWithOut()
      const locale = localeStore.getCurrentLocale
      const localeMap = localeStore.getLocaleMap
      // 这里改为远程请求即可。
      const defaultLocal = await import(`../../locales/${locale.lang}.ts`)
      const message = defaultLocal.default ?? {}
    
      setHtmlPageLang(locale.lang)
    
      localeStore.setCurrentLocale({
        lang: locale.lang
        // elLocale: elLocal
      })
    
      return {
        legacy: false,
        locale: locale.lang,
        fallbackLocale: locale.lang,
        messages: {
          [locale.lang]: message
        },
        availableLocales: localeMap.map((v) => v.lang),
        sync: true,
        silentTranslationWarn: true,
        missingWarn: false,
        silentFallbackWarn: true
      }
    }


### [#](https://cloud.iocoder.cn/vue3/i18n/#_8-1-uselocale)8.1 useLocale

代码: [src/hooks/web/useLocale.ts(opens new window)](https://github.com/yudaocode/yudao-ui-admin-vue3/blob/master/src/hooks/web/useLocale.ts#L19-L35)

当手动切换语言的时候会触发 `useLocale` 函数，useLocale 也是异步函数，只需等待接口返回响应的数据后，再进行设置即可

    export const useLocale = () => {
      // Switching the language will change the locale of useI18n
      // And submit to configuration modification
      const changeLocale = async (locale: LocaleType) => {
        const globalI18n = i18n.global
        
        // 改为远程获取
        const langModule = await import(`../../locales/${locale}.ts`)
    
        globalI18n.setLocaleMessage(locale, langModule.default)
    
        setI18nLanguage(locale)
      }
    
      return {
        changeLocale
      }
    }