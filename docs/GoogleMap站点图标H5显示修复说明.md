# GoogleMap 站点图标 H5 显示修复说明

## 问题描述

在 GoogleMap 组件中，通过管理后台上传的站点图标在 H5 端（uni-app）显示不正确，主要问题包括：

1. **图标不显示**：上传的图标在 H5 端无法正常加载
2. **URL 格式问题**：使用本地 blob URL 导致跨域和持久化问题
3. **兼容性问题**：不同平台对图片 URL 的处理方式不同

## 问题原因

### 1. 本地 URL 问题

原来的实现使用 `URL.createObjectURL()` 创建本地 blob URL：

```javascript
// 问题代码
const fileUrl = URL.createObjectURL(file)
formData.value.stationIcons[type] = fileUrl
```

这种方式存在以下问题：
- **跨域限制**：blob URL 在不同域之间无法访问
- **持久化问题**：页面刷新后 blob URL 失效
- **内存泄漏**：需要手动释放 blob URL

### 2. 文件上传方式不当

没有使用系统标准的文件上传 API，而是直接创建本地 URL。

## 修复方案

### 1. 使用系统文件上传 API

修改 `property.vue` 中的图标上传逻辑：

```typescript
// 修复后的代码
const handleIconUpload = async (file: File, type: 'online' | 'offline' | 'busy') => {
  try {
    // 检查文件类型和大小
    if (!file.type.startsWith('image/')) {
      ElMessage.error('请上传图片文件')
      return false
    }
    
    if (file.size > 2 * 1024 * 1024) {
      ElMessage.error('图片大小不能超过2MB')
      return false
    }
    
    // 使用系统文件上传API
    const res = await FileApi.updateFile({ 
      file: file, 
      directory: 'google-map-station-icons' 
    })
    
    if (res.code === 0) {
      // 更新对应的图标URL
      formData.value.stationIcons[type] = res.data
      ElMessage.success('图标上传成功')
    } else {
      ElMessage.error('图标上传失败：' + res.msg)
    }
    
    return false
  } catch (error) {
    console.error('图标上传失败:', error)
    ElMessage.error('图标上传失败，请重试')
    return false
  }
}
```

### 2. 添加图标 URL 验证

在 `index.vue` 和 `s-google-map.vue` 中添加图标 URL 验证逻辑：

```typescript
// 获取有效的图标URL
const getValidIconUrl = (customIconUrl: string | undefined, statusType: 'online' | 'offline' | 'busy') => {
  // 如果自定义图标URL存在且有效，使用自定义图标
  if (customIconUrl && isValidImageUrl(customIconUrl)) {
    return customIconUrl
  }
  
  // 否则使用默认图标
  const defaultIcons = {
    online: 'https://maps.google.com/mapfiles/ms/icons/green-dot.png',
    offline: 'https://maps.google.com/mapfiles/ms/icons/red-dot.png',
    busy: 'https://maps.google.com/mapfiles/ms/icons/yellow-dot.png'
  }
  
  return defaultIcons[statusType] || defaultIcons.online
}

// 验证图片URL是否有效
const isValidImageUrl = (url: string) => {
  if (!url || typeof url !== 'string') {
    return false
  }
  
  // 检查是否是有效的URL格式
  try {
    new URL(url)
  } catch {
    return false
  }
  
  // 检查是否是图片文件
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg']
  const lowerUrl = url.toLowerCase()
  return imageExtensions.some(ext => lowerUrl.includes(ext)) || 
         lowerUrl.includes('data:image/') ||
         lowerUrl.includes('blob:')
}
```

### 3. 统一图标处理逻辑

确保管理后台和 H5 端使用相同的图标处理逻辑：

```typescript
// 根据站点状态选择对应的图标
let iconUrl = getValidIconUrl(props.data.stationIcons?.online, 'online')
if (station.status === 0) {
  iconUrl = getValidIconUrl(props.data.stationIcons?.offline, 'offline')
} else if (station.status === 2) {
  iconUrl = getValidIconUrl(props.data.stationIcons?.busy, 'busy')
}
```

## 修复效果

### 1. 文件上传改进

- ✅ **使用标准 API**：通过系统文件上传 API 上传图标
- ✅ **持久化存储**：图标文件存储在服务器，URL 持久有效
- ✅ **跨域支持**：服务器 URL 支持跨域访问
- ✅ **错误处理**：完善的错误处理和用户提示

### 2. 图标显示改进

- ✅ **H5 端正常显示**：上传的图标在 H5 端正常显示
- ✅ **URL 验证**：自动验证图标 URL 的有效性
- ✅ **降级处理**：无效 URL 时自动使用默认图标
- ✅ **多平台兼容**：支持不同平台的图片格式

### 3. 用户体验改进

- ✅ **实时预览**：上传后立即显示图标预览
- ✅ **状态反馈**：清晰的上传成功/失败提示
- ✅ **文件验证**：文件类型和大小验证
- ✅ **错误恢复**：上传失败时自动恢复默认图标

## 技术实现

### 1. 文件上传流程

```
用户选择图片 → 文件验证 → 调用 FileApi.updateFile() → 获取服务器URL → 更新配置
```

### 2. 图标加载流程

```
获取配置 → 验证图标URL → 有效则使用自定义图标 → 无效则使用默认图标 → 创建地图标记
```

### 3. 错误处理机制

- **文件类型验证**：只允许图片文件
- **文件大小限制**：限制为 2MB
- **URL 格式验证**：确保 URL 格式正确
- **网络错误处理**：上传失败时显示错误信息

## 测试验证

### 测试步骤

1. **管理后台测试**
   - 进入装修编辑器
   - 添加 GoogleMap 组件
   - 启用"显示换电站"
   - 上传不同状态的图标
   - 验证图标预览正常

2. **H5 端测试**
   - 访问 H5 页面
   - 查看地图上的站点图标
   - 验证自定义图标正常显示
   - 测试不同状态图标的切换

3. **兼容性测试**
   - 测试不同图片格式（PNG、JPG、GIF）
   - 测试不同文件大小
   - 测试网络异常情况

### 预期结果

- 管理后台上传图标成功，预览正常
- H5 端正确显示上传的图标
- 无效图标时自动使用默认图标
- 错误情况有适当的用户提示

## 注意事项

1. **文件目录**：图标文件存储在 `google-map-station-icons` 目录下
2. **文件大小**：单个图标文件限制为 2MB
3. **文件格式**：支持常见的图片格式（PNG、JPG、GIF、WebP、SVG）
4. **URL 持久性**：服务器 URL 持久有效，不会因页面刷新失效
5. **跨域支持**：服务器 URL 支持跨域访问，适用于 H5 端

## 相关文件

- `property.vue`：属性配置面板（文件上传逻辑）
- `index.vue`：管理后台组件（图标显示逻辑）
- `s-google-map.vue`：uni-app 组件（H5 端图标显示）
- `FileApi`：系统文件上传 API 